@echo off
chcp 65001 >nul
echo ========================================
echo Switch to Blowing Agent Plugin
echo ========================================
echo.

cd /d "%~dp0extension"

echo Backing up original files...
if exist manifest.json (
    copy manifest.json manifest-original.json >nul
    echo [OK] Backup manifest.json
)

if exist content.js (
    copy content.js content-original.js >nul
    echo [OK] Backup content.js
)

if exist styles.css (
    copy styles.css styles-original.css >nul
    echo [OK] Backup styles.css
)

echo.
echo Switching to blowing agent files...
copy manifest-blowing.json manifest.json >nul
echo [OK] Switch manifest.json

copy content-blowing.js content.js >nul
echo [OK] Switch content.js

copy styles-blowing.css styles.css >nul
echo [OK] Switch styles.css

echo.
echo ========================================
echo Switch Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Open Chrome browser
echo 2. Go to chrome://extensions/
echo 3. Find the plugin and click "Reload" button
echo 4. Start backend server:
echo    conda activate AISteelMaking
echo    python -m uvicorn src.backend.main:app --host 0.0.0.0 --port 9001 --reload
echo.
pause
