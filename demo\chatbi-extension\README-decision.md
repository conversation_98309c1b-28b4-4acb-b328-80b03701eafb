# 智能炼钢决策助手浏览器插件

## 概述

本插件是为钢铁大模型经营决策智能体专门定制的浏览器扩展，可以在任何网页中快速访问智能问数功能。

## 主要变更

### 1. 连接配置
- WebSocket地址已更新为：`ws://localhost:9000/ws/agent/decision`
- 与经营决策智能体直接通信

### 2. 消息格式适配
- 发送消息格式：
  ```json
  {
    "type": "query",
    "content": "查询内容",
    "context": {}
  }
  ```
- 支持的消息类型：
  - `connected`: 连接成功
  - `processing`: 处理中
  - `stream_start`: 流式响应开始
  - `stream`: 流式内容
  - `stream_end`: 流式响应结束
  - `info`: 信息提示
  - `history`: 历史记录
  - `pong`: 心跳响应
  - `error`: 错误信息

### 3. 功能调整
- 移除了语音识别和语音播报功能
- 添加了命令按钮：
  - 查看历史
  - 清空历史
  - 心跳测试
- 更新了推荐问题为钢铁行业相关内容

### 4. 界面优化
- 标题更新为"智能炼钢决策助手"
- 优化了样式和交互体验

## 安装方法

1. 打开Chrome浏览器，进入扩展管理页面（chrome://extensions/）
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择`extension`文件夹
5. 使用`manifest-decision.json`替换原有的`manifest.json`
6. 使用`content-decision.js`替换原有的`content.js`
7. 使用`styles-decision.css`替换原有的`styles.css`

## 使用方法

1. 确保后端服务运行在 `localhost:9000`
2. 在任意网页点击右下角的浮动图标打开聊天窗口
3. 输入问题或点击推荐问题进行查询
4. 使用命令按钮执行特殊操作

## 推荐问题示例

- 查询今天的产量数据
- 分析上周Q235钢种的质量趋势
- 昨天的成本指标是多少？
- 查询本月的生产计划完成情况

## 注意事项

1. 插件需要后端服务正常运行才能使用
2. WebSocket连接断开后会自动重连
3. 支持表格和图表的渲染显示