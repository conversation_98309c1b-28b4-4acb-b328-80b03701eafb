# 钢铁智能体系统 (Steel LLM)

## 项目概述

本项目是由山东九羊钢铁集团、山东浪潮智能生产和北京科技大学联合开发的炼钢智能体系统。系统采用"云-边-端"三层协同架构，通过多模态工业大模型实现转炉炼钢过程的智能化控制和优化。

## 系统架构

- 钢铁大模型服务、智能体集群、数据集管理

## 核心功能

1. **极数吹炼智能体**：实时炉况感知与操作导航
2. **极目观火智能体**：多模态数据融合与炉况分析
3. **智能问数智能体**：生产指标分析与决策支持

## 技术栈

- **后端**: Python 3.10, FastAPI
- **大模型**: xinference[vllm]
- **数据库**: PostgreSQL + TimescaleDB, Redis, InfluxDB
- **前端**: Vue 3 + TypeScript
- **部署**: Docker + Kubernetes

## 快速开始

### 环境要求

- Python 3.10+
- Docker 20.10+
- CUDA 11.8+ (GPU推理)

### 安装依赖

#### 1.安装poetry（如果没有安装）
1. 安装pipx（windows或者linux）
```bash
python -m pip install --user pipx
python -m pipx ensurepath
```
2. 使用pipx安装poetry
```bash
pipx install poetry
```
3. 重启终端
#### 2.安装依赖
1. 首先创建conda环境，`mamba create -n AISteelMaking python=3.12`
2. poetry初始化`poetry init`，即生成pyproject.toml文件
3. 从toml文件中安装poetry包`poetry install`
4. 添加某个包`poetry add numpy`

#### 注意
使用poetry后，就不要再使用pip进行安装了

### 启动服务
在根目录终端运行`uvicorn src.backend.main:app --host 0.0.0.0 --port 9001 --reload`

## 项目结构


## 开发指南

### 代码规范

- 使用 Black 进行代码格式化
- 使用 MyPy 进行类型检查
- 遵循 PEP 8 编码规范

### 提交规范

- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 许可证

本项目为专有软件，版权归属三方合作单位共同所有。