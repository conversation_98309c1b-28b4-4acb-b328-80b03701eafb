/* 悬浮按钮 */
.steel-helper-btn {
  position: fixed;
  right: 20px;
  bottom: 240px;
  width: 50px;
  height: 50px;
  background: #409eff;
  color: #fff;
  border-radius: 50%;
  text-align: center;
  line-height: 50px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  z-index: 9999;
  transition: all 0.3s;
}
.steel-helper-btn:hover {
  background: #66b1ff;
}

/* 助手面板 */
.steel-helper-panel {
  position: fixed;
  right: 0;
  top: 100px;
  width: 450px;
  height: calc(100% - 120px);
  background: #fff;
  box-shadow: -2px 0 12px rgba(0,0,0,0.1);
  transform: translateX(100%);
  transition: transform 0.3s;
  display: flex;
  flex-direction: column;
  z-index: 9998;
}
.steel-helper-panel.active {
  transform: translateX(0);
}

/* 面板头部 */
.panel-header {
  padding: 12px 16px;
  background: #409eff;
  color: #fff;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.close-btn {
  cursor: pointer;
  font-size: 20px;
}

/* 消息区域 */
.panel-content {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
}
.message-container {
  margin: 8px 0;
  max-width: 80%;
  padding: 10px 14px;
  border-radius: 6px;
  line-height: 1.6;
}
/* 用户消息 */
.message-container.user {
  background: #e6f3ff;
  margin-left: auto;
}
/* 助手消息 */
.message-container.assistant {
  background: #f5f7fa;
}
.message-text {
  word-wrap: break-word;
}

/* 输入区域 */
.input-area {
  display: flex;
  padding: 12px;
  gap: 8px;
}
#message-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #dcdcdc;
  border-radius: 4px;
  outline: none;
}
#send-btn {
  padding: 8px 16px;
  background: #409eff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
#send-btn:hover {
  background: #66b1ff;
}

/* 预设问题样式 */
.preset-questions {
  margin-top: 10px;
}
.preset-question {
  padding: 8px 12px;
  background: #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
}
.preset-question:hover {
  background: #e0e0e0;
}