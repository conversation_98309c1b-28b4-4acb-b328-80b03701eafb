import pytest
import asyncio
import json
import sys
import os
from fastapi.testclient import TestClient

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.backend.main import app
from src.backend.services import ConnectionManager
from src.agents.decision_agent import DecisionAgent


class TestDecisionAgent:
    """测试经营决策智能体功能"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        # 确保app有必要的状态
        if not hasattr(app.state, 'connection_manager'):
            app.state.connection_manager = ConnectionManager()
        return TestClient(app)
    
    @pytest.mark.asyncio
    async def test_decision_agent_initialization(self):
        """测试决策智能体初始化"""
        agent = DecisionAgent()
        await agent.initialize()
        assert agent.initialized is True
        assert agent.engine is not None
    
    @pytest.mark.asyncio
    async def test_knowledge_base_query(self):
        """测试知识库查询"""
        agent = DecisionAgent()
        await agent.initialize()
        
        # 测试知识库查询
        query = "如何降低经营成本？"
        result = await agent.process(query, {})
        
        assert result is not None
        assert "成本" in result
        print(f"知识库查询结果: {result[:200]}...")
    
    @pytest.mark.asyncio 
    async def test_database_query(self):
        """测试数据库查询功能"""
        agent = DecisionAgent()
        await agent.initialize()
        
        # 测试指标查询
        query = "查询昨天的钢坯成本"
        result = await agent.process(query, {})
        
        print(f"数据库查询结果: {result}")
        # 注意：实际测试需要有测试数据库
    
    @pytest.mark.asyncio
    async def test_history_management(self):
        """测试历史管理功能"""
        agent = DecisionAgent()
        await agent.initialize()
        
        # 添加一些查询历史
        await agent.process("测试查询1", {})
        await agent.process("测试查询2", {})
        
        # 获取历史
        history = agent.get_history(limit=5)
        assert "测试查询1" in history
        assert "测试查询2" in history
        
        # 清空历史
        result = agent.clear_history()
        assert "清空" in result
    
    def test_websocket_endpoint(self, client):
        """测试WebSocket端点"""
        with client.websocket_connect("/ws/agent/decision") as websocket:
            # 接收连接消息
            data = websocket.receive_json()
            assert data["type"] == "connected"
            assert data["agent"] == "decision"
            assert "智能问数" in data["message"]
            
            # 发送查询
            websocket.send_json({
                "type": "query",
                "content": "什么是钢铁料消耗？"
            })
            
            # 接收处理中消息
            processing = websocket.receive_json()
            assert processing["type"] == "processing"
            
            # 接收流式响应
            received_content = ""
            while True:
                response = websocket.receive_json()
                if response["type"] == "stream":
                    received_content += response.get("content", "")
                    if response.get("is_finished", False):
                        break
            
            assert len(received_content) > 0
            print(f"WebSocket响应: {received_content[:200]}...")
    
    def test_special_commands(self, client):
        """测试特殊命令"""
        with client.websocket_connect("/ws/agent/decision") as websocket:
            # 跳过连接消息
            websocket.receive_json()
            
            # 测试查看历史命令
            websocket.send_text("查看历史")
            response = websocket.receive_json()
            assert response["type"] == "history"
            
            # 测试清空历史命令
            websocket.send_text("清空历史")
            response = websocket.receive_json()
            assert response["type"] == "info"
            assert "清空" in response["message"]


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-s", "-k", "test_knowledge_base_query"])