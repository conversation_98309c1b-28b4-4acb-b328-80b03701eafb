# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

待重构代码
ref/

# Virtual Environment
venv/
ENV/
env/
.venv

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.project
.pydevproject

# Testing
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.env
.env.local
.env.*.local

settings.local.json

# Logs
logs/
*.log

# Database
*.db
*.sqlite3

# Models
models/*.pt
models/*.onnx
models/*.pth
*.pkl

# Data
data/
*.csv
*.parquet

# OS
.DS_Store
Thumbs.db

# Build
dist/
build/
*.egg-info/

# Node (for frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production
*.pid
*.seed
*.pid.lock

# Docker
.dockerignore

# Temporary
tmp/
temp/
*.tmp
*.temp

# Secrets
*.key
*.pem
secrets/