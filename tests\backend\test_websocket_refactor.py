import pytest
import asyncio
import json
from fastapi.testclient import TestClient
from websockets.sync.client import connect as websocket_connect
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.backend.main import app
from config.logging import logger


class TestWebSocketRefactor:
    """测试重构后的WebSocket接口"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    def test_health_check(self, client):
        """测试健康检查接口"""
        response = client.get("/health/")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"
    
    def test_management_api(self, client):
        """测试管理API"""
        # 测试获取连接数
        response = client.get("/api/connections")
        assert response.status_code == 200
        assert "count" in response.json()
        assert "active_connections" in response.json()
    
    def test_websocket_endpoints_exist(self, client):
        """测试WebSocket端点是否存在"""
        # 获取所有路由
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(route.path)
        
        # 检查WebSocket端点
        expected_endpoints = [
            "/ws/agent",
            "/ws/agent/blowing",
            "/ws/agent/vision",
            "/ws/agent/analytics",
            "/ws/agent/decision",
            "/ws/agent/legacy"
        ]
        
        for endpoint in expected_endpoints:
            assert endpoint in routes, f"WebSocket endpoint {endpoint} not found"
    
    @pytest.mark.asyncio
    async def test_base_websocket_connection(self):
        """测试基础WebSocket连接"""
        with TestClient(app) as client:
            with client.websocket_connect("/ws/agent") as websocket:
                # 接收连接消息
                data = websocket.receive_json()
                assert data["type"] == "connected"
                assert "client_id" in data
                assert "欢迎使用钢铁大模型智能助手" in data["message"]
                
                # 发送ping消息
                websocket.send_json({"type": "ping"})
                
                # 接收pong响应
                response = websocket.receive_json()
                assert response["type"] == "pong"
    
    @pytest.mark.asyncio
    async def test_blowing_websocket(self):
        """测试吹炼控制智能体WebSocket"""
        with TestClient(app) as client:
            with client.websocket_connect("/ws/agent/blowing") as websocket:
                # 接收连接消息
                data = websocket.receive_json()
                assert data["type"] == "connected"
                assert data["agent"] == "blowing"
                assert "吹炼控制智能体" in data["message"]
                
                # 发送查询消息
                websocket.send_json({
                    "type": "query",
                    "content": "当前炉况如何？"
                })
                
                # 接收处理中消息
                processing = websocket.receive_json()
                assert processing["type"] == "processing"
                assert processing["agent"] == "blowing"
    
    @pytest.mark.asyncio
    async def test_vision_websocket(self):
        """测试视觉智能体WebSocket"""
        with TestClient(app) as client:
            with client.websocket_connect("/ws/agent/vision") as websocket:
                # 接收连接消息
                data = websocket.receive_json()
                assert data["type"] == "connected"
                assert data["agent"] == "vision"
                assert "视觉分析智能体" in data["message"]
    
    @pytest.mark.asyncio
    async def test_analytics_websocket(self):
        """测试数据分析智能体WebSocket"""
        with TestClient(app) as client:
            with client.websocket_connect("/ws/agent/analytics") as websocket:
                # 接收连接消息
                data = websocket.receive_json()
                assert data["type"] == "connected"
                assert data["agent"] == "analytics"
                assert "数据分析智能体" in data["message"]
    
    @pytest.mark.asyncio
    async def test_decision_websocket(self):
        """测试决策智能体WebSocket"""
        with TestClient(app) as client:
            with client.websocket_connect("/ws/agent/decision") as websocket:
                # 接收连接消息
                data = websocket.receive_json()
                assert data["type"] == "connected"
                assert data["agent"] == "decision"
                assert "决策智能体" in data["message"]
    
    @pytest.mark.asyncio
    async def test_legacy_websocket(self):
        """测试兼容性WebSocket"""
        with TestClient(app) as client:
            with client.websocket_connect("/ws/agent/legacy") as websocket:
                # 发送问题
                websocket.send_text("你好")
                
                # 接收响应
                response_parts = []
                while True:
                    data = websocket.receive_text()
                    if data == "[DONE]":
                        break
                    response_parts.append(data)
                
                # 检查是否有响应
                assert len(response_parts) > 0


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-s"])