import time
from typing import List, Dict, Optional


class HistoryManager:
    def __init__(self, max_history_rounds: int = 2):
        self.full_history: List[Dict] = []  # 完整历史记录
        self.dialogue_history: List[Dict] = []  # 精简对话历史
        self.max_history_rounds = max_history_rounds
    
    def add_record(
        self,
        user_query: str,
        question_type: str,
        result: str,
        sql: Optional[str] = None
    ):
        """添加完整历史记录"""
        record = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "user_query": user_query,
            "question_type": question_type,
            "sql": sql,
            "result": result
        }
        self.full_history.append(record)
    
    def add_dialogue(self, user_query: str, system_response: str):
        """添加精简对话历史"""
        self.dialogue_history.append({
            "user": user_query,
            "system": system_response
        })
        if len(self.dialogue_history) > self.max_history_rounds:
            self.dialogue_history.pop(0)
    
    def get_context_prompt(self, new_query: str) -> str:
        """构建包含历史对话的上下文prompt"""
        if not self.dialogue_history:
            return new_query
        
        history_str = "\n".join([
            f"用户：{item['user']}\n系统：{item['system']}"
            for item in self.dialogue_history
        ])
        return f"""以下是历史对话内容：{history_str}现在用户的新问题是：{new_query}请结合历史对话内容，理解用户意图并回答新问题。"""
    
    def get_full_history(self, limit: int = 10) -> List[Dict]:
        """获取完整历史记录"""
        return self.full_history[-limit:]
    
    def format_full_history(self, limit: int = 10) -> str:
        """格式化完整历史记录"""
        records = self.get_full_history(limit)
        if not records:
            return "暂无历史记录"
        
        formatted = []
        for i, record in enumerate(records, 1):
            item = [
                f"[{i}] 时间：{record['timestamp']}",
                f"问题：{record['user_query']}",
                f"类型：{record['question_type']}"
            ]
            if record["question_type"] == "适合数据库查询" and record["sql"]:
                item.append(f"SQL：{record['sql'][:80]}...")
            item.append(f"结果：{record['result'][:100]}...")
            formatted.append("\n".join(item) + "\n" + "-" * 60)
        
        return "\n".join(formatted)
    
    def clear_all(self):
        """清空所有历史记录"""
        self.full_history = []
        self.dialogue_history = []
        return "所有历史记录已清空"