"""
调试版WebSocket客户端
显示所有原始消息，帮助定位问题
"""

import asyncio
import websockets
import json
from datetime import datetime


async def debug_client():
    """调试客户端"""
    url = "ws://localhost:8001/ws/agent"
    
    print(f"[{datetime.now().strftime('%H:%M:%S')}] 正在连接到 {url}...")
    
    async with websockets.connect(url) as websocket:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] 连接成功！")
        
        # 创建一个任务来接收消息
        async def receive_loop():
            message_count = 0
            while True:
                try:
                    raw_message = await websocket.recv()
                    message_count += 1
                    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 收到第 {message_count} 条消息:")
                    print(f"原始消息: {raw_message[:100]}..." if len(raw_message) > 100 else f"原始消息: {raw_message}")
                    
                    # 尝试解析JSON
                    try:
                        data = json.loads(raw_message)
                        print(f"消息类型: {data.get('type')}")
                        
                        # 根据类型显示内容
                        if data.get('type') == 'connected':
                            print(f"欢迎消息: {data.get('message')}")
                        elif data.get('type') == 'content':
                            print(f"内容片段: '{data.get('content')}'", end='')
                        elif data.get('type') == 'status':
                            print(f"状态: {data.get('content')}")
                        elif data.get('type') == 'intent':
                            print(f"意图: {data.get('intent')} (置信度: {data.get('confidence')})")
                        elif data.get('type') == 'done':
                            print(f"完成，会话ID: {data.get('session_id')}")
                            
                    except json.JSONDecodeError:
                        print("警告: 无法解析为JSON")
                        
                except websockets.exceptions.ConnectionClosed:
                    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 连接已关闭")
                    break
                except Exception as e:
                    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 接收错误: {type(e).__name__}: {e}")
                    break
        
        # 启动接收循环
        receive_task = asyncio.create_task(receive_loop())
        
        # 等待一秒确保收到欢迎消息
        await asyncio.sleep(1)
        
        # 发送测试查询
        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 发送查询: '你好'")
        query_message = {
            "type": "query",
            "content": "你好",
            "context": {}
        }
        await websocket.send(json.dumps(query_message))
        
        # 等待10秒接收所有响应
        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 等待响应...")
        await asyncio.sleep(10)
        
        # 取消接收任务
        receive_task.cancel()
        
    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 测试完成")


if __name__ == "__main__":
    print("=== WebSocket 调试客户端 ===")
    print("此客户端将显示所有收到的原始消息\n")
    
    try:
        asyncio.run(debug_client())
    except KeyboardInterrupt:
        print("\n测试中断")