import os
import sys
from loguru import logger
from dotenv import load_dotenv # 1. 导入 load_dotenv

load_dotenv()
log_level = os.getenv("LOG_LEVEL", "INFO")

# --- 后续的 loguru 配置保持不变 ---

# 移除默认配置
logger.remove()

# 配置终端输出
logger.add(sys.stderr, level=log_level, colorize=True,
           format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")

# 配置文件输出
log_path = "logs/"
logger.add(f"{log_path}app.log", rotation="10 MB", retention="10 days", level="INFO", encoding='utf-8')
logger.add(f"{log_path}error.log", rotation="10 MB", retention="10 days", level="ERROR", encoding='utf-8')

# 导出 logger，方便其他模块使用
__all__ = ["logger"]