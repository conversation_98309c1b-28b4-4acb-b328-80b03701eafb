# 语音识别服务配置指南

## 概述
本项目集成了科大讯飞语音识别服务，支持实时语音转文本功能。

## 配置步骤

### 1. 获取科大讯飞API密钥

1. 访问[科大讯飞开放平台](https://www.xfyun.cn/)
2. 注册并登录账号
3. 创建应用，选择"语音听写"服务
4. 获取以下信息：
   - APP ID
   - API Key
   - API Secret

### 2. 配置环境变量

在项目根目录创建 `.env` 文件（如果不存在），添加以下配置：

```env
# 科大讯飞语音识别
XFYUN_APP_ID=你的APP_ID
XFYUN_API_KEY=你的API_KEY
XFYUN_API_SECRET=你的API_SECRET
```

### 3. 安装依赖

确保已安装语音识别所需的依赖包：

```bash
pip install websockets
```

## 使用说明

### 后端服务

语音识别服务通过WebSocket端点提供：
- 端点地址：`ws://localhost:9000/ws/speech`
- 支持的操作：
  - `start`: 开始录音
  - `stop`: 停止录音
  - 音频数据流传输

### 浏览器插件

浏览器插件已集成语音识别功能：
1. 点击麦克风图标开始录音
2. 说话内容会实时转换为文本
3. 再次点击停止录音

### API调用示例

```javascript
// 连接到语音识别服务
const ws = new WebSocket('ws://localhost:9000/ws/speech');

// 开始录音
ws.send(JSON.stringify({ action: 'start' }));

// 发送音频数据（16kHz, 16位PCM格式）
ws.send(audioData);

// 停止录音
ws.send(JSON.stringify({ action: 'stop' }));

// 接收识别结果
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.type === 'recognition') {
    // 实时识别结果
    console.log(data.data);
  } else if (data.type === 'final_result') {
    // 最终识别结果
    console.log(data.text);
  }
};
```

## 音频格式要求

- 采样率：16000 Hz
- 位深度：16位
- 声道数：单声道
- 编码格式：PCM

## 注意事项

1. 确保麦克风权限已授予
2. 网络连接稳定
3. 音频数据需要降采样到16kHz
4. 每次会话最长支持60秒录音

## 故障排除

### 无法连接到语音识别服务
- 检查科大讯飞API密钥是否正确配置
- 确认网络可以访问科大讯飞API服务器
- 查看后端日志排查具体错误

### 识别效果不佳
- 确保麦克风质量良好
- 减少背景噪音
- 说话清晰，语速适中

### 浏览器兼容性
- 推荐使用Chrome或Edge浏览器
- 需要浏览器支持WebRTC和WebSocket