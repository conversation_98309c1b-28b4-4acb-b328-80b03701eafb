const fs = require('fs');
const { createCanvas } = require('canvas');

function generateIcon(size) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // Background
    ctx.fillStyle = '#5c6bc0';
    ctx.fillRect(0, 0, size, size);
    
    // Draw BI text
    ctx.fillStyle = 'white';
    ctx.font = `bold ${size * 0.4}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('BI', size / 2, size / 2);
    
    return canvas.toBuffer('image/png');
}

// Generate icons
const sizes = [16, 48, 128];
sizes.forEach(size => {
    const buffer = generateIcon(size);
    fs.writeFileSync(`icon-${size}.png`, buffer);
    console.log(`Generated icon-${size}.png`);
});