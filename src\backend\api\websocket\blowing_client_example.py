#!/usr/bin/env python3
"""
吹炼智能体 WebSocket 客户端示例
演示如何使用新的 WebSocket 接口进行阶段播报和实时监控
"""

import asyncio
import json
import websockets
from datetime import datetime


class BlowingWebSocketClient:
    """吹炼智能体 WebSocket 客户端"""
    
    def __init__(self, uri="ws://localhost:8000/ws/agent/blowing"):
        self.uri = uri
        self.websocket = None
        
    async def connect(self):
        """连接到 WebSocket 服务器"""
        try:
            self.websocket = await websockets.connect(self.uri)
            print(f"✓ 已连接到 {self.uri}")
            return True
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            print("✓ 已断开连接")
    
    async def send_message(self, message):
        """发送消息"""
        if not self.websocket:
            print("❌ 未连接到服务器")
            return None
            
        try:
            await self.websocket.send(json.dumps(message))
            response = await self.websocket.recv()
            return json.loads(response)
        except Exception as e:
            print(f"❌ 发送消息失败: {e}")
            return None
    
    async def start_blowing_process(self, start_time=None):
        """启动吹炼过程（包含实时监控）"""
        message = {
            "type": "start_process"
        }
        if start_time:
            message["start_time"] = start_time
            
        print(f"🚀 启动吹炼过程...")
        response = await self.send_message(message)
        
        if response:
            print(f"响应: {response.get('message')}")
            if response.get('type') == 'process_started':
                print(f"监控开始时间: {response.get('monitoring_start_time')}")
                print(f"监控间隔: {response.get('monitoring_interval')}")
        
        return response
    
    async def stop_blowing_process(self):
        """停止吹炼过程（包含实时监控）"""
        message = {
            "type": "stop_process"
        }
        
        print(f"🛑 停止吹炼过程...")
        response = await self.send_message(message)
        
        if response:
            print(f"响应: {response.get('message')}")
        
        return response
    
    async def stage_broadcast(self, stage_number, stage_name, broadcast_time=None):
        """执行阶段播报"""
        message = {
            "type": "stage_broadcast",
            "stage_info": {
                "stage_number": stage_number,
                "stage_name": stage_name
            }
        }
        if broadcast_time:
            message["broadcast_time"] = broadcast_time
            
        print(f"📢 阶段播报: {stage_name} (阶段 {stage_number})")
        response = await self.send_message(message)
        
        if response and response.get('success'):
            report = response.get('report', {})
            print(f"✓ 播报成功")
            print(f"播报时间: {report.get('timestamp')}")
            print(f"播报类型: {report.get('stage_type')}")
            
            # 显示播报内容预览
            content = report.get('broadcast_content', '')
            if content:
                print(f"播报内容预览: {content[:200]}...")
        else:
            print(f"❌ 播报失败: {response.get('error') if response else '无响应'}")
        
        return response
    
    async def get_status(self):
        """获取当前状态"""
        message = {
            "type": "status"
        }
        
        response = await self.send_message(message)
        
        if response:
            print(f"📊 当前状态:")
            print(f"  监控状态: {'运行中' if response.get('monitoring_active') else '已停止'}")
            current_stage = response.get('current_stage')
            if current_stage:
                print(f"  当前阶段: {current_stage.get('stage_name')} (阶段 {current_stage.get('stage_number')})")
            else:
                print(f"  当前阶段: 无")
        
        return response


async def demo_complete_blowing_process():
    """演示完整的吹炼过程"""
    client = BlowingWebSocketClient()
    
    try:
        # 连接到服务器
        if not await client.connect():
            return
        
        print("\n" + "=" * 60)
        print("吹炼过程演示开始")
        print("=" * 60)
        
        # 1. 启动吹炼过程（包含实时监控）
        await client.start_blowing_process(start_time="2025-08-13 08:00:00")
        await asyncio.sleep(2)
        
        # 2. 第一阶段播报
        await client.stage_broadcast(
            stage_number=1,
            stage_name="准备阶段",
            broadcast_time="2025-08-13 08:00:00"
        )
        await asyncio.sleep(3)
        
        # 3. 第二阶段播报
        await client.stage_broadcast(
            stage_number=2,
            stage_name="脱碳阶段",
            broadcast_time="2025-08-13 08:05:00"
        )
        await asyncio.sleep(3)
        
        # 4. 查看状态
        await client.get_status()
        await asyncio.sleep(3)
        
        # 5. 第三阶段播报
        await client.stage_broadcast(
            stage_number=3,
            stage_name="精炼阶段",
            broadcast_time="2025-08-13 08:10:00"
        )
        await asyncio.sleep(3)
        
        # 6. 结束阶段播报
        await client.stage_broadcast(
            stage_number=4,
            stage_name="吹炼结束",
            broadcast_time="2025-08-13 08:15:00"
        )
        await asyncio.sleep(3)
        
        # 7. 停止吹炼过程
        await client.stop_blowing_process()
        
        print("\n" + "=" * 60)
        print("吹炼过程演示完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.disconnect()


async def demo_simple_usage():
    """演示简单用法"""
    client = BlowingWebSocketClient()
    
    try:
        if not await client.connect():
            return
        
        print("\n" + "=" * 40)
        print("简单用法演示")
        print("=" * 40)
        
        # 启动过程
        await client.start_blowing_process()
        await asyncio.sleep(2)
        
        # 单个阶段播报
        await client.stage_broadcast(1, "测试阶段")
        await asyncio.sleep(2)
        
        # 查看状态
        await client.get_status()
        await asyncio.sleep(2)
        
        # 停止过程
        await client.stop_blowing_process()
        
    except Exception as e:
        print(f"❌ 简单演示中发生错误: {e}")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    print("吹炼智能体 WebSocket 客户端演示")
    print("请确保服务器正在运行在 http://localhost:8000")
    print()
    
    # 选择演示模式
    mode = input("选择演示模式 (1: 完整流程, 2: 简单用法): ").strip()
    
    if mode == "1":
        asyncio.run(demo_complete_blowing_process())
    elif mode == "2":
        asyncio.run(demo_simple_usage())
    else:
        print("无效选择，运行完整流程演示")
        asyncio.run(demo_complete_blowing_process())
