#!/bin/bash

echo "========================================"
echo "经营决策智能体测试脚本"
echo "========================================"
echo ""

echo "请选择运行模式："
echo "1. 交互式测试客户端"
echo "2. 自动化测试套件"
echo "3. 查看测试用例"
echo ""

read -p "请输入选项 (1-3): " choice

case $choice in
    1)
        echo ""
        echo "启动交互式测试客户端..."
        python test_decision_agent.py
        ;;
    2)
        echo ""
        echo "运行自动化测试套件..."
        python run_decision_tests.py
        ;;
    3)
        echo ""
        echo "显示测试用例..."
        python decision_test_cases.py
        ;;
    *)
        echo ""
        echo "无效选项！"
        ;;
esac

echo ""
echo "按任意键继续..."
read -n 1