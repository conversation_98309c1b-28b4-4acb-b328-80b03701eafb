#!/usr/bin/env python3
"""
实时监控功能测试脚本
模拟整个吹炼过程的实时监控
"""

import asyncio
import json
import sys
import os
from datetime import datetime, timedelta

# 确保能够导入项目模块
# sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from agents.blowing_agent.agent import BlowingAgent


async def test_real_time_monitoring_complete_process():
    """测试完整的实时监控过程"""
    print("=" * 60)
    print("实时监控功能完整测试开始")
    print("=" * 60)

    agent = BlowingAgent()

    try:
        # 初始化智能体
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 模拟吹炼开始时间
        start_time = "2025-08-13 08:05:00"
        print(f"\n模拟吹炼开始时间: {start_time}")

        # 启动实时监控
        print("\n启动实时监控...")
        start_result = await agent.real_time_monitoring_function(
            action="start", start_time=start_time)

        if start_result.get("success"):
            print("✓ 实时监控启动成功")
            print(f"监控开始时间: {start_result.get('start_time')}")
            print(f"消息: {start_result.get('message')}")

            # 让监控运行一段时间（90秒，应该产生3次监控报告）
            print("\n监控运行中，将运行90秒（预计产生3次监控报告）...")
            print("监控间隔: 30秒")
            print("注意：监控会分析数据变化趋势，包括参数对比和变化分析")
            print("-" * 40)

            # 等待90秒让监控运行
            await asyncio.sleep(90)

            print("\n90秒监控时间结束")

        else:
            print(f"❌ 实时监控启动失败: {start_result.get('error')}")
            return

        # 停止实时监控
        print("\n停止实时监控...")
        stop_result = await agent.real_time_monitoring_function(action="stop")

        if stop_result.get("success"):
            print("✓ 实时监控停止成功")
            print(f"消息: {stop_result.get('message')}")
        else:
            print(f"❌ 实时监控停止失败: {stop_result.get('error')}")

        print("\n" + "=" * 60)
        print("实时监控功能完整测试完成")
        print("=" * 60)

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.cleanup()


async def test_real_time_monitoring_with_current_time():
    """测试使用当前时间的实时监控"""
    print("\n" + "=" * 60)
    print("当前时间实时监控测试")
    print("=" * 60)

    agent = BlowingAgent()

    try:
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 测试不传入时间参数（使用当前时间）
        print("\n测试使用当前时间启动监控...")

        start_result = await agent.real_time_monitoring_function(action="start"
                                                                 )
        # 不传入start_time参数，应该使用当前时间

        if start_result.get("success"):
            print("✓ 当前时间监控启动成功")
            print(f"监控开始时间: {start_result.get('start_time')}")

            # 运行60秒
            print("\n监控运行中，将运行60秒...")
            await asyncio.sleep(60)

            # 停止监控
            stop_result = await agent.real_time_monitoring_function(
                action="stop")
            if stop_result.get("success"):
                print("✓ 监控停止成功")
            else:
                print(f"❌ 监控停止失败: {stop_result.get('error')}")

        else:
            print(f"❌ 当前时间监控启动失败: {start_result.get('error')}")

    except Exception as e:
        print(f"❌ 当前时间监控测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.cleanup()


async def test_real_time_monitoring_start_stop_cycle():
    """测试监控的启动-停止循环"""
    print("\n" + "=" * 60)
    print("监控启动-停止循环测试")
    print("=" * 60)

    agent = BlowingAgent()

    try:
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 模拟多次启动停止循环
        cycles = [
            {
                "start_time": "2025-08-13 08:00:00",
                "duration": 30
            },
            {
                "start_time": "2025-08-13 08:10:00",
                "duration": 30
            },
            {
                "start_time": None,
                "duration": 30
            }  # 使用当前时间
        ]

        for i, cycle in enumerate(cycles, 1):
            print(f"\n--- 循环 {i} ---")
            start_time = cycle["start_time"]
            duration = cycle["duration"]

            if start_time:
                print(f"启动监控，开始时间: {start_time}")
                start_result = await agent.real_time_monitoring_function(
                    action="start", start_time=start_time)
            else:
                print("启动监控，使用当前时间")
                start_result = await agent.real_time_monitoring_function(
                    action="start")

            if start_result.get("success"):
                print(f"✓ 监控启动成功，运行 {duration} 秒...")
                await asyncio.sleep(duration)

                print("停止监控...")
                stop_result = await agent.real_time_monitoring_function(
                    action="stop")
                if stop_result.get("success"):
                    print("✓ 监控停止成功")
                else:
                    print(f"❌ 监控停止失败: {stop_result.get('error')}")
            else:
                print(f"❌ 监控启动失败: {start_result.get('error')}")

            # 在循环之间稍作停顿
            if i < len(cycles):
                print("等待5秒后进入下一循环...")
                await asyncio.sleep(5)

    except Exception as e:
        print(f"❌ 循环测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.cleanup()


async def test_real_time_monitoring_error_cases():
    """测试实时监控的错误情况"""
    print("\n" + "=" * 60)
    print("实时监控错误情况测试")
    print("=" * 60)

    agent = BlowingAgent()

    try:
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 测试无效操作
        print("\n测试无效操作...")
        result = await agent.real_time_monitoring_function(
            action="invalid_action")
        print(f"结果: {result.get('success')}, 错误: {result.get('error')}")

        # 测试无效时间格式
        print("\n测试无效时间格式...")
        try:
            result = await agent.real_time_monitoring_function(
                action="start", start_time="invalid-time-format")
            print(f"结果: {result.get('success')}, 错误: {result.get('error')}")
        except Exception as e:
            print(f"捕获到预期的错误: {e}")

        # 测试重复启动
        print("\n测试重复启动监控...")
        start_result1 = await agent.real_time_monitoring_function(
            action="start")
        print(f"第一次启动: {start_result1.get('success')}")

        await asyncio.sleep(2)  # 稍等一下

        start_result2 = await agent.real_time_monitoring_function(
            action="start")
        print(f"第二次启动: {start_result2.get('success')}")

        # 停止监控
        stop_result = await agent.real_time_monitoring_function(action="stop")
        print(f"停止监控: {stop_result.get('success')}")

        # 测试重复停止
        print("\n测试重复停止监控...")
        stop_result2 = await agent.real_time_monitoring_function(action="stop")
        print(f"重复停止: {stop_result2.get('success')}")

    except Exception as e:
        print(f"❌ 错误情况测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.cleanup()


async def main():
    """主测试函数"""
    print("开始实时监控功能测试...")

    # 测试完整的监控过程
    await test_real_time_monitoring_complete_process()

    # 测试当前时间监控
    await test_real_time_monitoring_with_current_time()

    # 测试启动停止循环
    await test_real_time_monitoring_start_stop_cycle()

    # 测试错误情况
    # await test_real_time_monitoring_error_cases()

    print("\n所有实时监控测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
