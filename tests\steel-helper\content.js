// 创建悬浮按钮
const helperBtn = document.createElement('div');
helperBtn.className = 'steel-helper-btn';
helperBtn.innerHTML = '钢';
document.body.appendChild(helperBtn);

// 创建助手面板
const helperPanel = document.createElement('div');
helperPanel.className = 'steel-helper-panel';
// 预设问题列表
const presetQuestions = [
    "炼钢的主要工艺流程是什么？",
    "转炉 “九大” 主要操作和工艺制度标准化模型包含哪些内容？",
    "如何提高钢的质量？",
    "生产运营涉及哪些类型的指标？",
    "炼钢过程中常见的问题有哪些？",
    "查一下2025年4月生白云石消耗",
    "2025年4月3日生白云石消耗有没有达到基准值及一级标准值",
    "查一下2025年4月炼铁工序中高炉最低顶温",
    "帮我查一下今年4月钢坯成本、炼钢工序加工成本",
    "帮我查一下今年4月铁水成本、高炉工序加工成本",
    "运营成本高一般是哪些因素导致的？",
    "如何降低运营成本？",
    "如何降低钢坯成本？"
];

// 保存最近4个提问过的问题的队列
let recentQuestionsQueue = [];

// 随机选择两个预设问题，排除最近提问过的问题
function getRandomPresetQuestions() {
    const availableQuestions = presetQuestions.filter(question => !recentQuestionsQueue.includes(question));
    const shuffled = availableQuestions.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, 2);
}

const randomQuestions = getRandomPresetQuestions();
const presetQuestionsHtml = randomQuestions.map(question => `<div class="preset-question">${question}</div>`).join('');
helperPanel.innerHTML = `
  <div class="panel-header">
    <span>炼钢智能助手</span>
    <span class="close-btn">×</span>
  </div>
  <div class="panel-content" id="message-area">
    <!-- 初始提示，可根据需求调整 -->
    <div class="message-container assistant">
      <div class="message-text">您好！我是炼钢领域助手，有问题可以问我哦～</div>
    </div>
    <div class="preset-questions">
      ${presetQuestionsHtml}
    </div>
  </div>
  <div class="input-area">
    <input type="text" id="message-input" placeholder="输入你的问题...">
    <button id="send-btn">发送</button>
  </div>
`;
document.body.appendChild(helperPanel);

// 显示/隐藏面板逻辑
helperBtn.addEventListener('click', () => {
    helperPanel.classList.toggle('active');
});
document.querySelector('.close-btn').addEventListener('click', () => {
    helperPanel.classList.remove('active');
});

// 消息发送 & 响应逻辑
const messageInput = document.getElementById('message-input');
const sendBtn = document.getElementById('send-btn');
const messageArea = document.getElementById('message-area');

// 记录 “当前助手正在回复的消息容器”，保证同一轮对话只开一个气泡
let currentAssistantMsgContainer = null;

sendBtn.addEventListener('click', sendMessage);
messageInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') sendMessage();
});

function sendMessage() {
    const userText = messageInput.value.trim();
    if (!userText) return;

    // 将问题添加到最近提问过的问题队列中
    recentQuestionsQueue.push(userText);
    if (recentQuestionsQueue.length > 4) {
        recentQuestionsQueue.shift(); // 移除最早的问题
    }

    // 1. 渲染“用户消息”气泡
    const userContainer = document.createElement('div');
    userContainer.className = 'message-container user';
    userContainer.innerHTML = `<div class="message-text">${userText}</div>`;
    messageArea.appendChild(userContainer);

    // 2. 清空输入框 + 滚动到底部
    messageInput.value = '';
    messageArea.scrollTop = messageArea.scrollHeight;

    // 3. 发送消息到 WebSocket
    chrome.runtime.sendMessage({type: 'sendMessage', content: userText});

    // 4. 准备接收助手回复：重置“当前助手消息容器”，等待新内容
    currentAssistantMsgContainer = null;
}

// 监听来自 background.js 的消息（助手回复）
chrome.runtime.onMessage.addListener((request) => {
    if (request.type === 'receiveMessage') {
        // 如果是新的回复且没有容器，创建新容器
        if (!currentAssistantMsgContainer) {
            currentAssistantMsgContainer = document.createElement('div');
            currentAssistantMsgContainer.className = 'message-container assistant';
            currentAssistantMsgContainer.innerHTML = '<div class="message-text"></div>';
            messageArea.appendChild(currentAssistantMsgContainer);
        }

        // 获取消息文本节点
        const textNode = currentAssistantMsgContainer.querySelector('.message-text');

        // 处理流式内容
        if (!request.is_finished) {
            // 对于stream_start消息可以显示"正在生成响应..."
            textNode.textContent += request.content;
        } else {
            // 对于结束消息，确保显示完整内容
            textNode.textContent = request.content;
            currentAssistantMsgContainer = null;

            // 更新预设问题
            const newRandomQuestions = getRandomPresetQuestions();
            const newPresetQuestionsHtml = newRandomQuestions.map(question =>
                `<div class="preset-question">${question}</div>`).join('');
            const presetQuestionsDiv = document.createElement('div');
            presetQuestionsDiv.className = 'preset-questions';
            presetQuestionsDiv.innerHTML = newPresetQuestionsHtml;
            messageArea.appendChild(presetQuestionsDiv);

            // 绑定预设问题点击事件
            newPresetQuestionsDiv.querySelectorAll('.preset-question').forEach(el => {
                el.addEventListener('click', () => {
                    messageInput.value = el.textContent;
                    sendMessage();
                });
            });
        }

        // 滚动到底部
        messageArea.scrollTop = messageArea.scrollHeight;
    }
});

// 让悬浮按钮可拖动
let isDragging = false;
let offsetX, offsetY;

helperBtn.addEventListener('mousedown', (e) => {
    isDragging = true;
    offsetX = e.clientX - helperBtn.offsetLeft;
    offsetY = e.clientY - helperBtn.offsetTop;
    helperBtn.style.cursor = 'grabbing';
});

document.addEventListener('mousemove', (e) => {
    if (isDragging) {
        helperBtn.style.left = (e.clientX - offsetX) + 'px';
        helperBtn.style.top = (e.clientY - offsetY) + 'px';
        helperBtn.style.right = 'auto';
        helperBtn.style.bottom = 'auto';
    }
});

document.addEventListener('mouseup', () => {
    isDragging = false;
    helperBtn.style.cursor = 'pointer';
});

// 为预设问题添加点击事件
const presetQuestionElements = document.querySelectorAll('.preset-question');
presetQuestionElements.forEach(questionElement => {
    questionElement.addEventListener('click', () => {
        const question = questionElement.textContent;
        messageInput.value = question;
        sendMessage();
    });
});