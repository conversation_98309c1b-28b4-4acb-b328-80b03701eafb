"""SQL查询相关工具类"""
from typing import Optional, Dict, Any, List, Tuple, Type, ClassVar
from langchain.tools import BaseTool
from langchain.callbacks.manager import (
    AsyncCallbackManagerForToolRun,
    CallbackManagerForToolRun,
)
from pydantic import BaseModel, Field
import pandas as pd
import json
from datetime import datetime
from config.logging import logger
from config.settings import settings
from langchain_core.prompts import PromptTemplate
from ..common.llms import get_llm
from database.connection import get_engine
from database.schema import get_table_info, get_default_table_info


class SQLGenerateInput(BaseModel):
    """SQL生成输入"""
    user_query: str = Field(..., description="用户查询语句")
    dialect: str = Field(default="MySQL", description="SQL方言")
    table_info: Optional[str] = Field(None, description="表结构信息")


class SQLGenerateTool(BaseTool):
    """SQL生成工具 - 根据自然语言生成SQL查询"""
    name: str = "generate_sql"
    description: str = "根据用户的自然语言查询生成对应的SQL语句，支持经营指标、成本分析等查询"
    args_schema: Type[BaseModel] = SQLGenerateInput

    def _run(self,
             user_query: str,
             dialect: str = "MySQL",
             table_info: Optional[str] = None,
             run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """同步运行 - 生成SQL"""
        llm = get_llm()
        current_date = datetime.today().date()
        return self._generate_sql(user_query, dialect, table_info, llm,
                                  current_date)

    async def _arun(
            self,
            user_query: str,
            dialect: str = "MySQL",
            table_info: Optional[str] = None,
            run_manager: Optional[AsyncCallbackManagerForToolRun] = None
    ) -> str:
        """异步运行 - 生成SQL"""
        llm = get_llm()
        current_date = datetime.today().date()
        return await self._agenerate_sql(user_query, dialect, table_info, llm,
                                         current_date)

    def _generate_sql(self, user_query: str, dialect: str,
                      table_info: Optional[str], llm, current_date) -> str:
        """生成SQL的核心逻辑"""
        # 如果没有提供表信息，使用默认的表结构
        if not table_info:
            table_info = self._get_default_table_info()

        prompt = PromptTemplate(
            template=GENERATE_SQL_PROMPT,
            input_variables=["dialect", "table_info", "input", "current_date"])

        prompt_text = prompt.format(dialect=dialect,
                                    table_info=table_info,
                                    input=user_query,
                                    current_date=current_date)

        response = llm.invoke(prompt_text)
        sql = response.content.replace("```", "").replace("sql", "").strip()

        logger.info(f"Generated SQL: {sql}")
        return sql

    async def _agenerate_sql(self, user_query: str, dialect: str,
                             table_info: Optional[str], llm,
                             current_date) -> str:
        """异步生成SQL"""
        if not table_info:
            table_info = self._get_default_table_info()

        prompt = PromptTemplate(
            template=GENERATE_SQL_PROMPT,
            input_variables=["dialect", "table_info", "input", "current_date"])

        prompt_text = prompt.format(dialect=dialect,
                                    table_info=table_info,
                                    input=user_query,
                                    current_date=current_date)

        response = await llm.ainvoke(prompt_text)
        sql = response.content.replace("```", "").replace("sql", "").strip()

        logger.info(f"Generated SQL: {sql}")
        return sql

    def _get_default_table_info(self) -> str:
        """获取默认的表结构信息"""
        # 首先尝试从真实数据库获取表结构
        try:
            return get_table_info(tables=settings.TARGET_TABLES)
        except Exception as e:
            logger.warning(f"Failed to get table info from database: {e}")
            # 如果失败，返回默认的表结构信息
            return get_default_table_info()


class SQLExecuteInput(BaseModel):
    """SQL执行输入"""
    sql: str = Field(..., description="要执行的SQL语句")
    use_mock_data: bool = Field(default=False, description="是否使用模拟数据")


class SQLExecuteTool(BaseTool):
    """SQL执行工具 - 执行SQL并返回结果"""
    name: str = "execute_sql"
    description: str = "执行SQL查询语句并返回结果数据"
    args_schema: Type[BaseModel] = SQLExecuteInput

    def _run(self,
             sql: str,
             use_mock_data: bool = False,
             run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """执行SQL并返回结果"""
        try:
            # 如果指定使用模拟数据，返回模拟数据
            if use_mock_data:
                return self._get_mock_data(sql)

            # 实际执行SQL
            engine = get_engine()
            df = pd.read_sql(sql, engine)
            result = {
                "success": True,
                "row_count": len(df),
                "columns": df.columns.tolist(),
                "data": df.to_dict("records"),
                "data_string": df.to_string()
            }
            return json.dumps(result, ensure_ascii=False)

        except Exception as e:
            logger.error(f"SQL execution error: {e}")
            # 如果真实数据库连接失败，尝试返回模拟数据
            if "数据库连接" in str(e) or "Connection" in str(e):
                logger.info("Database connection failed, returning mock data")
                return self._get_mock_data(sql)
            return json.dumps({
                "success": False,
                "error": str(e)
            },
                              ensure_ascii=False)

    def _get_mock_data(self, sql: str) -> str:
        """返回模拟数据"""
        # 根据SQL内容返回不同的模拟数据
        if "steelmaking" in sql.lower():
            mock_data = {
                "success":
                True,
                "row_count":
                5,
                "columns": ["指标日期", "指标名称", "当日指标值", "指标基准值", "异常判断标准"],
                "data": [{
                    "指标日期": "2024-01-15",
                    "指标名称": "吨钢毛利",
                    "当日指标值": 280.5,
                    "指标基准值": 300.0,
                    "异常判断标准": "低于基准值5%视为异常"
                }, {
                    "指标日期": "2024-01-15",
                    "指标名称": "钢坯成本",
                    "当日指标值": 2650.0,
                    "指标基准值": 2500.0,
                    "异常判断标准": "高于基准值5%视为异常"
                }],
                "data_string":
                "模拟数据结果..."
            }
        else:
            mock_data = {
                "success": True,
                "row_count": 0,
                "columns": [],
                "data": [],
                "data_string": "No data found"
            }

        return json.dumps(mock_data, ensure_ascii=False)

    async def _arun(
            self,
            sql: str,
            use_mock_data: bool = False,
            run_manager: Optional[AsyncCallbackManagerForToolRun] = None
    ) -> str:
        """异步执行SQL并返回结果"""
        # 暂时使用同步方法，后续可以改为异步数据库连接
        # 注意：这里我们不传递run_manager，因为类型不匹配
        return self._run(sql, use_mock_data, None)


class DataAnalysisInput(BaseModel):
    """数据分析输入"""
    query: str = Field(..., description="分析需求描述")
    data: str = Field(..., description="要分析的数据（JSON格式）")
    analysis_type: str = Field(
        default="general", description="分析类型：general/trend/comparison/anomaly")


class DataAnalysisTool(BaseTool):
    """数据分析工具 - 对查询结果进行智能分析"""
    name: str = "analyze_data"
    description: str = "对SQL查询结果进行深度分析，识别异常、趋势和业务洞察"
    args_schema: Type[BaseModel] = DataAnalysisInput

    def _run(self,
             query: str,
             data: str,
             analysis_type: str = "general",
             run_manager: Optional[CallbackManagerForToolRun] = None) -> str:
        """执行数据分析"""
        llm = get_llm()
        return self._analyze(query, data, analysis_type, llm)

    async def _arun(
            self,
            query: str,
            data: str,
            analysis_type: str = "general",
            run_manager: Optional[AsyncCallbackManagerForToolRun] = None
    ) -> str:
        """异步执行数据分析"""
        llm = get_llm()
        return await self._aanalyze(query, data, analysis_type, llm)

    def _analyze(self, query: str, data: str, analysis_type: str, llm) -> str:
        """分析数据的核心逻辑"""
        # 根据分析类型选择不同的提示词
        if analysis_type == "anomaly":
            prompt_template = ANOMALY_ANALYSIS_PROMPT
        elif analysis_type == "trend":
            prompt_template = TREND_ANALYSIS_PROMPT
        else:
            prompt_template = GENERAL_ANALYSIS_PROMPT

        prompt = PromptTemplate(template=prompt_template,
                                input_variables=["input", "data"])

        prompt_text = prompt.format(input=query, data=data)
        response = llm.invoke(prompt_text)

        return response.content

    async def _aanalyze(self, query: str, data: str, analysis_type: str,
                        llm) -> str:
        """异步分析数据"""
        if analysis_type == "anomaly":
            prompt_template = ANOMALY_ANALYSIS_PROMPT
        elif analysis_type == "trend":
            prompt_template = TREND_ANALYSIS_PROMPT
        else:
            prompt_template = GENERAL_ANALYSIS_PROMPT

        prompt = PromptTemplate(template=prompt_template,
                                input_variables=["input", "data"])

        prompt_text = prompt.format(input=query, data=data)
        response = await llm.ainvoke(prompt_text)

        return response.content


# 提示词模板
GENERATE_SQL_PROMPT = """
你是精通{dialect}的数据库专家，需根据用户问题和表结构信息，生成可直接执行的SQL查询语句。

核心规则：
1. 指标数据来源限定：
   - 当问题涉及炼钢工序及以下指标时，必须从steelmaking_indicator_info和steelmaking_daily_data表查询
   - 当问题涉及轧钢工序及以下指标时，必须从steelrolling_indicator_info和steelrolling_daily_data表查询
   - 当问题涉及炼铁工序及以下指标时，必须从ironmaking_indicator_info和ironmaking_daily_data表查询

2. 输出格式强制要求：
   - 仅返回SQL语句，无任何解释、注释或多余内容
   - 语句必须符合{dialect}语法规范

3. 时间处理规则：
   - 提及"今天"、"当前日期"等相对时间时，使用当前系统日期：{current_date}
   - 时间范围查询使用BETWEEN

4. 字段与条件约束：
   - WHERE子句中的所有过滤字段，必须包含在SELECT列表中
   - 有明确数据量要求时，使用LIMIT

### 可用表结构：
{table_info}

### 用户问题：
{input}
"""

GENERAL_ANALYSIS_PROMPT = """
请根据问题和查询结果进行分析。查询结果是从数据库查出的结构化数据，请：
1. 简明扼要地回答用户问题
2. 识别数据中的关键信息和模式
3. 提供有价值的业务洞察
4. 如果发现异常或问题，给出具体建议

问题：{input}
查询结果：{data}

分析：
"""

ANOMALY_ANALYSIS_PROMPT = """
请分析数据中的异常情况。根据"异常判断标准"进行指标异常分析：
1. 识别所有异常指标
2. 分析异常原因（需要有数据支撑）
3. 从"指标影响因素"中找出相关因素
4. 给出改进建议

只回答有异常的指标，格式为：
- XX指标异常，当日值(具体数值) >/<基准值(具体数值)
- 异常原因：...
- 改进建议：...

问题：{input}
数据：{data}

异常分析：
"""

TREND_ANALYSIS_PROMPT = """
请进行趋势分析：
1. 识别数据的变化趋势
2. 计算关键指标的变化率
3. 预测未来可能的走向
4. 提供趋势背后的原因分析

问题：{input}
数据：{data}

趋势分析：
"""


def get_sql_tools() -> List[BaseTool]:
    """获取SQL相关工具集"""
    return [SQLGenerateTool(), SQLExecuteTool(), DataAnalysisTool()]
