"""
WebSocket集成测试
测试WebSocketStreamingCallbackHandler与decision_websocket_endpoint的集成
"""

import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import WebSocket
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.backend.api.websocket.decision import WebSocketStreamingCallbackHandler, decision_websocket_endpoint
from src.backend.services.websocket_manager import ConnectionManager


class MockWebSocket:
    """模拟WebSocket连接"""
    
    def __init__(self):
        self.sent_messages = []
        self.received_messages = []
        self.is_accepted = False
        self.is_closed = False
        self.close_code = None
        self.close_reason = None
        self.app = MagicMock()
        self.app.state.connection_manager = ConnectionManager()
    
    async def accept(self):
        """模拟接受连接"""
        self.is_accepted = True
    
    async def send_text(self, data: str):
        """模拟发送文本"""
        self.sent_messages.append(("text", data))
    
    async def send_json(self, data: dict):
        """模拟发送JSON"""
        self.sent_messages.append(("json", data))
    
    async def receive_text(self):
        """模拟接收文本"""
        if self.received_messages:
            return self.received_messages.pop(0)
        else:
            # 模拟断开连接
            from fastapi import WebSocketDisconnect
            raise WebSocketDisconnect()
    
    async def close(self, code: int = 1000, reason: str = ""):
        """模拟关闭连接"""
        self.is_closed = True
        self.close_code = code
        self.close_reason = reason
    
    def add_received_message(self, message: str):
        """添加要接收的消息"""
        self.received_messages.append(message)


class TestWebSocketIntegration:
    """WebSocket集成测试类"""
    
    async def test_callback_handler_with_real_connection_manager(self):
        """测试回调处理器与真实ConnectionManager的集成"""
        print("🔗 测试回调处理器与ConnectionManager集成...")
        
        # 创建真实的ConnectionManager
        connection_manager = ConnectionManager()
        
        # 创建模拟WebSocket
        mock_websocket = MockWebSocket()
        client_id = "integration_test_client"
        
        # 连接WebSocket
        await connection_manager.connect(mock_websocket, client_id)
        
        # 创建回调处理器
        callback_handler = WebSocketStreamingCallbackHandler(connection_manager, client_id)
        
        # 测试LLM开始
        await callback_handler.on_llm_start({}, ["测试提示"])
        
        # 检查消息是否发送
        assert len(mock_websocket.sent_messages) == 1
        message_type, message_data = mock_websocket.sent_messages[0]
        assert message_type == "json"
        assert message_data["type"] == "stream_start"
        assert message_data["agent"] == "decision"
        
        # 测试token流
        tokens = ["智能", "炼钢", "助手"]
        for token in tokens:
            await callback_handler.on_llm_new_token(token)
        
        # 检查token消息
        assert len(mock_websocket.sent_messages) == 4  # 1个开始 + 3个token
        
        for i, token in enumerate(tokens):
            message_type, message_data = mock_websocket.sent_messages[i + 1]
            assert message_type == "json"
            assert message_data["type"] == "stream"
            assert message_data["content"] == token
            assert message_data["is_finished"] is False
        
        # 测试LLM结束
        from langchain.schema import LLMResult, Generation
        generation = Generation(text="智能炼钢助手")
        llm_result = LLMResult(generations=[[generation]])
        
        await callback_handler.on_llm_end(llm_result)
        
        # 检查结束消息
        assert len(mock_websocket.sent_messages) == 5  # 1个开始 + 3个token + 1个结束
        message_type, message_data = mock_websocket.sent_messages[4]
        assert message_type == "json"
        assert message_data["type"] == "stream_end"
        assert message_data["is_finished"] is True
        assert message_data["total_content"] == "智能炼钢助手"
        
        # 断开连接
        connection_manager.disconnect(client_id)
        
        print("✅ ConnectionManager集成测试通过")
    
    async def test_websocket_endpoint_with_mock_agent(self):
        """测试WebSocket端点与模拟智能体的集成"""
        print("🤖 测试WebSocket端点与模拟智能体集成...")
        
        # 创建模拟WebSocket
        mock_websocket = MockWebSocket()
        
        # 模拟DecisionAgent
        class MockDecisionAgent:
            def __init__(self):
                self.initialized = False
                self.history = []
            
            async def initialize(self):
                self.initialized = True
            
            def get_history(self, limit=10):
                return self.history[-limit:]
            
            def clear_history(self):
                self.history = []
                return "历史记录已清空"
            
            async def process_stream(self, query, context, callbacks=None):
                """模拟流式处理 - 异步生成器"""
                # 模拟通过回调发送tokens
                if callbacks:
                    for callback in callbacks:
                        await callback.on_llm_start({}, [query])
                        
                        tokens = ["根据", "您的", "查询", "，", "我为您", "分析", "如下"]
                        for token in tokens:
                            await callback.on_llm_new_token(token)
                        
                        from langchain.schema import LLMResult, Generation
                        full_response = "".join(tokens)
                        generation = Generation(text=full_response)
                        llm_result = LLMResult(generations=[[generation]])
                        await callback.on_llm_end(llm_result)
                
                # 返回异步生成器值，让调用者可以消费
                yield "模拟响应完成"
        
        # 使用patch替换DecisionAgent
        with patch('src.backend.api.websocket.decision.DecisionAgent', MockDecisionAgent):
            # 准备测试消息
            test_query = {
                "type": "query",
                "content": "请分析炼钢数据"
            }
            mock_websocket.add_received_message(json.dumps(test_query))
            
            # 运行WebSocket端点（会因为断开连接而结束）
            try:
                await decision_websocket_endpoint(mock_websocket)
            except Exception as e:
                # 预期会有WebSocketDisconnect异常
                pass
            
            # 验证消息发送
            sent_messages = mock_websocket.sent_messages
            
            # 应该至少有连接确认消息
            connection_message = None
            stream_messages = []
            
            for msg_type, msg_data in sent_messages:
                if msg_type == "json":
                    if msg_data.get("type") == "connected":
                        connection_message = msg_data
                    elif msg_data.get("type") in ["stream_start", "stream", "stream_end", "processing"]:
                        stream_messages.append(msg_data)
            
            # 验证连接消息
            assert connection_message is not None
            assert connection_message["agent"] == "decision"
            
            # 验证流式消息
            assert len(stream_messages) > 0
            
            # 查找开始、token和结束消息
            start_messages = [msg for msg in stream_messages if msg.get("type") == "stream_start"]
            token_messages = [msg for msg in stream_messages if msg.get("type") == "stream"]
            end_messages = [msg for msg in stream_messages if msg.get("type") == "stream_end"]
            
            assert len(start_messages) >= 1  # 至少有一个开始消息
            assert len(token_messages) >= 1   # 至少有一些token消息
            assert len(end_messages) >= 1     # 至少有一个结束消息
            
            print(f"   📨 发送了 {len(sent_messages)} 条消息")
            print(f"   🚀 开始消息: {len(start_messages)} 条")
            print(f"   🔤 Token消息: {len(token_messages)} 条") 
            print(f"   🏁 结束消息: {len(end_messages)} 条")
        
        print("✅ WebSocket端点集成测试通过")
    
    async def test_error_handling_integration(self):
        """测试错误处理的集成"""
        print("⚠️ 测试错误处理集成...")
        
        # 创建会抛出异常的ConnectionManager
        class FailingConnectionManager(ConnectionManager):
            async def send_json(self, client_id: str, data: dict):
                if data.get("type") == "stream":
                    raise Exception("模拟网络错误")
                await super().send_json(client_id, data)
        
        # 创建模拟WebSocket
        mock_websocket = MockWebSocket()
        connection_manager = FailingConnectionManager()
        client_id = "error_test_client"
        
        # 连接WebSocket
        await connection_manager.connect(mock_websocket, client_id)
        
        # 创建回调处理器
        callback_handler = WebSocketStreamingCallbackHandler(connection_manager, client_id)
        
        # 测试正常开始（应该成功）
        await callback_handler.on_llm_start({}, ["测试"])
        
        # 测试token发送（应该失败但不崩溃）
        try:
            await callback_handler.on_llm_new_token("测试token")
            assert False, "应该抛出异常"
        except Exception as e:
            assert "模拟网络错误" in str(e)
        
        print("✅ 错误处理集成测试通过")
    
    async def test_unicode_and_special_chars_integration(self):
        """测试Unicode和特殊字符的集成处理"""
        print("🌍 测试Unicode和特殊字符集成...")
        
        connection_manager = ConnectionManager()
        mock_websocket = MockWebSocket()
        client_id = "unicode_test_client"
        
        await connection_manager.connect(mock_websocket, client_id)
        callback_handler = WebSocketStreamingCallbackHandler(connection_manager, client_id)
        
        # 测试各种Unicode字符
        special_tokens = [
            "🔥",           # 表情符号
            "钢铁工业",      # 中文
            "№123",         # 特殊符号
            "α=0.5",        # 希腊字母和数学符号
            "温度：1500°C",  # 度数符号
            "效率↑",        # 箭头
            "CO₂",          # 下标
            "Fe²⁺",         # 上标和下标
        ]
        
        await callback_handler.on_llm_start({}, ["Unicode测试"])
        
        for token in special_tokens:
            await callback_handler.on_llm_new_token(token)
        
        from langchain.schema import LLMResult, Generation
        full_text = "".join(special_tokens)
        generation = Generation(text=full_text)
        llm_result = LLMResult(generations=[[generation]])
        await callback_handler.on_llm_end(llm_result)
        
        # 验证所有消息都正确发送
        assert len(mock_websocket.sent_messages) == len(special_tokens) + 2  # tokens + start + end
        
        # 验证Unicode内容正确传输
        for i, token in enumerate(special_tokens):
            message_type, message_data = mock_websocket.sent_messages[i + 1]
            assert message_data["content"] == token
        
        # 验证最终内容
        final_message = mock_websocket.sent_messages[-1][1]
        assert final_message["total_content"] == full_text
        
        print("✅ Unicode和特殊字符集成测试通过")


async def run_integration_tests():
    """运行所有集成测试"""
    print("🧪 开始WebSocket集成测试...")
    print("=" * 60)
    
    test_instance = TestWebSocketIntegration()
    
    try:
        await test_instance.test_callback_handler_with_real_connection_manager()
        await test_instance.test_websocket_endpoint_with_mock_agent()
        await test_instance.test_error_handling_integration()
        await test_instance.test_unicode_and_special_chars_integration()
        
        print("=" * 60)
        print("🎉 所有集成测试通过！")
        print("📋 集成测试摘要：")
        print("   - 回调处理器与ConnectionManager集成：✅")
        print("   - WebSocket端点与智能体集成：✅") 
        print("   - 错误处理集成：✅")
        print("   - Unicode字符集成：✅")
        print("📈 测试覆盖范围：")
        print("   - 流式token处理：✅")
        print("   - 连接管理：✅")
        print("   - 错误恢复：✅")
        print("   - 字符编码：✅")
        
    except Exception as e:
        print("❌ 集成测试失败:")
        print(f"   错误: {e}")
        raise


if __name__ == "__main__":
    print("运行 WebSocket 集成测试...")
    asyncio.run(run_integration_tests())
