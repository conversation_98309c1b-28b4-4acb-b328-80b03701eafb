def load_knowledge_base():
    """加载知识库"""
    return """
    炼钢场景包括转炉工序控枪、加料、复吹、拉碳、测试、合金化、出钢倾动、钢包吹氩、溅渣护炉等主要操作环节，生产运营包括产量指标（转炉产量、板坯产量等）、
    经济技术指标（钢铁料消耗、合金消耗、石灰消耗、氧气消耗、双命中率、电耗等）、成本指标（总产量、生产成本、工序加工成本、工序降本额）、
    质量指标（铁水温度、铁水硅含量、碳含量出钢温度等）。基于浪潮智产大模型，融合山东九羊4大类20多个私域数据集，
    将转炉"九大"转炉主要操作和工艺制度标准化模型（造渣、吹炼、副枪测试、增碳脱氧合金化、终点拉碳、复吹运维、出钢操作、钢水包吹氩、溅渣护炉操作）
    与仿人工操作的动态控制相结合,解决了炼钢厂过分依赖入炉原材料、依赖现场炉长和操作工"人员经验"、过程工艺成本控制难度大等问题, 
    打造了"极数吹炼""极目观火""智能问数"三个智能体，帮助炼钢厂逐步实现工艺和操作层面的标准化，生产运营精益化。
    
    经营成本高居不下的原因有两个方面：
    1、钢坯成本过高
    2、煤气资源浪费，回收利用率低，表现：高炉、转炉煤气产生量远超生产工序实际需求（如热风炉、烧结、炼钢等），大量富余煤气因缺乏高效利用途径被迫放散，导致能源浪费和环保压力。
    3、发电效率低，单耗过高，表现：使用中温中压锅炉或老旧发电机组，煤气发电单耗高（如5m³/kWh以上），远低于行业先进水平（亚临界机组2.4m³/kWh），导致发电量不足，经济性差。
    4、经济效益未充分释放，表现：传统煤气利用方式（如直接燃烧或低效发电）收益有限，无法覆盖设备折旧、运维成本，投资回收期长（如老旧方案回收期超3年）。
    5、系统冗余与产能错配，表现：原有锅炉与发电机组规模固定，无法匹配实际煤气波动（如高炉休风、转炉间歇性产气），导致设备利用率低。
    
    其中降低经营成本可以从以下方面进行着手：
    一、减少钢坯成本：[
  {
    "任务": "转炉炼钢工艺优化",
    "目标": "降低成本、提高质量、减少资源浪费",
    "规则": {
      "控制喷溅": {
        "目标": "杜绝喷溅、减少金属损失",
        "操作": "装入量实时调整，铁水成分按55-60吨控制"
      },
      "出钢温度": {
        "目标": "提高合格率",
        "操作": "降低出钢温度，推进钢包加盖"
      },
      "钢铁料消耗": {
        "目标": "降低消耗",
        "操作": "提高一倒C合格率，拉碳按5-7个控制，合格率>80%"
      },
      "渣料回收": {
        "操作": "行车砸碎渣块，吸出全部铁料"
      },
      "石灰消耗": {
        "规则": "根据铁水成分实时调整石灰量"
      },
      "生白云石消耗": {
        "规则": "根据炉渣MgO含量实时调整生白云石量"
      },
      "Mn回收率": {
        "操作": [
          "终点压枪≥30秒，降低全铁",
          "若终渣发泡，下一炉降低拉碳枪位100-200mm"
        ]
      },
      "合金成分优化": {
        "Mn控制范围": "0.23%-0.32%",
        "惩罚规则": "若Mn>0.35%，每炉考核炉长20元"
      },
      "维修费用": {
        "目标": "降低2元/吨",
        "操作": "修旧利废，改造旧连铸设备补充新连铸"
      },
      "水系统优化": {
        "操作": "扬床产水转浊环水池，制氧好水回流，新水补蒸发量"
      },
      "电系统优化": {
        "操作": "计划性停炉停机停泵，优化行车运行标准"
      },
      "煤气回收": {
        "目标": "多回收10m³，创效1.5元/吨",
        "规则": "优先保证石灰窑用气"
      },
      "连铸生产组织": {
        "温度控制": "避免频繁开浇堵流，控制中包过热度防断流",
        "质量控制": [
          "无脱方、缩孔",
          "优化拉速减少坏头坏尾损失"
        ]
      }
    }
  }
]
二、解决煤气资源浪费，回收利用率低问题（高炉、转炉煤气产生量远超生产工序实际需求（如热风炉、烧结、炼钢等），大量富余煤气因缺乏高效利用途径被迫放散，导致能源浪费和环保压力。）
解决方案：
1、建立煤气平衡动态监控系统
2、实时监测各工序煤气产生量与消耗量，识别富余量。
3、通过煤气柜或储气罐（如5万m³高炉煤气柜）缓冲供需波动，避免放散。
4、优先将富余煤气用于高效发电，替代传统低效燃烧或放散。

三、解决发电效率低，单耗过高问题（使用中温中压锅炉或老旧发电机组，煤气发电单耗高（如5m³/kWh以上），远低于行业先进水平（亚临界机组2.4m³/kWh），导致发电量不足，经济性差。）
解决方案：
1、升级发电技术与设备
2、淘汰中温中压机组，升级为亚临界或超临界锅炉发电机组，降低单耗（如从5m³/kWh降至2.3-2.4m³/kWh）。
3、改造现有闲置设备：对烧结余热锅炉等低效资产增加煤气烧嘴，盘活闲置产能。
4、采用"汽改电"：将汽动鼓风机改为电拖风机，集中蒸汽用于发电，提升系统效率。
四、解决经济效益未充分释放问题（传统煤气利用方式（如直接燃烧或低效发电）收益有限，无法覆盖设备折旧、运维成本，投资回收期长（如老旧方案回收期超3年）。）

解决方案：
1、全生命周期经济优化模型
2、计算边际效益：对比外购电价与煤气发电成本（如外购电0.64元/kWh，煤气发电成本0.11元/kWh），优先利用富余煤气发电。
3、多收益叠加：煤气发电节省的外购电费 + 替代煤粉的燃料成本（如年省煤粉成本2927万元） + 碳减排收益（潜在碳交易）。
4、分阶段投资：先建小型煤气柜（1500万元）快速回收，再逐步升级发电机组（如6万kW亚临界机组1.5亿元），综合回收期可缩短至1.5-2年。

五：解决系统冗余与产能错配问题（原有锅炉与发电机组规模固定，无法匹配实际煤气波动（如高炉休风、转炉间歇性产气），导致设备利用率低。）
解决方案：
1、模块化与柔性设计
2、模块化发电机组：采用多台中小型机组并联，根据煤气量灵活启停（如2×1.5万kW机组替代单台3万kW机组）。
3、动态调度：通过智能控制系统协调煤气柜、发电机组、余热锅炉，优先满足高价值工序（如炼钢），富余再发电。
    """