#!/usr/bin/env python3
"""
增强版吹炼智能体WebSocket客户端示例
演示实时监测和阶段播报功能的使用
"""

import asyncio
import websockets
import json
from datetime import datetime


class BlowingAgentClient:
    """吹炼智能体客户端"""
    
    def __init__(self, uri="ws://localhost:8000/ws/blowing"):
        self.uri = uri
        self.websocket = None
        self.running = False
    
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            self.websocket = await websockets.connect(self.uri)
            print(f"已连接到服务器: {self.uri}")
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            print("已断开连接")
    
    async def send_message(self, message):
        """发送消息"""
        if self.websocket:
            await self.websocket.send(json.dumps(message, ensure_ascii=False))
    
    async def receive_messages(self):
        """接收消息循环"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await self.handle_message(data)
        except websockets.exceptions.ConnectionClosed:
            print("连接已关闭")
        except Exception as e:
            print(f"接收消息错误: {e}")
    
    async def handle_message(self, data):
        """处理接收到的消息"""
        msg_type = data.get("type", "unknown")
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        print(f"\n[{timestamp}] 收到消息: {msg_type}")
        
        if msg_type == "connected":
            print(f"✓ {data.get('message', '已连接')}")
            
        elif msg_type == "monitoring_started":
            print(f"✓ 实时监测已启动: {data.get('message', '')}")
            
        elif msg_type == "monitoring_stopped":
            print(f"✓ 实时监测已停止: {data.get('message', '')}")
            
        elif msg_type == "stage_broadcast":
            print("📢 阶段播报:")
            result = data.get("result", {})
            if result.get("success"):
                report = result.get("report", {})
                print(f"   阶段: {report.get('stage_name', '未知')}")
                print(f"   吹炼时间: 第{report.get('blow_time', 0)}分钟")
                print(f"   播报内容: {report.get('broadcast_content', '无内容')}")
            else:
                print(f"   播报失败: {result.get('error', '未知错误')}")
                
        elif msg_type == "response":
            print(f"💬 智能体回复: {data.get('content', '')}")
            
        elif msg_type == "error":
            print(f"❌ 错误: {data.get('message', '未知错误')}")
            
        else:
            print(f"📄 其他消息: {json.dumps(data, ensure_ascii=False, indent=2)}")


async def demo_basic_query(client):
    """演示基本查询功能"""
    print("\n=== 基本查询演示 ===")
    
    # 发送查询消息
    query_message = {
        "type": "query",
        "content": "请分析当前炉况状态",
        "context": {"demo": True}
    }
    
    await client.send_message(query_message)
    print("已发送查询请求...")


async def demo_real_time_monitoring(client):
    """演示实时监测功能"""
    print("\n=== 实时监测演示 ===")
    
    # 启动监测
    start_message = {"type": "start_monitoring"}
    await client.send_message(start_message)
    print("已发送启动监测请求...")
    
    # 等待一段时间观察监测结果
    print("监测运行中，30秒后自动停止...")
    await asyncio.sleep(30)
    
    # 停止监测
    stop_message = {"type": "stop_monitoring"}
    await client.send_message(stop_message)
    print("已发送停止监测请求...")


async def demo_stage_notification(client):
    """演示阶段播报功能"""
    print("\n=== 阶段播报演示 ===")
    
    # 模拟不同阶段的通知
    stages = [
        {"stage_number": 1, "stage_name": "预热阶段"},
        {"stage_number": 2, "stage_name": "脱碳阶段"},
        {"stage_number": 3, "stage_name": "精炼阶段"},
        {"stage_number": 4, "stage_name": "终点阶段"}
    ]
    
    for stage in stages:
        stage_message = {
            "type": "stage_notification",
            "stage_info": stage
        }
        
        await client.send_message(stage_message)
        print(f"已发送阶段通知: {stage['stage_name']}")
        
        # 等待2秒再发送下一个阶段
        await asyncio.sleep(2)


async def demo_control_operations(client):
    """演示控制操作功能"""
    print("\n=== 控制操作演示 ===")
    
    # 调整氧气流量
    control_message = {
        "type": "control",
        "action": "adjust_oxygen",
        "params": {"flow_rate": 18500}
    }
    
    await client.send_message(control_message)
    print("已发送氧气流量调整请求...")


async def interactive_mode(client):
    """交互模式"""
    print("\n=== 交互模式 ===")
    print("输入命令 (输入 'help' 查看帮助, 'quit' 退出):")
    
    while True:
        try:
            command = input("\n> ").strip()
            
            if command == 'quit':
                break
            elif command == 'help':
                print("""
可用命令:
  start_monitoring  - 启动实时监测
  stop_monitoring   - 停止实时监测
  stage1           - 发送预热阶段通知
  stage2           - 发送脱碳阶段通知
  stage3           - 发送精炼阶段通知
  stage4           - 发送终点阶段通知
  adjust_oxygen    - 调整氧气流量
  query <内容>     - 发送查询请求
  quit             - 退出
""")
            elif command == 'start_monitoring':
                await client.send_message({"type": "start_monitoring"})
                
            elif command == 'stop_monitoring':
                await client.send_message({"type": "stop_monitoring"})
                
            elif command.startswith('stage'):
                stage_num = int(command[-1])
                stage_names = {1: "预热阶段", 2: "脱碳阶段", 3: "精炼阶段", 4: "终点阶段"}
                await client.send_message({
                    "type": "stage_notification",
                    "stage_info": {
                        "stage_number": stage_num,
                        "stage_name": stage_names.get(stage_num, f"第{stage_num}阶段")
                    }
                })
                
            elif command == 'adjust_oxygen':
                await client.send_message({
                    "type": "control",
                    "action": "adjust_oxygen",
                    "params": {"flow_rate": 19000}
                })
                
            elif command.startswith('query '):
                query_content = command[6:]
                await client.send_message({
                    "type": "query",
                    "content": query_content
                })
                
            else:
                print("未知命令，输入 'help' 查看帮助")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"命令执行错误: {e}")


async def main():
    """主函数"""
    print("吹炼智能体增强功能客户端演示")
    print("=" * 50)
    
    client = BlowingAgentClient()
    
    # 连接到服务器
    if not await client.connect():
        return
    
    try:
        # 启动消息接收任务
        receive_task = asyncio.create_task(client.receive_messages())
        
        # 等待连接确认
        await asyncio.sleep(1)
        
        # 选择演示模式
        print("\n选择演示模式:")
        print("1. 自动演示 (依次演示所有功能)")
        print("2. 交互模式 (手动输入命令)")
        
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            # 自动演示模式
            await demo_basic_query(client)
            await asyncio.sleep(3)
            
            await demo_control_operations(client)
            await asyncio.sleep(3)
            
            await demo_stage_notification(client)
            await asyncio.sleep(3)
            
            # 询问是否进行实时监测演示
            monitor_demo = input("\n是否演示实时监测功能？(会运行30秒) [y/N]: ").strip().lower()
            if monitor_demo == 'y':
                await demo_real_time_monitoring(client)
            
        elif choice == "2":
            # 交互模式
            await interactive_mode(client)
        
        # 取消接收任务
        receive_task.cancel()
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
