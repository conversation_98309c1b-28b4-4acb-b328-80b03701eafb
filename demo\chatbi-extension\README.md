# ChatBI Chrome Extension

一个智能的浏览器助手，支持实时对话和数据可视化。

## 功能特点

- 🎯 悬浮图标，随时调用
- 💬 流式对话，实时响应
- 📊 图表展示，数据可视化
- 🎤 语音输入支持
- 💡 智能推荐问题
- 🎨 精美的 UI 设计

## 快速开始

1. **启动后端服务**
   ```bash
   cd backend
   npm install
   npm start
   ```

2. **安装扩展**
   - 打开 Chrome 浏览器
   - 访问 `chrome://extensions/`
   - 开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `extension` 文件夹

3. **使用扩展**
   - 访问任意网页
   - 点击右下角的悬浮图标
   - 开始对话！

## 详细文档

- [API 文档](./API文档.md)
- [部署指南](./部署指南.md)

## 技术栈

- Chrome Extension API (Manifest V3)
- WebSocket 实时通信
- Node.js + Express 后端
- 原生 JavaScript (无框架依赖)