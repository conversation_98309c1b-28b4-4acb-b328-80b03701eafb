# 生成SQL的提示词
GENERATE_SQL_PROMPT = """
你是精通{dialect}的数据库专家，需根据用户问题和表结构信息，生成可直接执行的SQL查询语句。

### 可用表结构,及相关指标：
{table_info} 其中idx_name就是相关指标

核心规则：
1. 指标数据来源限定：
   - 指标匹配需完全一致（含单位及特殊符号）
   - 如果涉及查询相关指标，需要同时从indicator_info表中查询idx_benchmark(指标基准值)字段和abnormal_judgment_criteria(异常判断标准)，用来判断各个指标的状态是否正常。
   - idx_code 只用作indicator_info和daily_data两个表的关联字段，不作为查询筛选条件。
   - 异常判断标准要参考indicator_info中abnormal_judgment_criteria字段的描述。

2. 输出格式强制要求：
   - 仅返回SQL语句，无任何解释、注释或多余内容
   - 语句必须符合{dialect}语法规范，自动规避SQL注入风险

3. 时间处理规则：
   - 当调用表productdata时，未明确指定时间字段时，默认使用「装铁时间」作为时间条件字段
   - 提及"今天"、"当前日期"、"本月"、"今年"等相对时间时，需使用当前系统日期：{current_date}
   - 提及具体日期（如{current_date}）或当前日期时，若时间字段含时分秒，必须用范围查询：
     WHERE 装铁时间 BETWEEN '{current_date} 00:00:00' AND '{current_date} 23:59:59'

   - 禁止使用 WHERE 时间字段 = 'yyyy-mm-dd' 形式

4. 字段与条件约束：
   - WHERE子句中的所有过滤字段，必须包含在SELECT列表中
   - 有时间范围、日期条件或明确数据量要求，不使用LIMIT


### 用户问题：
{input}
"""

PROCESS_CLASSIFICATION_PROMPT = """
    你是一个工序分类专家，需要根据用户问题判断涉及的钢铁生产工序。

    可选工序：
    - 炼钢工序
    - 轧钢工序
    - 炼铁工序

    判断规则：
    1. 明确提及"炼钢"或炼钢相关指标（如:钢坯成本(元/t)、炼钢工序加工成本(元/t)、钢坯产量(t/d)、生白云石消耗(kg/t)、平均出钢温度(℃)、C窄成份控制合格率(%)、电耗(kwh/t)、氮气消耗(m3/t)、矿石单耗(kg/t)、高炉煤气消耗(m3/t)、钢铁料消耗(kg/t)、合金消耗(kg/t)、机物料消耗(元/t)、石灰消耗(kg/t)、铁水单耗(kg/t)、新水消耗(m3/t)、一倒C(0.04-0.08%)命中率(%)、一倒温度(1600-1655)命中率(%)、氧气消耗(kg/t)、平均中包温度(℃)、渣量(kg/t)、转炉煤气回收(m3/t)、铸坯收得率(%)、钢水成分不合(炉)、铁水Si稳定性(0.4-0.6)(%)、炼钢-质量指标-铁水温度、铁水温度(1300-1380℃)合格率(%)、炼钢-质量指标-钢水成分不合、铸坯夹杂等支数(支)、铸坯裂纹支数(支)、铸坯脱方支数(＞10mm）(支)、炼钢工序成本）属于炼钢工序
    2. 明确提及"轧钢"或轧钢相关指标（如:高线成本(元/t),轧钢工序实际降本额(元/t),1#高线工序加工成本(元/t),2#高线工序加工成本(元/t),高线工序加工成本(元/t),1#高线产量(t/d),2#高线产量(t/d),高线产量(t/d),1#高线钢坯单耗(t/t),1#高线高炉煤气消耗(m3/t),1#高线切头尾（粗中精）(t/t),2#高线钢坯单耗(t/t),2#高线高炉煤气消耗(m3/t),2#高线切头尾（粗中精）(t/t),电耗(kwh/t),氮气消耗(m3/t),高线钢坯单耗(t/t),机物料消耗(元/t),高线高炉煤气消耗(m3/t),高线切头尾（粗中精）(t/t),软水消耗(m3/t),新水消耗(m3/t),钢材合格率(%)）属于轧钢工序
    3. 明确提及"炼铁"或炼铁相关指标（如:1#高炉工序加工成本(元/t),2#高炉工序加工成本(元/t),高炉工序加工成本(元/t),铁水成本(元/t),1#高炉铁水产量(t/d),2#高炉铁水产量(t/d),高炉铁水产量(t/d),1#高炉富氧率(%),1#高炉返矿(kg/t),1#高炉焦比(kg/t),1#高炉矿耗(kg/t),1#高炉煤比(kg/t),1#高炉最低顶温(℃),1#高炉最低顶压(kpa),1#高炉利用系数,1#高炉CO利用率(%),1#高炉热风最低温度(℃),1#高炉综合入炉品位(%),2#高炉富氧率(%),2#高炉返矿(kg/t),2#高炉焦比(kg/t),2#高炉矿耗(kg/t),2#高炉煤比(kg/t),2#高炉最低顶温(℃),2#高炉最低顶压(kpa),2#高炉利用系数,2#高炉CO利用率(%),2#高炉热风最低温度(℃),2#高炉综合入炉品位(%),高炉富氧率(%),电耗(kwh/t),TRT发电量(kwh/t),高炉返矿(kg/t),高炉焦比(kg/t),高炉矿耗(kg/t),高炉煤比(kg/t),氮气(m3/t),新水消耗(m3/t),氧气(m3/t),高炉最低顶温(℃),高炉最低顶压(kpa),高炉利用系数,煤气回收(m3/t),高炉CO利用率(%),高炉热风最低温度(℃),高炉综合入炉品位(%),机物料消耗(元/t),焦炭反应后强度,焦炭反应性,焦炭M10(%),焦炭M40(%),铁水Si(0.4-0.6%)稳定率(%),铁水物理热(&gt;1460℃)合理率(%),1#高炉出铁次数(次),1#高炉铁量差(t),2#高炉出铁次数(次),2#高炉铁量差(t),高炉出铁次数(次),高炉铁量差(t),炼铁工序实际降本额(元/t)）属于炼铁工序
    4. 若无法明确判断，返回"未知"

    请严格返回以下结果之一，不得添加任何额外内容：
    ["炼钢工序"]
    ["轧钢工序"]
    ["炼铁工序"]
    ["未知"]

    问题：{input}
"""

# 分析结果的提示词
ANALYSIS_PROMPT = """
请根据问题和查询结果进行简要分析,查询结果是从数据库使用sql查出来的，所以第一列为序号，后面的列才是有用数据，另外你的回到要简明扼要，不要回答与问题无关的内容：
当数据里面有各种成本相关数据的时候，可以在回答里面体现运营成本相关的分析。
问题：{input}
查询结果：{data}
分析：
"""

# 问题分类提示词
QUESTION_CLASSIFICATION_PROMPT = """
你是一个精准的问题分类器，专门判断用户问题是否需要查询数据库才能回答。请严格遵循以下规则：

1. 需查询数据库的情况：
   - 明确提及以下具体指标（含单位）：钢坯成本(元/t)、炼钢工序加工成本(元/t)、钢坯产量(t/d)、生白云石消耗(kg/t)、平均出钢温度(℃)、C窄成份控制合格率(%)、电耗(kwh/t)、氮气消耗(m3/t)、矿石单耗(kg/t)、高炉煤气消耗(m3/t)、钢铁料消耗(kg/t)、合金消耗(kg/t)、机物料消耗(元/t)、石灰消耗(kg/t)、铁水单耗(kg/t)、新水消耗(m3/t)、一倒C(0.04-0.08%)命中率(%)、一倒温度(1600-1655℃)命中率(%)、氧气消耗(kg/t)、平均中包温度(℃)、渣量(kg/t)、转炉煤气回收(m3/t)、铸坯收得率(%)、钢水成分不合(炉)、铁水Si稳定性(0.4-0.6)(%)、炼钢-质量指标-铁水温度、铁水温度(1300-1380℃)合格率(%)、炼钢-质量指标-钢水成分不合、铸坯夹杂等支数(支)、铸坯裂纹支数(支)、铸坯脱方支数(＞10mm）(支)、炼钢工序成本、高线成本(元/t),轧钢工序实际降本额(元/t),1#高线工序加工成本(元/t),2#高线工序加工成本(元/t),高线工序加工成本(元/t),1#高线产量(t/d),2#高线产量(t/d),高线产量(t/d),1#高线钢坯单耗(t/t),1#高线高炉煤气消耗(m3/t),1#高线切头尾（粗中精）(t/t),2#高线钢坯单耗(t/t),2#高线高炉煤气消耗(m3/t),2#高线切头尾（粗中精）(t/t),电耗(kwh/t),氮气消耗(m3/t),高线钢坯单耗(t/t),机物料消耗(元/t),高线高炉煤气消耗(m3/t),高线切头尾（粗中精）(t/t),软水消耗(m3/t),新水消耗(m3/t),钢材合格率(%)、     1#高炉工序加工成本(元/t),2#高炉工序加工成本(元/t),高炉工序加工成本(元/t),铁水成本(元/t),1#高炉铁水产量(t/d),2#高炉铁水产量(t/d),高炉铁水产量(t/d),1#高炉富氧率(%),1#高炉返矿(kg/t),1#高炉焦比(kg/t),1#高炉矿耗(kg/t),1#高炉煤比(kg/t),1#高炉最低顶温(℃),1#高炉最低顶压(kpa),1#高炉利用系数,1#高炉CO利用率(%),1#高炉热风最低温度(℃),1#高炉综合入炉品位(%),2#高炉富氧率(%),2#高炉返矿(kg/t),2#高炉焦比(kg/t),2#高炉矿耗(kg/t),2#高炉煤比(kg/t),2#高炉最低顶温(℃),2#高炉最低顶压(kpa),2#高炉利用系数,2#高炉CO利用率(%),2#高炉热风最低温度(℃),2#高炉综合入炉品位(%),高炉富氧率(%),电耗(kwh/t),TRT发电量(kwh/t),高炉返矿(kg/t),高炉焦比(kg/t),高炉矿耗(kg/t),高炉煤比(kg/t),氮气(m3/t),新水消耗(m3/t),氧气(m3/t),高炉最低顶温(℃),高炉最低顶压(kpa),高炉利用系数,煤气回收(m3/t),高炉CO利用率(%),高炉热风最低温度(℃),高炉综合入炉品位(%),机物料消耗(元/t),焦炭反应后强度,焦炭反应性,焦炭M10(%),焦炭M40(%),铁水Si(0.4-0.6%)稳定率(%),铁水物理热(&gt;1460℃)合理率(%),1#高炉出铁次数(次),1#高炉铁量差(t),2#高炉出铁次数(次),2#高炉铁量差(t),高炉出铁次数(次),高炉铁量差(t),炼铁工序实际降本额(元/t)
   - 提及"指标"且指向上述具体内容时

2. 可直接回答的情况：
   - 未提及上述任何具体指标
   - 仅询问指标定义、计算方法等概念性内容（非具体数值）
   - 提及"指标"但未指向上述具体内容

请严格返回以下两种结果之一，不得添加任何额外内容：
["适合数据库查询"]
或
["直接回答"]

问题：{input}
"""

# 直接回答提示词
DIRECT_ANSWER_PROMPT = """
你是一个业务专家，知识库包含以下信息：
{knowledge_base}

请根据知识库和常识，直接回答用户问题：
问题：{input}
回答：
"""

# 每日主动分析提示词
INDICATOR_ANALYSIS_PROMPT = """
请分析昨天的炼钢指标数据 {result} ，根据“异常判断标准”进行指标异常分析。“指标影响因素”指的是当前指标与其他指标是相互依赖的，如果当前指标异常，就要从“指标影响因素”中找原因，并且要有数据支撑。
只回答有异常的之便，每一项异常回答格式为，xx指标异常，异常原因：当日指标值(具体数值)>/<基准值（具体数值），具体表现为：1.xx指标当日指标值为XXX,低于/高于基准值XXX；.... 最后再给出一些改进建议。
"""
