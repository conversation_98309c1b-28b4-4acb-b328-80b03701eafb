from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class InferenceType(str, Enum):
    """推理类型枚举"""
    BLOWING_STRATEGY = "blowing_strategy"
    FURNACE_ANALYSIS = "furnace_analysis"
    COMPLETION = "completion"
    CHAT = "chat"


class InferenceRequest(BaseModel):
    """推理请求模型"""
    type: InferenceType = Field(..., description="推理类型")
    prompt: Optional[str] = Field(None, description="文本提示")
    furnace_state: Optional[Dict[str, Any]] = Field(None, description="炉况状态")
    target_specs: Optional[Dict[str, float]] = Field(None, description="目标规格")
    context: Optional[List[Dict[str, Any]]] = Field(None, description="上下文信息")
    
    # 推理参数
    temperature: float = Field(0.7, description="温度参数")
    max_tokens: int = Field(512, description="最大生成长度")
    top_p: float = Field(0.9, description="Top-p采样")
    stream: bool = Field(False, description="是否流式输出")


class InferenceResponse(BaseModel):
    """推理响应模型"""
    request_id: str = Field(..., description="请求ID")
    type: InferenceType = Field(..., description="推理类型")
    result: Dict[str, Any] = Field(..., description="推理结果")
    usage: Dict[str, int] = Field(..., description="Token使用情况")
    latency_ms: float = Field(..., description="推理延迟(毫秒)")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
