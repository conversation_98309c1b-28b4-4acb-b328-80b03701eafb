# 钢铁大模型智能体WebSocket交互Demo

本Demo展示了经营决策智能体的交互功能，包括：
1. 人主动查询，智能体响应（聊天形式）
2. 智能体主动预警，推送异常信息

## 系统架构

```
前端客户端 <--WebSocket--> 后端服务
                            |
                            ├── 意图分类器
                            ├── 智能体编排器
                            └── 各类智能体
                                ├── 经营决策智能体
                                ├── 生产控制智能体
                                └── 质量分析智能体
```

## 快速开始

### 1. 启动后端服务

```bash
# 在项目根目录下
cd src
python -m backend.main
```

服务将在 `http://localhost:8000` 启动

### 2. 运行Demo客户端

在新的终端窗口中：

```bash
# 安装依赖（如果还没有安装）
pip install websockets aiohttp aioconsole colorama

# 运行基础客户端
cd demo/websocket
python client_async.py

# 或运行经营决策智能体专用测试客户端
python test_decision_agent.py
```

### 3. 测试功能

#### 3.1 测试查询功能

在客户端中输入各类查询，例如：

**经营决策类（重点功能）：**
- "查询昨天的吨钢毛利情况"
- "分析最近一周的钢坯成本变化趋势"
- "为什么吨钢毛利下降了？"
- "如何降低钢坯成本？"
- "对比本月和上月的生产成本"
- "预测下周的原料成本走势"
- "哪些产品的利润率最高？"
- "分析HRB400E的市场价格趋势"

**生产控制类：**
- "当前炉况如何？"
- "如何预防喷溅？"

**质量分析类：**
- "最近的质量合格率如何？"
- "分析质量不合格的原因"

#### 3.2 测试主动预警

在另一个终端运行监控测试工具：

```bash
python monitoring.py
```

选择选项2或3来触发KPI预警，观察客户端接收到的预警信息。

#### 3.3 经营决策智能体专项测试

**交互式测试：**
```bash
python test_decision_agent.py
# 进入交互模式后，可以输入：
# - help: 查看帮助
# - test: 运行预定义测试用例
# - alert: 运行KPI预警测试
# - 任意查询: 直接输入业务问题
```

**自动化测试：**
```bash
# 运行完整测试套件
python run_decision_tests.py

# 测试结果会保存为JSON文件
```

#### 3.4 测试用例示例

**数据查询类：**
- 需要查询数据库的问题会自动生成SQL并执行
- 例如："昨天的吨钢毛利是多少"、"本月钢坯成本趋势"

**概念解释类：**
- 不需要查询数据的问题会直接回答
- 例如："什么是吨钢毛利"、"如何计算成材率"

**异常分析类：**
- 自动识别异常指标并进行根因分析
- 例如："分析吨钢毛利异常的原因"、"为什么成本上升了"

## 消息格式

### 客户端发送格式

```json
{
    "type": "query",
    "content": "查询内容",
    "context": {}
}
```

### 服务端响应格式

**状态消息：**
```json
{
    "type": "status",
    "content": "正在分析..."
}
```

**内容消息：**
```json
{
    "type": "content",
    "content": "分析结果文本"
}
```

**预警消息：**
```json
{
    "type": "alert",
    "severity": "high",
    "title": "KPI异常预警",
    "content": "预警内容",
    "actions": [...]
}
```

## API端点

- WebSocket端点: `ws://localhost:8000/ws/agent`
- 查看活跃连接: `GET http://localhost:8000/api/connections`
- 广播消息: `POST http://localhost:8000/api/broadcast`

## 架构说明

### 关键组件

1. **ConnectionManager**: WebSocket连接管理器，负责管理所有客户端连接
2. **IntentClassifier**: 意图分类器，识别用户查询类型
3. **AgentOrchestrator**: 智能体编排器，协调各智能体工作
4. **DecisionAgent**: 经营决策智能体，处理经营相关查询

### 工作流程

1. 用户通过WebSocket发送查询
2. 意图分类器识别查询类型
3. 编排器路由到相应智能体
4. 智能体处理并返回流式响应
5. 后台监控系统持续检测KPI异常
6. 发现异常时主动推送预警

## 注意事项

1. 确保已安装所有依赖：`pip install -r requirements.txt`
2. 确保LLM配置正确（检查`.env`文件）
3. 首次运行可能需要下载模型，请耐心等待
4. WebSocket连接会定期发送心跳包保持连接
5. 经营决策智能体会定期（每5分钟）自动监控KPI指标
6. 测试脚本使用了colorama库来显示彩色输出，提高可读性

## 扩展开发

如需添加新的智能体或功能：

1. 在`src/agents/`下创建新的智能体
2. 在`IntentClassifier`中添加新的意图类型
3. 在`AgentOrchestrator`中添加路由逻辑
4. 更新前端客户端以支持新功能

### 经营决策智能体架构

经营决策智能体包含以下核心组件：

1. **SQL工具集** (`sql_tools.py`):
   - SQLGenerateTool: 自然语言转SQL
   - SQLExecuteTool: 执行SQL查询
   - DataAnalysisTool: 分析查询结果

2. **指标分析工具集** (`indicator_tools.py`):
   - IndicatorMonitorTool: 监控KPI指标
   - IndicatorAnalysisTool: 深度分析异常
   - RootCauseAnalysisTool: 根因分析

3. **工作模式**:
   - 主动模式：定期监控并预警
   - 引导模式：交互式深入分析
   - 被动模式：响应用户查询

## 故障排查

- 连接失败：检查服务是否正常启动
- 意图识别错误：查看服务端日志，调整关键词映射
- 响应缓慢：检查LLM服务状态和网络连接