from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import List, Optional


class Settings(BaseSettings):
    # Application
    APP_NAME: str = "Steel LLM Service"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # Security
    SECRET_KEY: str = "your-secret-key-here"
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost",
        "http://localhost:3000",
        "http://127.0.0.1",
        "http://127.0.0.1:3000",
    ]

    # 大模型相关
    ALIYUN_API_KEY: str = "sk-e067cac27b07405ca9b77fd7e15e2687"
    ALIYUN_URL: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    ALIYUN_MODEL_NAME: str = "qwen3-14b"
    LOCAL_URL: str = "http://***************:8188/v1"
    LOCAL_MODEL_NAME: str = "qwen3"
    USING_LOCAL_LLM: bool = True

    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"

    # Database
    DB_DIALECT: str = "mysql"
    DB_DRIVER: str = "mysqlconnector"
    # DB_USERNAME: str = "root"
    # DB_PASSWORD: str = "jishu_2023"
    # DB_HOST: str = "***************"
    # DB_PORT: str = "3416"
    # DB_NAME: str = "llmproduct1"
    DB_USERNAME: str = "AISteel"
    DB_PASSWORD: str = "AISteel@inspur_1"
    DB_HOST: str = "************"
    DB_PORT: str = "3306"
    DB_NAME: str = "llmproduct1"

    # 目标表
    TARGET_TABLES: List[str] = [
        "llmproduct1", "steelmaking_indicator_info", "steelmaking_daily_data",
        "ironmaking_indicator_info", "ironmaking_daily_data",
        "steelrolling_indicator_info", "steelrolling_daily_data"
    ]

    # 科大讯飞语音识别配置
    XFYUN_APP_ID: str = ""
    XFYUN_API_KEY: str = ""
    XFYUN_API_SECRET: str = ""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore"  # 如果 .env 文件中有 Settings 类未定义的变量，则忽略它们
    )


settings = Settings()
