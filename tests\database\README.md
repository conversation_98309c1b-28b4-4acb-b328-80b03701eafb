# 数据库模块测试

本目录包含了 `src/database` 模块的所有测试代码。

## 测试文件说明

- `conftest.py`: 测试配置和通用fixtures
- `test_connection.py`: 测试数据库连接相关功能
- `test_schema.py`: 测试表结构获取功能  
- `test_integration.py`: 集成测试，测试各模块协作
- `run_tests.py`: 测试运行脚本

## 运行测试

### 1. 运行所有测试

```bash
# 在项目根目录下
python -m pytest tests/database -v

# 或使用运行脚本
python tests/database/run_tests.py
```

### 2. 运行特定测试文件

```bash
# 只运行连接测试
python tests/database/run_tests.py --file test_connection.py

# 只运行表结构测试
python tests/database/run_tests.py --file test_schema.py

# 只运行集成测试
python tests/database/run_tests.py --file test_integration.py
```

### 3. 生成测试覆盖率报告

```bash
# 需要先安装 pytest-cov
pip install pytest-cov

# 运行测试并生成覆盖率报告
python tests/database/run_tests.py --coverage
```

## 测试覆盖范围

### test_connection.py
- ✓ 数据库URI生成
- ✓ 数据库引擎创建（成功/失败）
- ✓ 单例模式验证
- ✓ 数据库会话获取
- ✓ 连接测试（成功/失败）
- ✓ 特殊字符处理
- ✓ 不同数据库方言支持

### test_schema.py
- ✓ 获取所有表结构
- ✓ 获取指定表结构
- ✓ 处理不存在的表
- ✓ 混合表查询
- ✓ 异常处理和降级
- ✓ 详细列信息验证
- ✓ 默认表结构信息

### test_integration.py
- ✓ 完整工作流程测试
- ✓ 降级机制测试
- ✓ SQL执行模拟
- ✓ 连接池配置验证
- ✓ 事务处理测试
- ✓ 错误处理测试

## Mock 策略

测试使用了完整的Mock策略，不需要真实的数据库连接：

1. **Mock Engine**: 模拟SQLAlchemy引擎
2. **Mock Inspector**: 模拟数据库元数据检查
3. **Mock Session**: 模拟数据库会话
4. **Mock Settings**: 模拟配置对象

## 添加新测试

如需添加新的测试：

1. 在相应的测试文件中添加测试类或方法
2. 使用 `conftest.py` 中提供的fixtures
3. 遵循命名规范：`test_<功能描述>`
4. 确保测试独立且可重复运行

## 调试测试

如果测试失败，可以使用以下方法调试：

```bash
# 显示详细的失败信息
pytest tests/database -vv --tb=long

# 在第一个失败处停止
pytest tests/database -x

# 运行包含特定关键字的测试
pytest tests/database -k "connection"

# 显示print语句输出
pytest tests/database -s
```