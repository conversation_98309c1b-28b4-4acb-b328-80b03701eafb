:root {
  --chatbi-primary: #5c6bc0;
  --chatbi-primary-dark: #3949ab;
  --chatbi-primary-light: #7986cb;
  --chatbi-secondary: #ff6b6b;
  --chatbi-bg: #ffffff;
  --chatbi-surface: #f5f5f5;
  --chatbi-text: #333333;
  --chatbi-text-secondary: #666666;
  --chatbi-border: #e0e0e0;
  --chatbi-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  --chatbi-radius: 12px;
}

#chatbi-floating-icon {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, var(--chatbi-primary) 0%, var(--chatbi-primary-dark) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--chatbi-shadow);
  transition: all 0.3s ease;
  z-index: 999999;
  color: white;
}

#chatbi-floating-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(92, 107, 192, 0.4);
}

#chatbi-floating-icon svg {
  width: 28px;
  height: 28px;
}

#chatbi-window {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 450px;
  height: calc(100vh - 40px);
  max-height: calc(100vh - 40px);
  background: var(--chatbi-bg);
  border-radius: var(--chatbi-radius);
  box-shadow: var(--chatbi-shadow);
  display: flex;
  flex-direction: column;
  z-index: 999998;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  transition: all 0.3s ease;
  overflow: hidden;
}

#chatbi-window.chatbi-hidden {
  opacity: 0;
  pointer-events: none;
  transform: translateY(20px) scale(0.95);
}

.chatbi-header {
  background: linear-gradient(135deg, var(--chatbi-primary) 0%, var(--chatbi-primary-dark) 100%);
  color: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: var(--chatbi-radius) var(--chatbi-radius) 0 0;
  flex-shrink: 0;
}

.chatbi-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.chatbi-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.chatbi-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.chatbi-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: var(--chatbi-surface);
}

.chatbi-message {
  margin-bottom: 16px;
  animation: messageSlide 0.3s ease;
}

@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chatbi-message.chatbi-user {
  display: flex;
  justify-content: flex-end;
}

.chatbi-message.chatbi-assistant,
.chatbi-message.chatbi-system {
  display: flex;
  justify-content: flex-start;
}

.chatbi-message-content {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  position: relative;
}

.chatbi-user .chatbi-message-content {
  background: var(--chatbi-primary);
  color: white;
  border-bottom-right-radius: 4px;
}

.chatbi-assistant .chatbi-message-content {
  background: white;
  color: var(--chatbi-text);
  border: 1px solid var(--chatbi-border);
  border-bottom-left-radius: 4px;
  text-align: left;
}

.chatbi-system .chatbi-message-content {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
  font-size: 14px;
}

.chatbi-message-time {
  font-size: 11px;
  color: var(--chatbi-text-secondary);
  margin-top: 4px;
  text-align: right;
}

.chatbi-assistant .chatbi-message-time {
  text-align: left;
}

.chatbi-message-content pre {
  background: var(--chatbi-surface);
  border: 1px solid var(--chatbi-border);
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  overflow-x: auto;
}

.chatbi-message-content code {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
}

.chatbi-chart {
  background: white;
  border: 1px solid var(--chatbi-border);
  border-radius: 8px;
  padding: 16px;
  margin: 8px 0;
  text-align: center;
  color: var(--chatbi-primary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.chatbi-chart:hover {
  background: var(--chatbi-surface);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.chatbi-recommendations {
  padding: 8px 16px;
  background: white;
  border-top: 1px solid var(--chatbi-border);
  max-height: 80px;
  overflow-y: auto;
  flex-shrink: 0;
}

.chatbi-recommendations-title {
  font-size: 12px;
  color: var(--chatbi-text-secondary);
  margin-bottom: 6px;
  font-weight: 500;
}

.chatbi-recommendations-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.chatbi-recommendation {
  background: var(--chatbi-surface);
  border: 1px solid var(--chatbi-border);
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 12px;
  color: var(--chatbi-text);
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.chatbi-recommendation:hover {
  background: var(--chatbi-primary);
  color: white;
  border-color: var(--chatbi-primary);
  transform: translateY(-2px);
}

.chatbi-input-container {
  padding: 12px 16px;
  background: white;
  border-top: 1px solid var(--chatbi-border);
  flex-shrink: 0;
}

.chatbi-input-wrapper {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

#chatbi-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid var(--chatbi-border);
  border-radius: 8px;
  resize: none;
  font-family: inherit;
  font-size: 14px;
  transition: all 0.2s;
  box-sizing: border-box;
  min-height: 38px;
  max-height: 100px;
  overflow-y: auto;
}

#chatbi-input:focus {
  outline: none;
  border-color: var(--chatbi-primary);
  box-shadow: 0 0 0 3px rgba(92, 107, 192, 0.1);
}

.chatbi-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.chatbi-voice-btn {
  background: var(--chatbi-surface);
  border: 1px solid var(--chatbi-border);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.2s;
}

.chatbi-voice-btn:hover {
  background: var(--chatbi-primary);
  border-color: var(--chatbi-primary);
  transform: scale(1.1);
}

.chatbi-voice-btn.recording {
  background: var(--chatbi-secondary);
  border-color: var(--chatbi-secondary);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}

.chatbi-tts-btn {
  background: var(--chatbi-surface);
  border: 1px solid var(--chatbi-border);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.2s;
}

.chatbi-tts-btn:hover {
  background: var(--chatbi-primary);
  border-color: var(--chatbi-primary);
  transform: scale(1.1);
}

.chatbi-tts-btn.active {
  background: var(--chatbi-primary);
  color: white;
  border-color: var(--chatbi-primary);
}

.chatbi-tts-btn.speaking {
  background: var(--chatbi-secondary);
  color: white;
  border-color: var(--chatbi-secondary);
  animation: pulse 1s infinite;
}

.chatbi-send-btn {
  background: var(--chatbi-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.chatbi-send-btn:hover {
  background: var(--chatbi-primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(92, 107, 192, 0.3);
}

.chatbi-send-btn:active {
  transform: translateY(0);
}

.chatbi-messages::-webkit-scrollbar {
  width: 6px;
}

.chatbi-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chatbi-messages::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.chatbi-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

@media (max-width: 480px) {
  #chatbi-window {
    width: calc(100vw - 20px);
    height: calc(100vh - 40px);
    right: 10px;
    bottom: 20px;
  }
}