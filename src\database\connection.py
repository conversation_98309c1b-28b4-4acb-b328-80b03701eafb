"""数据库连接管理"""
import sqlalchemy
from sqlalchemy.orm import sessionmaker, Session
from typing import Optional
from config.settings import settings
from config.logging import logger


def get_db_uri() -> str:
    """生成数据库连接URI"""
    from urllib.parse import quote_plus
    encoded_password = quote_plus(settings.DB_PASSWORD)
    return f"{settings.DB_DIALECT}+{settings.DB_DRIVER}://{settings.DB_USERNAME}:{encoded_password}@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"


def create_engine() -> sqlalchemy.Engine:
    """创建数据库引擎"""
    try:
        db_uri = get_db_uri()
        # 创建引擎，配置连接池
        engine = sqlalchemy.create_engine(
            db_uri,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,  # 连接前先ping一下确保连接有效
            pool_recycle=3600,  # 每小时回收连接
        )
        logger.info(
            f"Database engine created successfully for {settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"
        )
        return engine
    except Exception as e:
        logger.error(f"Failed to create database engine: {e}")
        raise Exception(f"数据库连接失败: {e}")


# 全局引擎实例
_engine: Optional[sqlalchemy.Engine] = None


def get_engine() -> sqlalchemy.Engine:
    """获取数据库引擎（单例模式）"""
    global _engine
    if _engine is None:
        _engine = create_engine()
    return _engine


def get_db_session() -> Session:
    """获取数据库会话"""
    engine = get_engine()
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()


def test_connection() -> bool:
    """测试数据库连接"""
    try:
        engine = get_engine()
        with engine.connect() as conn:
            result = conn.execute(sqlalchemy.text("SELECT 1"))
            result.fetchone()
        logger.info("Database connection test successful")
        return True
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False


if __name__ == "__main__":
    # 如果直接运行此文件，则测试数据库连接
    if test_connection():
        print("数据库连接测试成功")
    else:
        print("数据库连接测试失败")

    # 查询数据库包含哪些表
    engine = get_engine()
    inspector = sqlalchemy.inspect(engine)
    tables = inspector.get_table_names()
    print("数据库中的表:")
    for table in tables:
        print(f"- {table}")
