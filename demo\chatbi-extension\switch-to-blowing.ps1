# PowerShell script to switch to blowing agent plugin
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Switch to Blowing Agent Plugin" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Change to extension directory
$extensionPath = Join-Path $PSScriptRoot "extension"
Set-Location $extensionPath

Write-Host "Backing up original files..." -ForegroundColor Yellow

# Backup original files
if (Test-Path "manifest.json") {
    Copy-Item "manifest.json" "manifest-original.json" -Force
    Write-Host "[OK] Backup manifest.json" -ForegroundColor Green
}

if (Test-Path "content.js") {
    Copy-Item "content.js" "content-original.js" -Force
    Write-Host "[OK] Backup content.js" -ForegroundColor Green
}

if (Test-Path "styles.css") {
    Copy-Item "styles.css" "styles-original.css" -Force
    Write-Host "[OK] Backup styles.css" -ForegroundColor Green
}

Write-Host ""
Write-Host "Switching to blowing agent files..." -ForegroundColor Yellow

# Switch to blowing agent files
Copy-Item "manifest-blowing.json" "manifest.json" -Force
Write-Host "[OK] Switch manifest.json" -ForegroundColor Green

Copy-Item "content-blowing.js" "content.js" -Force
Write-Host "[OK] Switch content.js" -ForegroundColor Green

Copy-Item "styles-blowing.css" "styles.css" -Force
Write-Host "[OK] Switch styles.css" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Switch Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Open Chrome browser" -ForegroundColor White
Write-Host "2. Go to chrome://extensions/" -ForegroundColor White
Write-Host "3. Find the plugin and click 'Reload' button" -ForegroundColor White
Write-Host "4. Start backend server:" -ForegroundColor White
Write-Host "   conda activate AISteelMaking" -ForegroundColor Gray
Write-Host "   python -m uvicorn src.backend.main:app --host 0.0.0.0 --port 9001 --reload" -ForegroundColor Gray
Write-Host ""
Read-Host "Press Enter to continue"
