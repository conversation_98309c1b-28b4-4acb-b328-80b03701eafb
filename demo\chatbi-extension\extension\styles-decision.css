/* 浮动图标样式 */
#chatbi-floating-icon {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: all 0.3s ease;
}

#chatbi-floating-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

#chatbi-floating-icon svg {
  width: 28px;
  height: 28px;
  color: white;
}

/* 聊天窗口样式 */
#chatbi-window {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 380px;
  height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  z-index: 10000;
  transition: all 0.3s ease;
  overflow: hidden;
}

#chatbi-window.chatbi-hidden {
  display: none;
}

/* 头部样式 */
.chatbi-header {
  background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px 12px 0 0;
}

.chatbi-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.chatbi-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.chatbi-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 消息容器样式 */
.chatbi-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f5f5f5;
  /* 使用硬件加速优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
}

.chatbi-message {
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
}

.chatbi-message.chatbi-user {
  align-items: flex-end;
}

.chatbi-message.chatbi-assistant,
.chatbi-message.chatbi-system {
  align-items: flex-start;
}

.chatbi-message-content {
  background: white;
  padding: 12px 16px;
  border-radius: 18px;
  max-width: 85%;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  /* 防止内容更新时的布局抖动 */
  min-height: 20px;
  transition: none; /* 禁用过渡效果以避免闪烁 */
}

.chatbi-user .chatbi-message-content {
  background: #1e88e5;
  color: white;
}

.chatbi-system .chatbi-message-content {
  background: #e0e0e0;
  color: #666;
  font-style: italic;
  font-size: 14px;
}

.chatbi-message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  margin-left: 12px;
  margin-right: 12px;
}

/* 推荐问题样式 */
.chatbi-recommendations {
  padding: 12px 20px;
  background: #fafafa;
  border-top: 1px solid #e0e0e0;
}

.chatbi-recommendations-title {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
}

.chatbi-recommendations-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.chatbi-recommendation {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
}

.chatbi-recommendation:hover {
  background: #1e88e5;
  color: white;
  border-color: #1e88e5;
}

/* 输入区域样式 */
.chatbi-input-container {
  padding: 16px;
  background: white;
  border-top: 1px solid #e0e0e0;
}

.chatbi-command-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.chatbi-cmd-btn {
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
}

.chatbi-cmd-btn:hover {
  background: #e0e0e0;
  color: #333;
}

.chatbi-input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

#chatbi-input {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 10px 16px;
  font-size: 14px;
  resize: none;
  outline: none;
  transition: border-color 0.2s;
  max-height: 120px;
  overflow-y: auto;
}

#chatbi-input:focus {
  border-color: #1e88e5;
}

.chatbi-buttons {
  display: flex;
  gap: 8px;
}

.chatbi-send-btn {
  background: #1e88e5;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chatbi-send-btn:hover {
  background: #1565c0;
}

.chatbi-send-btn:active {
  transform: scale(0.95);
}

/* 代码块样式 */
.chatbi-message-content pre {
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  overflow-x: auto;
}

.chatbi-message-content code {
  font-family: 'Courier New', Courier, monospace;
  font-size: 13px;
}

/* 表格和图表容器样式 */
.chatbi-chart-container {
  margin: 10px 0;
}

/* 滚动条样式 */
.chatbi-messages::-webkit-scrollbar,
#chatbi-input::-webkit-scrollbar {
  width: 6px;
}

.chatbi-messages::-webkit-scrollbar-track,
#chatbi-input::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.chatbi-messages::-webkit-scrollbar-thumb,
#chatbi-input::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.chatbi-messages::-webkit-scrollbar-thumb:hover,
#chatbi-input::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 响应式设计 */
@media (max-width: 480px) {
  #chatbi-window {
    width: 100%;
    height: 100%;
    bottom: 0;
    right: 0;
    border-radius: 0;
  }
  
  .chatbi-header {
    border-radius: 0;
  }
}