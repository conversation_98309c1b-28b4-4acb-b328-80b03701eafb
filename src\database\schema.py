"""数据库表结构相关功能"""
import sqlalchemy
from typing import List, Optional
from config.settings import settings
from config.logging import logger
from .connection import get_engine


def get_table_info(tables: Optional[List[str]] = None) -> str:
    """
    获取数据库表结构信息（支持指定表名列表）

    参数：
        tables: 列表，指定需要获取结构的表名（如 ["table1", "table2"]），默认None则获取所有表
    
    返回：
        表结构信息的字符串描述
    """
    try:
        engine = get_engine()
        inspector = sqlalchemy.inspect(engine)
        all_tables = inspector.get_table_names()  # 获取数据库中所有表名

        # 筛选需要的表（如果指定了tables，则只保留存在的表）
        target_tables = tables if tables else all_tables
        # 过滤掉不存在的表（避免报错）
        valid_tables = [table for table in target_tables if table in all_tables]

        if not valid_tables:
            return "指定的表不存在或未传入表名"

        table_info = []
        for table in valid_tables:
            columns = inspector.get_columns(table)
            table_info.append(f"表名：{table}")
            table_info.append("字段：")
            for col in columns:
                table_info.append(f"- {col['name']}（类型：{col['type']}）")

        return "\n".join(table_info)

    except Exception as e:
        logger.error(f"获取表结构信息失败: {e}")
        # 返回默认的表结构信息
        return get_default_table_info()


def get_default_table_info() -> str:
    """获取默认的表结构信息（当数据库连接失败时使用）"""
    return """
    -- 炼铁工序每日生产指标完成数据
    表名：ironmaking_daily_data
    字段：
    - id（类型：int(11)，主键）-- 自增主键，非空
    - idx_code（类型：varchar(50)）-- 指标编码，关联ironmaking_indicator_info表
    - time_flag（类型：date）-- 记录日期
    - idx_value（类型：float）-- 当日指标值
    - idx_count_value（类型：float）-- 月度累计平均值
    - create_time（类型：date）-- 数据插入日期
    存储引擎：InnoDB，字符集：utf8mb4，排序规则：utf8mb4_unicode_ci

    -- 炼铁工序各类指标的基础信息及标准值
    表名：ironmaking_indicator_info
    字段：
    - idx_code（类型：varchar(50)，主键）-- 指标编码，唯一标识，非空
    - idx_name（类型：varchar(100)）-- 指标名称
    - idx_benchmark（类型：varchar(50)）-- 指标基准值
    - level1（类型：float）-- 一级标准值
    - level2（类型：float）-- 二级标准值
    - level3（类型：float）-- 三级标准值
    - is_key_indicator（类型：tinyint(1)，默认0）-- 是否重点指标，1表示是，0表示否
    存储引擎：InnoDB，字符集：utf8mb4，排序规则：utf8mb4_unicode_ci

    -- 炼钢工序每日生产指标完成数据
    表名：steelmaking_daily_data
    字段：
    - id（类型：int(11)，主键）-- 自增主键，非空
    - idx_code（类型：varchar(50)）-- 指标编码，关联steelmaking_indicator_info表
    - time_flag（类型：date）-- 记录日期
    - idx_value（类型：float）-- 当日指标值
    - idx_count_value（类型：float）-- 月度累计平均值
    - create_time（类型：date）-- 数据插入日期
    存储引擎：InnoDB，字符集：utf8mb4，排序规则：utf8mb4_unicode_ci

    -- 炼钢工序各类指标的基础信息及标准值
    表名：steelmaking_indicator_info
    字段：
    - idx_code（类型：varchar(50)，主键）-- 指标编码，唯一标识，非空
    - idx_name（类型：varchar(100)）-- 指标名称
    - idx_benchmark（类型：varchar(50)）-- 指标基准值
    - influencing_factors（类型：varchar(500)）-- 指标的影响因素
    - level1（类型：float）-- 一级标准值
    - level2（类型：float）-- 二级标准值
    - is_key_indicator（类型：tinyint(1)，默认0）-- 是否重点指标，1表示是，0表示否
    - abnormal_judgment_criteria（类型：varchar(255)）-- 异常判断标准
    存储引擎：InnoDB，字符集：utf8mb4，排序规则：utf8mb4_unicode_ci

    -- 轧钢工序每日生产指标完成数据
    表名：steelrolling_daily_data
    字段：
    - id（类型：int(11)，主键）-- 自增主键，非空
    - idx_code（类型：varchar(50)）-- 指标编码，关联steelrolling_indicator_info表
    - time_flag（类型：date）-- 记录日期
    - idx_value（类型：float）-- 当日指标值
    - idx_count_value（类型：float）-- 月度累计平均值
    - create_time（类型：date）-- 数据插入日期
    存储引擎：InnoDB，字符集：utf8mb4，排序规则：utf8mb4_unicode_ci

    -- 轧钢工序各类指标的基础信息及标准值
    表名：steelrolling_indicator_info
    字段：
    - idx_code（类型：varchar(50)，主键）-- 指标编码，唯一标识，非空
    - idx_name（类型：varchar(100)）-- 指标名称
    - idx_benchmark（类型：varchar(50)）-- 指标基准值
    - level1（类型：float）-- 一级标准值
    - level2（类型：float）-- 二级标准值
    - level3（类型：float）-- 三级标准值
    - is_key_indicator（类型：tinyint(1)，默认0）-- 是否重点指标，1表示是，0表示否
    存储引擎：InnoDB，字符集：utf8mb4，排序规则：utf8mb4_unicode_ci
    """

# 炼钢SQL
DAILY_STEEL_MAKING_SQL = """
SELECT 
    sdd.time_flag as 指标日期,
    sii.idx_name as 指标名称,
    sii.idx_benchmark as 当日指标值,
    sdd.idx_value as 指标基准值,
    sii.level1 as 一级标准值,
    sii.level2 as 二级标准值,
    sii.abnormal_judgment_criteria as 异常判断标准,
    sii.influencing_factors as 指标影响因素
FROM 
    steelmaking_daily_data sdd
JOIN 
    steelmaking_indicator_info sii ON sdd.idx_code = sii.idx_code
WHERE 
   # sdd.time_flag = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
   sdd.time_flag = '2025-07-05'
   AND
   sii.is_key_indicator=1
"""
