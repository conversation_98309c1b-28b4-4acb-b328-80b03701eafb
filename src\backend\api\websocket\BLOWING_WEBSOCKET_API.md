# 吹炼智能体 WebSocket API 文档

## 概述

吹炼智能体 WebSocket 接口提供实时的阶段播报和监控功能。客户端可以通过发送指令来控制吹炼过程的播报和监控。

**WebSocket 端点**: `/ws/agent/blowing`

## 核心功能

### 1. 统一的开始/结束控制
- **开始指令**: 同时启动阶段播报和实时监控
- **结束指令**: 同时停止阶段播报和实时监控
- **实时监控**: 每60秒自动生成监控报告
- **阶段播报**: 客户端发送阶段指令时进行播报

### 2. 时间参数支持
- 支持指定时间参数或使用当前时间
- 时间格式: `YYYY-MM-DD HH:MM:SS`

## 消息格式

所有消息都使用 JSON 格式，包含 `type` 字段指定消息类型。

## 客户端发送的消息类型

### 1. 启动吹炼过程
```json
{
    "type": "start_process",
    "start_time": "2025-08-13 08:00:00"  // 可选，不提供则使用当前时间
}
```

**功能**: 启动实时监控，准备接收阶段播报指令

### 2. 停止吹炼过程
```json
{
    "type": "stop_process"
}
```

**功能**: 停止实时监控和阶段播报

### 3. 阶段播报
```json
{
    "type": "stage_broadcast",
    "stage_info": {
        "stage_number": 1,
        "stage_name": "准备阶段"
    },
    "broadcast_time": "2025-08-13 08:00:00"  // 可选，不提供则使用当前时间
}
```

**功能**: 执行指定阶段的播报

### 4. 查询状态
```json
{
    "type": "status"
}
```

**功能**: 查询当前监控状态和阶段信息

### 5. 心跳检测
```json
{
    "type": "ping"
}
```

**功能**: 保持连接活跃

### 6. 退出连接
```json
{
    "type": "exit"
}
```

**功能**: 优雅地关闭连接

## 服务器响应的消息类型

### 1. 连接成功
```json
{
    "type": "connected",
    "client_id": "uuid",
    "agent": "blowing",
    "message": "欢迎使用吹炼智能体！支持阶段播报和实时监控功能。"
}
```

### 2. 吹炼过程启动成功
```json
{
    "type": "process_started",
    "agent": "blowing",
    "message": "吹炼过程已启动，实时监控开始运行",
    "monitoring_start_time": "2025-08-13T08:00:00",
    "monitoring_interval": "60秒"
}
```

### 3. 吹炼过程停止成功
```json
{
    "type": "process_stopped",
    "agent": "blowing",
    "message": "吹炼过程已停止，实时监控已结束"
}
```

### 4. 阶段播报结果
```json
{
    "type": "stage_broadcast_result",
    "agent": "blowing",
    "success": true,
    "stage_info": {
        "stage_number": 1,
        "stage_name": "准备阶段"
    },
    "report": {
        "timestamp": "2025-08-13T08:00:00",
        "stage_name": "准备阶段",
        "stage_type": "first_stage",
        "broadcast_content": "播报内容...",
        "blow_time": "0",
        "current_parameters": {...},
        "material_reminder": "加料提醒...",
        "splash_info": null
    }
}
```

### 5. 状态查询响应
```json
{
    "type": "status_response",
    "agent": "blowing",
    "monitoring_active": true,
    "current_stage": {
        "stage_number": 2,
        "stage_name": "脱碳阶段"
    }
}
```

### 6. 心跳响应
```json
{
    "type": "pong",
    "agent": "blowing"
}
```

### 7. 错误响应
```json
{
    "type": "error",
    "agent": "blowing",
    "message": "错误描述"
}
```

## 使用流程

### 典型的吹炼过程流程

1. **建立连接**
   ```
   客户端 -> 服务器: WebSocket 连接
   服务器 -> 客户端: connected 消息
   ```

2. **启动吹炼过程**
   ```
   客户端 -> 服务器: start_process
   服务器 -> 客户端: process_started
   ```

3. **阶段播报循环**
   ```
   客户端 -> 服务器: stage_broadcast (阶段1)
   服务器 -> 客户端: stage_broadcast_result
   
   // 实时监控每60秒自动运行
   
   客户端 -> 服务器: stage_broadcast (阶段2)
   服务器 -> 客户端: stage_broadcast_result
   
   // 继续其他阶段...
   ```

4. **停止吹炼过程**
   ```
   客户端 -> 服务器: stop_process
   服务器 -> 客户端: process_stopped
   ```

5. **关闭连接**
   ```
   客户端 -> 服务器: exit
   服务器 -> 客户端: info (再见消息)
   ```

## 实时监控说明

- **自动运行**: 启动吹炼过程后，实时监控每60秒自动生成一次监控报告
- **数据分析**: 监控会分析数据变化趋势，包括参数对比和变化分析
- **时间限制**: 监控只分析指定开始时间之前的数据
- **专业分析**: 使用大模型生成专业的炼钢分析和操作建议

## 阶段播报说明

- **按需触发**: 只有客户端发送阶段指令时才进行播报
- **时间参数**: 支持指定播报时间，查询该时间之前的最新数据
- **阶段类型**: 
  - 第一阶段: 播报完整初始数据和加料计划
  - 后续阶段: 播报实时参数和操作建议
  - 结束阶段: 播报吹炼结束信息

## 错误处理

常见错误情况：
- 消息格式错误
- 阶段信息缺失
- 时间格式错误
- 数据库连接问题
- 智能体初始化失败

## 示例代码

参考 `blowing_client_example.py` 文件中的完整示例代码。

## 注意事项

1. **连接管理**: 客户端断开连接时，服务器会自动清理资源并停止监控
2. **状态同步**: 可以随时使用 `status` 消息查询当前状态
3. **时间格式**: 时间参数必须使用 `YYYY-MM-DD HH:MM:SS` 格式
4. **资源清理**: 服务器会在连接断开时自动停止监控并清理资源
