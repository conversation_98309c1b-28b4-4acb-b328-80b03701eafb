"""测试数据库表结构获取功能"""
import pytest
from unittest.mock import Mock, patch
import sqlalchemy

from database.schema import get_table_info, get_default_table_info


class TestTableInfo:
    """测试表结构信息获取"""
    
    def test_get_table_info_all_tables(self, mock_engine, mock_inspector):
        """测试获取所有表的结构信息"""
        with patch("database.schema.get_engine") as mock_get_engine:
            mock_get_engine.return_value = mock_engine
            
            with patch("sqlalchemy.inspect") as mock_inspect:
                mock_inspect.return_value = mock_inspector
                
                # 不传入tables参数，获取所有表
                result = get_table_info()
                
                # 验证inspect被正确调用
                mock_inspect.assert_called_once_with(mock_engine)
                mock_inspector.get_table_names.assert_called_once()
                
                # 验证结果包含所有表的信息
                assert "表名：steelmaking_indicator_info" in result
                assert "表名：steelmaking_daily_data" in result
                assert "表名：ironmaking_indicator_info" in result
                assert "表名：ironmaking_daily_data" in result
                assert "表名：steelrolling_indicator_info" in result
                assert "表名：steelrolling_daily_data" in result
                
                # 验证字段信息
                assert "idx_code（类型：VARCHAR(50)）" in result
                assert "idx_name（类型：VARCHAR(100)）" in result
                assert "time_flag（类型：DATE）" in result
                assert "idx_value（类型：DECIMAL(10,2)）" in result
    
    def test_get_table_info_specific_tables(self, mock_engine, mock_inspector):
        """测试获取指定表的结构信息"""
        with patch("database.schema.get_engine") as mock_get_engine:
            mock_get_engine.return_value = mock_engine
            
            with patch("sqlalchemy.inspect") as mock_inspect:
                mock_inspect.return_value = mock_inspector
                
                # 只获取炼钢相关的表
                tables = ["steelmaking_indicator_info", "steelmaking_daily_data"]
                result = get_table_info(tables)
                
                # 验证只包含指定表的信息
                assert "表名：steelmaking_indicator_info" in result
                assert "表名：steelmaking_daily_data" in result
                
                # 不应包含其他表
                assert "表名：ironmaking_indicator_info" not in result
                assert "表名：ironmaking_daily_data" not in result
                assert "表名：steelrolling_indicator_info" not in result
                assert "表名：steelrolling_daily_data" not in result
    
    def test_get_table_info_nonexistent_tables(self, mock_engine, mock_inspector):
        """测试获取不存在的表"""
        with patch("database.schema.get_engine") as mock_get_engine:
            mock_get_engine.return_value = mock_engine
            
            with patch("sqlalchemy.inspect") as mock_inspect:
                mock_inspect.return_value = mock_inspector
                
                # 请求不存在的表
                tables = ["nonexistent_table1", "nonexistent_table2"]
                result = get_table_info(tables)
                
                assert result == "指定的表不存在或未传入表名"
    
    def test_get_table_info_mixed_tables(self, mock_engine, mock_inspector):
        """测试获取部分存在的表"""
        with patch("database.schema.get_engine") as mock_get_engine:
            mock_get_engine.return_value = mock_engine
            
            with patch("sqlalchemy.inspect") as mock_inspect:
                mock_inspect.return_value = mock_inspector
                
                # 请求部分存在的表
                tables = ["steelmaking_indicator_info", "nonexistent_table", "ironmaking_daily_data"]
                result = get_table_info(tables)
                
                # 只包含存在的表
                assert "表名：steelmaking_indicator_info" in result
                assert "表名：ironmaking_daily_data" in result
                assert "nonexistent_table" not in result
    
    def test_get_table_info_exception_handling(self, mock_settings):
        """测试异常处理"""
        with patch("database.schema.get_engine") as mock_get_engine:
            mock_get_engine.side_effect = Exception("Database connection failed")
            
            # 应该返回默认表信息
            result = get_table_info()
            
            # 验证返回默认表信息
            assert "炼钢指标信息表" in result
            assert "炼钢日数据表" in result
            assert "轧钢指标信息表" in result
            assert "轧钢日数据表" in result
            assert "炼铁指标信息表" in result
            assert "炼铁日数据表" in result
    
    def test_get_table_info_column_details(self, mock_engine, mock_inspector):
        """测试获取详细的列信息"""
        with patch("database.schema.get_engine") as mock_get_engine:
            mock_get_engine.return_value = mock_engine
            
            with patch("sqlalchemy.inspect") as mock_inspect:
                mock_inspect.return_value = mock_inspector
                
                tables = ["steelmaking_indicator_info"]
                result = get_table_info(tables)
                
                # 验证炼钢指标信息表的所有字段
                expected_columns = [
                    "idx_code（类型：VARCHAR(50)）",
                    "idx_name（类型：VARCHAR(100)）",
                    "idx_benchmark（类型：VARCHAR(50)）",
                    "level1（类型：DECIMAL(10,2)）",
                    "level2（类型：DECIMAL(10,2)）",
                    "abnormal_judgment_criteria（类型：TEXT）",
                    "influencing_factors（类型：TEXT）",
                    "is_key_indicator（类型：TINYINT）"
                ]
                
                for column in expected_columns:
                    assert column in result


class TestDefaultTableInfo:
    """测试默认表结构信息"""
    
    def test_get_default_table_info(self):
        """测试获取默认表结构信息"""
        result = get_default_table_info()
        
        # 验证包含所有默认表
        tables = [
            "steelmaking_indicator_info",
            "steelmaking_daily_data",
            "steelrolling_indicator_info",
            "steelrolling_daily_data",
            "ironmaking_indicator_info",
            "ironmaking_daily_data"
        ]
        
        for table in tables:
            assert f"表名：{table}" in result
        
        # 验证包含表结构说明
        assert "炼钢指标信息表" in result
        assert "炼钢日数据表" in result
        assert "轧钢指标信息表" in result
        assert "轧钢日数据表" in result
        assert "炼铁指标信息表" in result
        assert "炼铁日数据表" in result
        
        # 验证字段信息格式
        assert "idx_code（类型：VARCHAR(50)）-- 主键" in result
        assert "time_flag（类型：DATE）" in result
        assert "idx_value（类型：DECIMAL(10,2)）" in result
    
    def test_default_table_info_completeness(self):
        """测试默认表信息的完整性"""
        result = get_default_table_info()
        
        # 炼钢指标信息表字段
        steelmaking_indicator_fields = [
            "idx_code", "idx_name", "idx_benchmark", "level1", "level2",
            "abnormal_judgment_criteria", "influencing_factors", "is_key_indicator"
        ]
        
        for field in steelmaking_indicator_fields:
            assert field in result
        
        # 日数据表通用字段
        daily_data_fields = ["time_flag", "idx_code", "idx_value"]
        
        for field in daily_data_fields:
            assert result.count(field) >= 3  # 至少在3个日数据表中出现
        
        # 指标信息表通用字段
        indicator_info_fields = ["idx_code", "idx_name", "unit", "description"]
        
        # 验证轧钢和炼铁指标信息表包含这些字段
        assert result.count("unit（类型：VARCHAR(20)）") >= 2
        assert result.count("description（类型：TEXT）") >= 2


class TestTableInfoIntegration:
    """集成测试"""
    
    def test_table_info_with_settings(self, mock_engine, mock_inspector, mock_settings):
        """测试使用settings中的TARGET_TABLES"""
        with patch("database.schema.get_engine") as mock_get_engine:
            mock_get_engine.return_value = mock_engine
            
            with patch("sqlalchemy.inspect") as mock_inspect:
                mock_inspect.return_value = mock_inspector
                
                with patch("database.schema.settings", mock_settings):
                    # 使用settings中的TARGET_TABLES
                    result = get_table_info(mock_settings.TARGET_TABLES)
                    
                    # 验证包含所有目标表
                    for table in mock_settings.TARGET_TABLES:
                        if table != "llmproduct1":  # 这个不是表名
                            assert f"表名：{table}" in result
    
    def test_empty_database(self, mock_engine):
        """测试空数据库的情况"""
        with patch("database.schema.get_engine") as mock_get_engine:
            mock_get_engine.return_value = mock_engine
            
            with patch("sqlalchemy.inspect") as mock_inspect:
                mock_inspector = Mock()
                mock_inspector.get_table_names.return_value = []
                mock_inspect.return_value = mock_inspector
                
                result = get_table_info()
                
                assert result == "指定的表不存在或未传入表名"