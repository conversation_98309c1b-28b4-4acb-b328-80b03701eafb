@echo off
chcp 65001 >nul
echo ========================================
echo Restore to Original Plugin
echo ========================================
echo.

cd /d "%~dp0extension"

echo Restoring original files...

if exist manifest-original.json (
    copy manifest-original.json manifest.json >nul
    echo [OK] Restore manifest.json
) else (
    echo [ERROR] manifest-original.json backup file not found
)

if exist content-original.js (
    copy content-original.js content.js >nul
    echo [OK] Restore content.js
) else (
    echo [ERROR] content-original.js backup file not found
)

if exist styles-original.css (
    copy styles-original.css styles.css >nul
    echo [OK] Restore styles.css
) else (
    echo [ERROR] styles-original.css backup file not found
)

echo.
echo ========================================
echo Restore Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Open Chrome browser
echo 2. Go to chrome://extensions/
echo 3. Find the plugin and click "Reload" button
echo.
pause
