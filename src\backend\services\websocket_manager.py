from typing import Dict, Set, Optional
from fastapi import WebSocket
import asyncio
import json
from datetime import datetime
from config.logging import logger


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃的WebSocket连接
        self.active_connections: Dict[str, WebSocket] = {}
        # 存储连接的元数据
        self.connection_metadata: Dict[str, Dict] = {}
        
    async def connect(self, websocket: WebSocket, client_id: str):
        """接受新的WebSocket连接"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.connection_metadata[client_id] = {
            "connected_at": datetime.now().isoformat(),
            "last_active": datetime.now().isoformat()
        }
        logger.info(f"Client {client_id} connected")
        
    def disconnect(self, client_id: str):
        """断开连接"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            del self.connection_metadata[client_id]
            logger.info(f"Client {client_id} disconnected")
            
    async def send_text(self, client_id: str, message: str):
        """向特定客户端发送文本消息"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            await websocket.send_text(message)
            self.connection_metadata[client_id]["last_active"] = datetime.now().isoformat()
            
    async def send_json(self, client_id: str, data: dict):
        """向特定客户端发送JSON消息"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            try:
                await websocket.send_json(data)
                self.connection_metadata[client_id]["last_active"] = datetime.now().isoformat()
                logger.debug(f"Successfully sent to {client_id}: {data.get('type')}")
            except Exception as e:
                logger.error(f"Failed to send to {client_id}: {e}")
                raise
        else:
            logger.warning(f"Client {client_id} not in active connections")
            
    async def broadcast(self, message: str, exclude: Optional[Set[str]] = None):
        """广播消息给所有连接的客户端"""
        exclude = exclude or set()
        tasks = []
        for client_id, websocket in self.active_connections.items():
            if client_id not in exclude:
                tasks.append(websocket.send_text(message))
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
            
    async def broadcast_json(self, data: dict, exclude: Optional[Set[str]] = None):
        """广播JSON消息给所有连接的客户端"""
        exclude = exclude or set()
        tasks = []
        for client_id, websocket in self.active_connections.items():
            if client_id not in exclude:
                tasks.append(websocket.send_json(data))
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
            
    def get_active_connections(self) -> Dict[str, Dict]:
        """获取所有活跃连接的信息"""
        return {
            client_id: metadata 
            for client_id, metadata in self.connection_metadata.items()
        }
        
    async def heartbeat(self):
        """心跳检测，清理断开的连接"""
        while True:
            try:
                disconnected = []
                # 创建字典副本以避免在迭代时修改
                for client_id, websocket in list(self.active_connections.items()):
                    try:
                        await websocket.send_json({"type": "ping"})
                    except:
                        disconnected.append(client_id)
                        
                for client_id in disconnected:
                    self.disconnect(client_id)
                    
                await asyncio.sleep(30)  # 每30秒检测一次
            except Exception as e:
                logger.error(f"Heartbeat error: {e}")
                await asyncio.sleep(30)