#!/usr/bin/env python3
"""
吹炼智能体精简版功能测试脚本
测试实时监测和阶段播报功能
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from src.agents.blowing_agent.agent import BlowingAgent


async def test_database_connection():
    """测试数据库连接功能"""
    print("=== 测试数据库连接 ===")

    # 创建智能体实例
    agent = BlowingAgent()

    try:
        # 初始化（包含数据库连接）
        await agent.initialize()

        # 测试获取当前炉次号
        heat_id = await agent._get_current_heat_id()
        print(f"当前炉次号: {heat_id}")

        if heat_id:
            # 测试查询吹炼数据
            data = await agent._query_blowing_data(heat_id)
            print(f"查询到 {len(data)} 条数据")

            if data:
                # 显示最新数据
                latest = data[-1]
                print(f"最新数据 - 吹炼时间: {latest.get('blowtime')}分钟")
                print(f"氧气流量: {latest.get('o2_flowrate')} Nm³/min")
                print(f"枪位高度: {latest.get('gun_height')} mm")
                print(f"火焰颜色: {latest.get('fire_color')}")

    except Exception as e:
        print(f"测试失败: {e}")
    finally:
        # 清理资源
        await agent.cleanup()


async def test_real_time_monitoring():
    """测试实时监测功能"""
    print("\n=== 测试实时监测功能 ===")

    agent = BlowingAgent()

    try:
        # 初始化
        await agent.initialize()

        # 启动实时监测
        print("启动实时监测...")
        await agent.start_real_time_monitoring()

        # 运行60秒（2个监测周期，因为现在是30秒一次）
        print("监测运行中，60秒后自动停止...")
        await asyncio.sleep(60)

        # 停止监测
        print("停止实时监测...")
        await agent.stop_real_time_monitoring()

    except Exception as e:
        print(f"监测测试失败: {e}")
    finally:
        await agent.cleanup()


async def test_stage_notification():
    """测试阶段播报功能"""
    print("\n=== 测试阶段播报功能 ===")

    agent = BlowingAgent()

    try:
        # 初始化
        await agent.initialize()

        # 模拟阶段通知
        stage_info = {"stage_number": 2, "stage_name": "脱碳阶段"}

        print(f"发送阶段通知: {stage_info}")
        result = await agent.handle_stage_notification(stage_info)

        print("阶段播报结果:")
        try:
            # 创建一个可序列化的副本，处理datetime对象
            printable_result = result.copy()
            if "report" in printable_result and "initial_data" in printable_result[
                    "report"]:
                printable_result["report"]["initial_data"] = "包含初始数据（已省略详情）"
            print(json.dumps(printable_result, ensure_ascii=False, indent=2))
        except Exception as json_error:
            print(f"播报成功，但结果包含无法序列化的数据: {json_error}")
            print(f"播报状态: {'成功' if result.get('success') else '失败'}")
            if result.get("report"):
                print(f"阶段名称: {result['report'].get('stage_name')}")
                print(f"播报类型: {result['report'].get('stage_type')}")
                if result['report'].get('broadcast_content'):
                    print("播报内容:")
                    print(result['report']['broadcast_content'][:200] +
                          "..." if len(result['report']['broadcast_content']) >
                          200 else result['report']['broadcast_content'])

    except Exception as e:
        print(f"阶段播报测试失败: {e}")
    finally:
        await agent.cleanup()


async def test_material_schedule_parsing():
    """测试加料计划解析功能"""
    print("\n=== 测试加料计划解析 ===")

    agent = BlowingAgent()

    # 测试加料计划字符串
    test_schedule = '''{"第一批":{"材料":{"石灰":3500}, "时间":2}, "第二批":{"材料":{"白云石":1200, "萤石":200}, "时间":8}, "第三批":{"材料":{"石灰":3000, "轻烧白云石":800}, "时间":14}}'''

    try:
        # 解析加料计划
        schedule = await agent._parse_material_schedule(test_schedule)
        print("解析结果:")
        print(json.dumps(schedule, ensure_ascii=False, indent=2))

        # 测试加料提醒检查
        for test_time in [1, 2, 7, 8, 13, 14, 15]:
            reminder = await agent._check_material_schedule(
                test_time, schedule)
            if reminder:
                print(f"第{test_time}分钟: {reminder}")

    except Exception as e:
        print(f"加料计划测试失败: {e}")


async def main():
    """主测试函数"""
    print("吹炼智能体增强功能测试开始")
    print("=" * 50)

    # 测试数据库连接
    await test_database_connection()

    # 测试加料计划解析
    await test_material_schedule_parsing()

    # 测试阶段播报
    await test_stage_notification()

    # 询问是否进行实时监测测试
    print("\n是否进行实时监测测试？(会运行30秒) [y/N]: ", end="")
    # 在实际使用时可以取消注释下面的代码
    import sys
    response = sys.stdin.readline().strip().lower()
    if response == 'y':
        await test_real_time_monitoring()

    print("\n所有测试完成！")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
