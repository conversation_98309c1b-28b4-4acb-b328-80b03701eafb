from langchain_openai import ChatOpenAI, OpenAI, OpenAIEmbeddings
from config import settings
from pydantic import SecretStr


def get_llm(extra_body: dict = None) -> ChatOpenAI:
    """
    获取 LLM 实例，支持动态传入额外参数（如 enable_thinking）
    """
    # 默认参数：非流式调用必须设置 enable_thinking=False
    default_extra = {"enable_thinking": False}
    # 合并用户传入的参数（用户参数优先级更高）
    if extra_body:
        default_extra.update(extra_body)

    if settings.USING_LOCAL_LLM:
        return ChatOpenAI(
            model=settings.LOCAL_MODEL_NAME,
            api_key=SecretStr(settings.ALIYUN_API_KEY),
            base_url=settings.LOCAL_URL,
            extra_body=default_extra  # 传递合并后的参数
        )
    else:
        return ChatOpenAI(
            model=settings.ALIYUN_MODEL_NAME,
            api_key=SecretStr(settings.ALIYUN_API_KEY),
            base_url=settings.ALIYUN_URL,
            extra_body=default_extra  # 传递合并后的参数
        )


def get_embeddings() -> OpenAIEmbeddings:
    """
    获取嵌入模型实例
    """
    if settings.USING_LOCAL_LLM:
        return OpenAIEmbeddings(api_key=SecretStr(settings.ALIYUN_API_KEY),
                                base_url=settings.LOCAL_URL)
    else:
        return OpenAIEmbeddings(api_key=SecretStr("EMPTY"),
                                base_url=settings.ALIYUN_URL)


if __name__ == "__main__":
    llm = get_llm()

    print("==== 模型流式输出 =======")
    stream = llm.stream("你是谁",
                        extra_body={
                            "enable_thinking": False,
                            "max_tokens": 100,
                            "temperature": 0.1
                        })
    for chunk in stream:
        print(chunk.content, end="", flush=True)  # type: ignore

    print("\n==== 模型非流式输出 =======")
    response = llm.invoke("你是谁",
                          extra_body={
                              "enable_thinking": False,
                              "max_tokens": 100,
                              "temperature": 0.1
                          })
    print(response.content)

    # 测试模型异步输出
    print("\n==== 模型异步输出 =======")
    import asyncio
    import time

    async def task(n):
        print(f"Task {n} started at {time.strftime('%X')}")
        response = await llm.ainvoke("你是谁",
                                     extra_body={
                                         "enable_thinking": False,
                                         "max_tokens": 100,
                                         "temperature": 0.8
                                     })
        print(f"Task {n} finished at {time.strftime('%X')}")
        return response.content

    async def main():
        start_time = time.time()
        results = await asyncio.gather(task(2), task(1), task(3))  # 并发执行三个任务
        end_time = time.time()
        print(f"Total time: {end_time - start_time:.2f} seconds")
        print("Results:", results)

    asyncio.run(main())

    # 模型多轮对话输出
    print("\n==== 模型多轮对话输出 =======")
    messages = [{
        "role": "system",
        "content": "你叫浪潮小钢，是一个智能炼钢助手"
    }, {
        "role": "user",
        "content": "你好！"
    }, {
        "role": "assistant",
        "content": "你好！很高兴见到你。"
    }, {
        "role": "user",
        "content": "你叫什么名字"
    }]
    stream = llm.stream(messages,
                        extra_body={
                            "enable_thinking": False,
                            "max_tokens": 512,
                            "temperature": 0.1
                        })
    for chunk in stream:
        print(chunk.content, end="", flush=True)  # type: ignore
