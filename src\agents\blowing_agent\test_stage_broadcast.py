#!/usr/bin/env python3
"""
阶段播报功能测试脚本
模拟整个吹炼过程的阶段播报
"""

import asyncio
import json
import sys
import os
from datetime import datetime, timedelta

# 确保能够导入项目模块
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from agents.blowing_agent.agent import BlowingAgent


async def test_stage_broadcast_complete_process():
    """测试完整的阶段播报过程"""
    print("=" * 60)
    print("阶段播报功能完整测试开始")
    print("=" * 60)

    agent = BlowingAgent()

    try:
        # 初始化智能体
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 定义吹炼过程的各个阶段
        stages = [{
            "stage_number": 1,
            "stage_name": "准备阶段",
            "time": "2025-08-13 08:00:00",
            "description": "吹炼开始前的准备工作"
        }, {
            "stage_number": 2,
            "stage_name": "预热阶段",
            "time": "2025-08-13 08:05:00",
            "description": "炉内预热，开始吹氧"
        }, {
            "stage_number": 3,
            "stage_name": "脱碳阶段",
            "time": "2025-08-13 08:10:00",
            "description": "主要脱碳反应阶段"
        }, {
            "stage_number": 4,
            "stage_name": "精炼阶段",
            "time": "2025-08-13 08:15:00",
            "description": "成分调整和精炼"
        }, {
            "stage_number": 5,
            "stage_name": "终点控制阶段",
            "time": "2025-08-13 08:20:00",
            "description": "接近终点，精确控制"
        }, {
            "stage_number": 6,
            "stage_name": "吹炼结束",
            "time": "2025-08-13 08:25:00",
            "description": "吹炼过程完成"
        }]

        print(f"\n将模拟 {len(stages)} 个阶段的播报过程：")
        for i, stage in enumerate(stages, 1):
            print(f"  {i}. {stage['stage_name']} - {stage['time']}")

        print("\n开始阶段播报测试...")
        print("注意：每个阶段会查询指定时间之前的最新数据进行播报")
        print("-" * 60)

        # 逐个测试每个阶段
        for i, stage in enumerate(stages):
            print(f"\n【阶段 {stage['stage_number']}】{stage['stage_name']}")
            print(f"时间: {stage['time']}")
            print(f"描述: {stage['description']}")
            print("-" * 40)

            # 调用阶段播报函数
            result = await agent.stage_broadcast_function(
                stage_info={
                    "stage_number": stage["stage_number"],
                    "stage_name": stage["stage_name"]
                },
                broadcast_time=stage["time"])

            # 显示播报结果
            if result.get("success"):
                report = result.get("report", {})
                print("✓ 阶段播报成功")
                print(f"播报类型: {report.get('stage_type')}")
                print(f"炉次号: {report.get('heat_id')}")

                # 显示播报内容（截取前300字符）
                broadcast_content = report.get('broadcast_content', '')
                if broadcast_content:
                    print("\n播报内容:")
                    if len(broadcast_content) > 300:
                        print(broadcast_content[:300] + "...")
                    else:
                        print(broadcast_content)

                # 显示其他关键信息
                if report.get('material_reminder'):
                    print(f"\n加料提醒: {report['material_reminder']}")

                if report.get('splash_info'):
                    print(f"喷溅情况: {report['splash_info']}")

            else:
                print(f"❌ 阶段播报失败: {result.get('error')}")

            print("-" * 40)

            # 在阶段之间稍作停顿
            if i < len(stages) - 1:
                print("等待3秒后进入下一阶段...")
                await asyncio.sleep(10)

        print("\n" + "=" * 60)
        print("阶段播报功能完整测试完成")
        print("=" * 60)

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.cleanup()


async def test_stage_broadcast_with_current_time():
    """测试使用当前时间的阶段播报"""
    print("\n" + "=" * 60)
    print("当前时间阶段播报测试")
    print("=" * 60)

    agent = BlowingAgent()

    try:
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 测试不传入时间参数（使用当前时间）
        print("\n测试使用当前时间的阶段播报...")

        result = await agent.stage_broadcast_function(
            stage_info={
                "stage_number": 2,
                "stage_name": "当前时间测试阶段"
            }
            # 不传入broadcast_time参数，应该使用当前时间
        )

        if result.get("success"):
            report = result.get("report", {})
            print("✓ 当前时间阶段播报成功")
            print(f"播报时间: {report.get('timestamp')}")
            print(f"阶段名称: {report.get('stage_name')}")
            print(f"播报类型: {report.get('stage_type')}")

            # 显示播报内容的前200字符
            broadcast_content = report.get('broadcast_content', '')
            if broadcast_content:
                print(f"\n播报内容预览: {broadcast_content[:200]}...")
        else:
            print(f"❌ 当前时间阶段播报失败: {result.get('error')}")

    except Exception as e:
        print(f"❌ 当前时间测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.cleanup()


async def test_stage_broadcast_error_cases():
    """测试阶段播报的错误情况"""
    print("\n" + "=" * 60)
    print("阶段播报错误情况测试")
    print("=" * 60)

    agent = BlowingAgent()

    try:
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 测试无效时间格式
        print("\n测试无效时间格式...")
        try:
            result = await agent.stage_broadcast_function(
                stage_info={
                    "stage_number": 1,
                    "stage_name": "错误时间测试"
                },
                broadcast_time="invalid-time-format")
            print(f"结果: {result.get('success')}, 错误: {result.get('error')}")
        except Exception as e:
            print(f"捕获到预期的错误: {e}")

        # 测试过早的时间（数据库中没有数据）
        print("\n测试过早的时间...")
        result = await agent.stage_broadcast_function(
            stage_info={
                "stage_number": 1,
                "stage_name": "过早时间测试"
            },
            broadcast_time="2020-01-01 00:00:00")
        print(f"结果: {result.get('success')}, 错误: {result.get('error')}")

    except Exception as e:
        print(f"❌ 错误情况测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.cleanup()


async def main():
    """主测试函数"""
    print("开始阶段播报功能测试...")

    # 测试完整的阶段播报过程
    await test_stage_broadcast_complete_process()

    # 测试当前时间播报
    # await test_stage_broadcast_with_current_time()

    # 测试错误情况
    # await test_stage_broadcast_error_cases()

    print("\n所有阶段播报测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
