let chatWindow = null;
let floatingIcon = null;
let ws = null;
let messages = [];
let currentStreamingMessage = null;
let currentStreamingElement = null;

function createFloatingIcon() {
  floatingIcon = document.createElement('div');
  floatingIcon.id = 'chatbi-floating-icon';
  floatingIcon.innerHTML = `
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="currentColor"/>
      <path d="M12 6C8.69 6 6 8.69 6 12C6 15.31 8.69 18 12 18C15.31 18 18 15.31 18 12C18 8.69 15.31 6 12 6ZM12 16.5C9.52 16.5 7.5 14.48 7.5 12C7.5 9.52 9.52 7.5 12 7.5C14.48 7.5 16.5 9.52 16.5 12C16.5 14.48 14.48 16.5 12 16.5Z" fill="currentColor"/>
      <circle cx="12" cy="12" r="1.5" fill="currentColor"/>
    </svg>
  `;
  floatingIcon.addEventListener('click', toggleChatWindow);
  document.body.appendChild(floatingIcon);
}

function createChatWindow() {
  chatWindow = document.createElement('div');
  chatWindow.id = 'chatbi-window';
  chatWindow.className = 'chatbi-hidden';
  chatWindow.innerHTML = `
    <div class="chatbi-header">
      <h3>智能炼钢决策助手</h3>
      <button class="chatbi-close" aria-label="Close">&times;</button>
    </div>
    <div class="chatbi-messages" id="chatbi-messages"></div>
    <div class="chatbi-recommendations" id="chatbi-recommendations"></div>
    <div class="chatbi-input-container">
      <div class="chatbi-command-buttons">
        <button class="chatbi-cmd-btn" data-cmd="history">查看历史</button>
        <button class="chatbi-cmd-btn" data-cmd="clear_history">清空历史</button>
        <button class="chatbi-cmd-btn" data-cmd="ping">心跳测试</button>
      </div>
      <div class="chatbi-input-wrapper">
        <textarea 
          id="chatbi-input" 
          placeholder="请输入您的问题..." 
          rows="1"
        ></textarea>
        <div class="chatbi-buttons">
          <button id="chatbi-send" class="chatbi-send-btn">发送</button>
        </div>
      </div>
    </div>
  `;
  document.body.appendChild(chatWindow);

  const closeBtn = chatWindow.querySelector('.chatbi-close');
  const sendBtn = chatWindow.querySelector('#chatbi-send');
  const input = chatWindow.querySelector('#chatbi-input');
  const cmdButtons = chatWindow.querySelectorAll('.chatbi-cmd-btn');

  closeBtn.addEventListener('click', toggleChatWindow);
  sendBtn.addEventListener('click', sendMessage);
  
  cmdButtons.forEach(btn => {
    btn.addEventListener('click', () => {
      const cmd = btn.dataset.cmd;
      sendCommand(cmd);
    });
  });
  
  input.addEventListener('focus', () => {
    input.rows = 3;
  });
  
  input.addEventListener('blur', () => {
    if (!input.value.trim()) {
      input.rows = 1;
    }
  });
  
  input.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  });

  connectWebSocket();
  showRecommendations();
}

function toggleChatWindow() {
  if (!chatWindow) {
    createChatWindow();
  }
  chatWindow.classList.toggle('chatbi-hidden');
  
  // Toggle floating icon visibility
  if (!chatWindow.classList.contains('chatbi-hidden')) {
    document.getElementById('chatbi-input').focus();
    floatingIcon.style.display = 'none';
  } else {
    floatingIcon.style.display = 'flex';
  }
}

function connectWebSocket() {
  ws = new WebSocket('ws://localhost:9000/ws/agent/decision');
  
  ws.onopen = () => {
    console.log('WebSocket connected to decision agent');
  };
  
  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    handleWebSocketMessage(data);
  };
  
  ws.onerror = (error) => {
    console.error('WebSocket error:', error);
    addMessage('system', '连接错误，请稍后重试。');
  };
  
  ws.onclose = () => {
    console.log('WebSocket disconnected');
    setTimeout(connectWebSocket, 3000);
  };
}

function handleWebSocketMessage(data) {
  switch (data.type) {
    case 'connected':
      console.log('Connected to decision agent:', data.message);
      addMessage('system', data.message);
      break;
      
    case 'processing':
      addMessage('system', data.message);
      break;
      
    case 'stream_start':
      // 创建流式消息对象
      currentStreamingMessage = {
        role: 'assistant',
        content: '',
        timestamp: new Date().toISOString()
      };
      messages.push(currentStreamingMessage);
      
      // 创建消息元素
      const messagesContainer = document.getElementById('chatbi-messages');
      const messageDiv = document.createElement('div');
      messageDiv.className = 'chatbi-message chatbi-assistant';
      messageDiv.setAttribute('data-message-id', 'streaming');
      
      const contentDiv = document.createElement('div');
      contentDiv.className = 'chatbi-message-content';
      contentDiv.id = 'streaming-content';
      
      const timeDiv = document.createElement('div');
      timeDiv.className = 'chatbi-message-time';
      timeDiv.textContent = new Date(currentStreamingMessage.timestamp).toLocaleTimeString();
      
      messageDiv.appendChild(contentDiv);
      messageDiv.appendChild(timeDiv);
      messagesContainer.appendChild(messageDiv);
      
      currentStreamingElement = contentDiv;
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
      break;
    
    case 'stream':
      if (currentStreamingMessage && currentStreamingElement) {
        // 只追加新内容，不重新处理整个文本
        currentStreamingMessage.content += data.content;
        
        // 直接追加文本，避免重新解析
        const textNode = document.createTextNode(data.content);
        currentStreamingElement.appendChild(textNode);
        
        // 保持滚动在底部
        const messagesContainer = document.getElementById('chatbi-messages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
      }
      break;
    
    case 'stream_end':
      if (currentStreamingMessage && currentStreamingElement) {
        // 流结束时，使用完整内容重新渲染一次，确保格式正确
        if (data.total_content) {
          currentStreamingMessage.content = data.total_content;
        }
        currentStreamingElement.innerHTML = renderContent(currentStreamingMessage.content);
        currentStreamingElement.removeAttribute('id');
      }
      
      // 清理流式状态
      currentStreamingMessage = null;
      currentStreamingElement = null;
      showRecommendations();
      break;
      
    case 'info':
      addMessage('system', data.message);
      break;
      
    case 'history':
      addMessage('system', `历史记录: ${JSON.stringify(data.content, null, 2)}`);
      break;
      
    case 'pong':
      console.log('Received pong');
      addMessage('system', '心跳响应正常');
      break;
    
    case 'error':
      addMessage('system', `错误: ${data.message}`);
      break;
  }
}

function sendMessage() {
  const input = document.getElementById('chatbi-input');
  const message = input.value.trim();
  
  if (!message) return;
  
  addMessage('user', message);
  input.value = '';
  input.rows = 1;
  
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify({
      type: 'query',
      content: message,
      context: {}
    }));
  } else {
    addMessage('system', '未连接到服务器，请稍候...');
  }
}

function sendCommand(cmd) {
  const commands = {
    'history': { type: 'history' },
    'clear_history': { type: 'clear_history' },
    'ping': { type: 'ping' }
  };
  
  if (ws && ws.readyState === WebSocket.OPEN && commands[cmd]) {
    ws.send(JSON.stringify(commands[cmd]));
  }
}

function addMessage(role, content) {
  const message = {
    role,
    content,
    timestamp: new Date().toISOString()
  };
  messages.push(message);
  
  // 创建并添加消息元素
  const messagesContainer = document.getElementById('chatbi-messages');
  const messageDiv = document.createElement('div');
  messageDiv.className = `chatbi-message chatbi-${role}`;
  
  const contentDiv = document.createElement('div');
  contentDiv.className = 'chatbi-message-content';
  contentDiv.innerHTML = renderContent(content);
  
  const timeDiv = document.createElement('div');
  timeDiv.className = 'chatbi-message-time';
  timeDiv.textContent = new Date(message.timestamp).toLocaleTimeString();
  
  messageDiv.appendChild(contentDiv);
  messageDiv.appendChild(timeDiv);
  messagesContainer.appendChild(messageDiv);
  
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function renderMessages() {
  const messagesContainer = document.getElementById('chatbi-messages');
  messagesContainer.innerHTML = messages.map(msg => `
    <div class="chatbi-message chatbi-${msg.role}">
      <div class="chatbi-message-content">${renderContent(msg.content)}</div>
      <div class="chatbi-message-time">${new Date(msg.timestamp).toLocaleTimeString()}</div>
    </div>
  `).join('');
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function renderContent(content) {
  // Handle table rendering
  content = content.replace(/```table\n([\s\S]*?)```/g, (match, tableData) => {
    try {
      const table = JSON.parse(tableData);
      return renderTable(table);
    } catch (e) {
      return match;
    }
  });
  
  // Handle chart rendering
  content = content.replace(/```chart\n([\s\S]*?)```/g, (match, chartData) => {
    try {
      const chart = JSON.parse(chartData);
      return renderChart(chart);
    } catch (e) {
      return match;
    }
  });
  
  // Handle code blocks
  content = content.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
    return `<pre><code class="language-${lang || 'plaintext'}">${escapeHtml(code)}</code></pre>`;
  });
  
  // Handle bold and italic
  content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
  
  // Handle line breaks
  content = content.replace(/\n/g, '<br>');
  
  return content;
}

function renderChart(chart) {
  const chartId = 'chart-' + Math.random().toString(36).substr(2, 9);
  const { type, title, data } = chart;
  const { labels, values } = data;
  
  // Create a simple canvas-based chart
  setTimeout(() => {
    const canvas = document.getElementById(chartId);
    if (canvas) {
      const ctx = canvas.getContext('2d');
      const width = canvas.width;
      const height = canvas.height;
      
      // Clear canvas
      ctx.clearRect(0, 0, width, height);
      
      // Simple bar chart implementation
      if (type === 'bar') {
        const barWidth = width / labels.length * 0.8;
        const maxValue = Math.max(...values);
        const padding = 40;
        
        values.forEach((value, i) => {
          const barHeight = (value / maxValue) * (height - padding * 2);
          const x = (width / labels.length) * i + (width / labels.length - barWidth) / 2;
          const y = height - padding - barHeight;
          
          // Draw bar
          ctx.fillStyle = `hsl(${220 + i * 30}, 70%, 60%)`;
          ctx.fillRect(x, y, barWidth, barHeight);
          
          // Draw label
          ctx.fillStyle = '#666';
          ctx.font = '12px sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText(labels[i], x + barWidth / 2, height - 10);
          
          // Draw value
          ctx.fillText(value.toLocaleString(), x + barWidth / 2, y - 5);
        });
      } else if (type === 'pie' || type === 'doughnut') {
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) / 2 - 20;
        const total = values.reduce((a, b) => a + b, 0);
        let currentAngle = -Math.PI / 2;
        
        values.forEach((value, i) => {
          const sliceAngle = (value / total) * 2 * Math.PI;
          
          // Draw slice
          ctx.beginPath();
          ctx.moveTo(centerX, centerY);
          ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
          ctx.closePath();
          ctx.fillStyle = `hsl(${220 + i * 60}, 70%, 60%)`;
          ctx.fill();
          
          // Draw label
          const labelAngle = currentAngle + sliceAngle / 2;
          const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
          const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);
          
          ctx.fillStyle = 'white';
          ctx.font = '12px sans-serif';
          ctx.textAlign = 'center';
          ctx.fillText(labels[i], labelX, labelY);
          
          currentAngle += sliceAngle;
        });
        
        // Draw doughnut hole
        if (type === 'doughnut') {
          ctx.beginPath();
          ctx.arc(centerX, centerY, radius * 0.5, 0, 2 * Math.PI);
          ctx.fillStyle = '#f5f5f5';
          ctx.fill();
        }
      }
      
      // Draw title
      ctx.fillStyle = '#333';
      ctx.font = 'bold 14px sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(title, width / 2, 20);
    }
  }, 100);
  
  return `<div class="chatbi-chart-container">
    <canvas id="${chartId}" width="320" height="200" style="border: 1px solid #e0e0e0; border-radius: 8px; margin: 10px 0;"></canvas>
  </div>`;
}

function renderTable(table) {
  const { headers, rows } = table;
  
  let html = '<table style="width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 13px;">';
  
  // Headers
  html += '<thead><tr>';
  headers.forEach(header => {
    html += `<th style="border: 1px solid #e0e0e0; padding: 8px; background: #f5f5f5; font-weight: 600; text-align: left;">${escapeHtml(header)}</th>`;
  });
  html += '</tr></thead>';
  
  // Rows
  html += '<tbody>';
  rows.forEach(row => {
    html += '<tr>';
    row.forEach(cell => {
      html += `<td style="border: 1px solid #e0e0e0; padding: 8px;">${escapeHtml(cell)}</td>`;
    });
    html += '</tr>';
  });
  html += '</tbody></table>';
  
  return html;
}

function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function showRecommendations(questions = null) {
  const container = document.getElementById('chatbi-recommendations');
  const defaultQuestions = [
    "查询今天的产量数据",
    "分析上周Q235钢种的质量趋势",
    "昨天的成本指标是多少？",
    "查询本月的生产计划完成情况"
  ];
  
  const recommendedQuestions = questions || defaultQuestions;
  
  container.innerHTML = `
    <div class="chatbi-recommendations-title">推荐问题：</div>
    <div class="chatbi-recommendations-list">
      ${recommendedQuestions.map(q => `
        <button class="chatbi-recommendation" data-question="${escapeHtml(q)}">${escapeHtml(q)}</button>
      `).join('')}
    </div>
  `;
  
  container.querySelectorAll('.chatbi-recommendation').forEach(btn => {
    btn.addEventListener('click', () => {
      document.getElementById('chatbi-input').value = btn.dataset.question;
      sendMessage();
    });
  });
}

// Chrome extension message listener
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'toggleChat') {
    toggleChatWindow();
  }
});

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', createFloatingIcon);
} else {
  createFloatingIcon();
}