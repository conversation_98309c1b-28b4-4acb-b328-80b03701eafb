import hashlib
import base64
import hmac
import json
from urllib.parse import urlencode
from wsgiref.handlers import format_date_time
from datetime import datetime
import time
from time import mktime
import asyncio
import websockets
from websockets.exceptions import ConnectionClosed, ConnectionClosedOK
from typing import Optional, Dict, Any
from src.config.logging import logger
from src.config.settings import settings


STATUS_FIRST_FRAME = 0
STATUS_CONTINUE_FRAME = 1
STATUS_LAST_FRAME = 2

CHUNK_SIZE = 1280  # 每帧40ms的数据，16000采样率 * 0.04 = 640个采样 * 2字节 = 1280字节


class WsParam:
    def __init__(self, app_id: str, api_key: str, api_secret: str):
        self.app_id = app_id
        self.api_key = api_key
        self.api_secret = api_secret
        
        self.common_args = {
            'app_id': self.app_id
        }
        
        self.business_args = {
            'domain': 'iat',
            'language': 'zh_cn',
            'accent': 'mandarin',
            'vinfo': 1,
            'vad_eos': 10000,
            'dwa': 'wpgs',
            'ptt': 0
        }
    
    def create_url(self) -> str:
        url = 'wss://ws-api.xfyun.cn/v2/iat'
        now = datetime.now()
        date = format_date_time(mktime(now.timetuple()))
        
        signature_origin = 'host: ' + 'ws-api.xfyun.cn' + '\n'
        signature_origin += 'date: ' + date + '\n'
        signature_origin += 'GET ' + '/v2/iat ' + 'HTTP/1.1'
        signature_sha = hmac.new(self.api_secret.encode('utf-8'), signature_origin.encode('utf-8'),
                                digestmod=hashlib.sha256).digest()
        signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
        
        authorization_origin = 'api_key="%s", algorithm="%s", headers="%s", signature="%s"' % (
            self.api_key, 'hmac-sha256', 'host date request-line', signature_sha)
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')
        v = {
            'authorization': authorization,
            'date': date,
            'host': 'ws-api.xfyun.cn'
        }
        url = url + '?' + urlencode(v)
        return url


class SpeechRecognitionHandler:
    def __init__(self):
        self.ws_param = WsParam(
            settings.XFYUN_APP_ID,
            settings.XFYUN_API_KEY,
            settings.XFYUN_API_SECRET
        )
        self.rec_text: Dict[int, str] = {}
        self.xfyun_ws: Optional[websockets.WebSocketClientProtocol] = None
        self.status = STATUS_FIRST_FRAME
        self.audio_buffer = bytearray()
        self.is_connected = False
        self.frame_count = 0
        self.send_task = None
        self.receive_task = None
        self.last_send_time = 0
        
    async def connect_to_xfyun(self) -> bool:
        """连接到科大讯飞语音识别服务"""
        try:
            ws_url = self.ws_param.create_url()
            self.xfyun_ws = await websockets.connect(ws_url)
            self.status = STATUS_FIRST_FRAME
            self.rec_text = {}
            self.is_connected = True
            self.frame_count = 0
            self.last_send_time = time.time()
            logger.info("已连接到科大讯飞语音识别服务")
            
            # 启动接收任务
            self.receive_task = asyncio.create_task(self.receive_loop())
            
            return True
        except Exception as e:
            logger.error(f"连接科大讯飞失败: {e}")
            self.is_connected = False
            return False
        
    async def disconnect_from_xfyun(self):
        """断开与科大讯飞的连接"""
        self.is_connected = False
        
        # 取消任务
        if self.send_task:
            self.send_task.cancel()
            try:
                await self.send_task
            except asyncio.CancelledError:
                pass
            self.send_task = None
            
        if self.receive_task:
            self.receive_task.cancel()
            try:
                await self.receive_task
            except asyncio.CancelledError:
                pass
            self.receive_task = None
        
        if self.xfyun_ws:
            try:
                await self.xfyun_ws.close()
            except:
                pass
            self.xfyun_ws = None
            logger.info("已断开与科大讯飞的连接")
    
    async def send_audio_chunk(self, audio_data: bytes) -> bool:
        """发送音频数据块"""
        if not self.xfyun_ws or not self.is_connected:
            return False
            
        try:
            # 控制发送速率，每帧之间至少间隔40ms
            current_time = time.time()
            time_diff = current_time - self.last_send_time
            if time_diff < 0.04:
                await asyncio.sleep(0.04 - time_diff)
            
            if self.status == STATUS_FIRST_FRAME:
                d = {
                    'common': self.ws_param.common_args,
                    'business': self.ws_param.business_args,
                    'data': {
                        'status': 0,
                        'format': 'audio/L16;rate=16000',
                        'audio': base64.b64encode(audio_data).decode('utf-8'),
                        'encoding': 'raw'
                    }
                }
                self.status = STATUS_CONTINUE_FRAME
                logger.debug(f"发送第一帧音频数据，大小: {len(audio_data)} 字节")
            else:
                d = {
                    'data': {
                        'status': 1,
                        'format': 'audio/L16;rate=16000',
                        'audio': base64.b64encode(audio_data).decode('utf-8'),
                        'encoding': 'raw'
                    }
                }
                self.frame_count += 1
                if self.frame_count % 25 == 0:
                    logger.debug(f"已发送 {self.frame_count} 帧音频数据")
            
            await self.xfyun_ws.send(json.dumps(d))
            self.last_send_time = time.time()
            return True
            
        except Exception as e:
            logger.error(f"发送音频数据失败: {e}")
            self.is_connected = False
            return False
    
    async def send_end_frame(self) -> bool:
        """发送结束帧"""
        if not self.xfyun_ws or not self.is_connected:
            return False
            
        try:
            # 确保只发送一次结束帧
            if self.status != STATUS_LAST_FRAME:
                d = {
                    'data': {
                        'status': 2,
                        'format': 'audio/L16;rate=16000',
                        'audio': '',
                        'encoding': 'raw'
                    }
                }
                await self.xfyun_ws.send(json.dumps(d))
                self.status = STATUS_LAST_FRAME
                logger.info("已发送结束帧")
            return True
        except Exception as e:
            logger.error(f"发送结束帧失败: {e}")
            return False
    
    async def receive_loop(self):
        """持续接收识别结果"""
        while self.is_connected and self.xfyun_ws:
            try:
                message = await asyncio.wait_for(self.xfyun_ws.recv(), timeout=1.0)
                data = json.loads(message)
                
                code = data.get('code')
                if code != 0:
                    err_msg = data.get('message', '')
                    logger.error(f'科大讯飞错误: {err_msg}, 错误码: {code}')
                    continue
                    
                result_data = data.get('data', {}).get('result', {})
                ws = result_data.get('ws', [])
                pgs = result_data.get('pgs')
                sn = result_data.get('sn')
                
                result = ''
                for i in ws:
                    for w in i.get('cw', []):
                        result += w.get('w', '')
                        
                if pgs == 'rpl':
                    rg = result_data.get('rg', [0, 1])
                    self.rec_text.update({rg[0]: result})
                    for i in range(rg[0] + 1, rg[1]):
                        self.rec_text.pop(i, None)
                else:
                    self.rec_text[sn] = result
                    
                logger.debug(f'识别结果为: {self.rec_text}')
                
                # 检查是否是最后一帧
                status = data.get('data', {}).get('status', 0)
                if status == 2:
                    logger.info("收到最后一帧响应，识别完成")
                    break
                    
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                if self.is_connected:
                    logger.error(f'接收消息时发生异常: {e}')
                break
    
    def get_recognition_result(self) -> Dict[int, str]:
        """获取当前识别结果"""
        return self.rec_text.copy()
    
    def get_final_text(self) -> str:
        """获取最终的识别文本"""
        text = ''
        sorted_keys = sorted(self.rec_text.keys())
        for key in sorted_keys:
            text += self.rec_text[key]
        return text