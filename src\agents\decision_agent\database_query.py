import pandas as pd
from sqlalchemy import Engine
from config.logging import logger


def execute_sql(engine: Engine, sql: str):
    """执行SQL并返回结果"""
    try:
        chunksize = 100
        chunks = []
        logger.info(f"执行SQL: {sql}")
        
        for chunk in pd.read_sql(sql, engine, chunksize=chunksize):
            chunks.append(chunk)
            logger.info(f"加载数据块: {len(chunk)} 行")
        
        if not chunks:
            logger.info("查询结果为空")
            return pd.DataFrame(), ""
        
        df = pd.concat(chunks)
        logger.info(f"查询完成，共 {len(df)} 行")
        return df, df.to_string()
    except Exception as e:
        logger.error(f"SQL执行错误: {e}")
        return pd.DataFrame(), str(e)