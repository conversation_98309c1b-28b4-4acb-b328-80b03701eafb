let chatWindow = null;
let floatingIcon = null;
let ws = null;
let messages = [];
let currentStreamingMessage = null;
let currentStreamingMessageElement = null;
let voiceBroadcastEnabled = true;
let isSpeaking = false;

function createFloatingIcon() {
  floatingIcon = document.createElement("div");
  floatingIcon.id = "chatbi-floating-icon";
  floatingIcon.innerHTML = `
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="currentColor"/>
      <path d="M12 6C8.69 6 6 8.69 6 12C6 15.31 8.69 18 12 18C15.31 18 18 15.31 18 12C18 8.69 15.31 6 12 6ZM12 16.5C9.52 16.5 7.5 14.48 7.5 12C7.5 9.52 9.52 7.5 12 7.5C14.48 7.5 16.5 9.52 16.5 12C16.5 14.48 14.48 16.5 12 16.5Z" fill="currentColor"/>
      <circle cx="12" cy="12" r="1.5" fill="currentColor"/>
    </svg>
  `;
  floatingIcon.addEventListener("click", toggleChatWindow);
  document.body.appendChild(floatingIcon);
}

function createChatWindow() {
  chatWindow = document.createElement("div");
  chatWindow.id = "chatbi-window";
  chatWindow.className = "chatbi-hidden";
  chatWindow.innerHTML = `
    <div class="chatbi-header">
      <h3>智能炼钢决策助手</h3>
      <button class="chatbi-close" aria-label="Close">&times;</button>
    </div>
    <div class="chatbi-messages" id="chatbi-messages"></div>
    <div class="chatbi-recommendations" id="chatbi-recommendations"></div>
    <div class="chatbi-input-container">
      <div class="chatbi-input-wrapper">
        <textarea 
          id="chatbi-input" 
          placeholder="输入消息或点击 🎤 进行语音输入..." 
          rows="1"
        ></textarea>
        <div class="chatbi-buttons">
          <button id="chatbi-voice" class="chatbi-voice-btn" aria-label="Voice input">🎤</button>
          <button id="chatbi-tts" class="chatbi-tts-btn ${
            voiceBroadcastEnabled ? "active" : ""
          }" aria-label="Toggle voice broadcast">${
    voiceBroadcastEnabled ? "🔊" : "🔇"
  }</button>
          <button id="chatbi-send" class="chatbi-send-btn">发送</button>
        </div>
      </div>
    </div>
  `;
  document.body.appendChild(chatWindow);

  const closeBtn = chatWindow.querySelector(".chatbi-close");
  const sendBtn = chatWindow.querySelector("#chatbi-send");
  const voiceBtn = chatWindow.querySelector("#chatbi-voice");
  const ttsBtn = chatWindow.querySelector("#chatbi-tts");
  const input = chatWindow.querySelector("#chatbi-input");

  closeBtn.addEventListener("click", toggleChatWindow);
  sendBtn.addEventListener("click", sendMessage);
  voiceBtn.addEventListener("click", startVoiceRecognition);
  ttsBtn.addEventListener("click", toggleVoiceBroadcast);

  input.addEventListener("focus", () => {
    input.rows = 3;
  });

  input.addEventListener("blur", () => {
    if (!input.value.trim()) {
      input.rows = 1;
    }
  });

  input.addEventListener("keydown", (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  });

  showRecommendations();
  connectWebSocket();
}

function toggleChatWindow() {
  if (!chatWindow) {
    createChatWindow();
  }
  chatWindow.classList.toggle("chatbi-hidden");

  // Toggle floating icon visibility
  if (!chatWindow.classList.contains("chatbi-hidden")) {
    document.getElementById("chatbi-input").focus();
    floatingIcon.style.display = "none";
  } else {
    floatingIcon.style.display = "flex";
  }
}

function connectWebSocket() {
  ws = new WebSocket("ws://localhost:9001/ws/agent/blowing");

  ws.onopen = () => {
    console.log("WebSocket connected");
  };

  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    handleWebSocketMessage(data);
  };

  ws.onerror = (error) => {
    console.error("WebSocket error:", error);
    addMessage("system", "连接错误，请稍后重试。");
  };

  ws.onclose = () => {
    console.log("WebSocket disconnected");
    setTimeout(connectWebSocket, 3000);
  };
}

function handleWebSocketMessage(data) {
  switch (data.type) {
    case "connected":
      console.log("Connected to decision agent:", data.message);
      addMessage("system", data.message);
      break;

    case "processing":
      addMessage("system", data.message);
      break;

    case "stream_start":
      currentStreamingMessage = {
        role: "assistant",
        content: "",
        timestamp: new Date().toISOString(),
      };
      messages.push(currentStreamingMessage);
      renderMessages(true); // Pass true to indicate streaming
      break;

    case "stream":
      if (currentStreamingMessage && currentStreamingMessageElement) {
        currentStreamingMessage.content += data.content;
        // Directly update the content of the streaming message element
        currentStreamingMessageElement.querySelector(
          ".chatbi-message-content"
        ).innerHTML = renderContent(currentStreamingMessage.content);
      }
      break;

    case "stream_end":
      if (data.total_content && currentStreamingMessage) {
        currentStreamingMessage.content = data.total_content;
        if (currentStreamingMessageElement) {
          currentStreamingMessageElement.querySelector(
            ".chatbi-message-content"
          ).innerHTML = renderContent(currentStreamingMessage.content);
        }
      }
      if (currentStreamingMessage && voiceBroadcastEnabled) {
        speakText(extractTextForSpeech(currentStreamingMessage.content));
      }
      currentStreamingMessage = null;
      currentStreamingMessageElement = null;
      showRecommendations();
      break;

    case "info":
      addMessage("system", data.message);
      break;

    case "history":
      addMessage(
        "system",
        `历史记录: ${JSON.stringify(data.content, null, 2)}`
      );
      break;

    case "pong":
      console.log("Received pong");
      break;

    case "recommendations":
      showRecommendations(data.questions);
      break;

    case "error":
      addMessage("system", `Error: ${data.message}`);
      break;
  }
}

function sendMessage() {
  const input = document.getElementById("chatbi-input");
  const message = input.value.trim();

  if (!message) return;

  addMessage("user", message);
  input.value = "";

  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.send(
      JSON.stringify({
        type: "query",
        content: message,
        context: {},
      })
    );
  } else {
    addMessage("system", "未连接到服务器，请稍候...");
  }
}

function addMessage(role, content) {
  messages.push({
    role,
    content,
    timestamp: new Date().toISOString(),
  });
  renderMessages();
}

function renderMessages(isStreaming = false) {
  const messagesContainer = document.getElementById("chatbi-messages");

  if (isStreaming && currentStreamingMessage) {
    // If streaming, just add the new message container
    const msgElement = document.createElement("div");
    msgElement.className = `chatbi-message chatbi-${currentStreamingMessage.role}`;
    msgElement.innerHTML = `
      <div class="chatbi-message-content">${renderContent(
        currentStreamingMessage.content
      )}</div>
      <div class="chatbi-message-time">${new Date(
        currentStreamingMessage.timestamp
      ).toLocaleTimeString()}</div>
    `;
    messagesContainer.appendChild(msgElement);
    currentStreamingMessageElement = msgElement;
  } else if (!isStreaming) {
    // Full re-render
    messagesContainer.innerHTML = messages
      .map(
        (msg) => `
      <div class="chatbi-message chatbi-${msg.role}">
        <div class="chatbi-message-content">${renderContent(msg.content)}</div>
        <div class="chatbi-message-time">${new Date(
          msg.timestamp
        ).toLocaleTimeString()}</div>
      </div>
    `
      )
      .join("");
  }

  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function renderContent(content) {
  // Handle table rendering
  content = content.replace(/```table\n([\s\S]*?)```/g, (match, tableData) => {
    try {
      const table = JSON.parse(tableData);
      return renderTable(table);
    } catch (e) {
      return match;
    }
  });

  // Handle chart rendering
  content = content.replace(/```chart\n([\s\S]*?)```/g, (match, chartData) => {
    try {
      const chart = JSON.parse(chartData);
      return renderChart(chart);
    } catch (e) {
      return match;
    }
  });

  content = content.replace(
    /```(\w+)?\n([\s\S]*?)```/g,
    (match, lang, code) => {
      return `<pre><code class="language-${lang || "plaintext"}">${escapeHtml(
        code
      )}</code></pre>`;
    }
  );

  content = content.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");
  content = content.replace(/\*(.*?)\*/g, "<em>$1</em>");
  content = content.replace(/\n/g, "<br>");

  return content;
}

function renderChart(chart) {
  const chartId = "chart-" + Math.random().toString(36).substr(2, 9);
  const { type, title, data } = chart;
  const { labels, values } = data;

  // Create a simple canvas-based chart
  setTimeout(() => {
    const canvas = document.getElementById(chartId);
    if (canvas) {
      const ctx = canvas.getContext("2d");
      const width = canvas.width;
      const height = canvas.height;

      // Clear canvas
      ctx.clearRect(0, 0, width, height);

      // Simple bar chart implementation
      if (type === "bar") {
        const barWidth = (width / labels.length) * 0.8;
        const maxValue = Math.max(...values);
        const padding = 40;

        values.forEach((value, i) => {
          const barHeight = (value / maxValue) * (height - padding * 2);
          const x =
            (width / labels.length) * i +
            (width / labels.length - barWidth) / 2;
          const y = height - padding - barHeight;

          // Draw bar
          ctx.fillStyle = `hsl(${220 + i * 30}, 70%, 60%)`;
          ctx.fillRect(x, y, barWidth, barHeight);

          // Draw label
          ctx.fillStyle = "#666";
          ctx.font = "12px sans-serif";
          ctx.textAlign = "center";
          ctx.fillText(labels[i], x + barWidth / 2, height - 10);

          // Draw value
          ctx.fillText(value.toLocaleString(), x + barWidth / 2, y - 5);
        });
      } else if (type === "pie" || type === "doughnut") {
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) / 2 - 20;
        const total = values.reduce((a, b) => a + b, 0);
        let currentAngle = -Math.PI / 2;

        values.forEach((value, i) => {
          const sliceAngle = (value / total) * 2 * Math.PI;

          // Draw slice
          ctx.beginPath();
          ctx.moveTo(centerX, centerY);
          ctx.arc(
            centerX,
            centerY,
            radius,
            currentAngle,
            currentAngle + sliceAngle
          );
          ctx.closePath();
          ctx.fillStyle = `hsl(${220 + i * 60}, 70%, 60%)`;
          ctx.fill();

          // Draw label
          const labelAngle = currentAngle + sliceAngle / 2;
          const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
          const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);

          ctx.fillStyle = "white";
          ctx.font = "12px sans-serif";
          ctx.textAlign = "center";
          ctx.fillText(labels[i], labelX, labelY);

          currentAngle += sliceAngle;
        });

        // Draw doughnut hole
        if (type === "doughnut") {
          ctx.beginPath();
          ctx.arc(centerX, centerY, radius * 0.5, 0, 2 * Math.PI);
          ctx.fillStyle = "#f5f5f5";
          ctx.fill();
        }
      }

      // Draw title
      ctx.fillStyle = "#333";
      ctx.font = "bold 14px sans-serif";
      ctx.textAlign = "center";
      ctx.fillText(title, width / 2, 20);
    }
  }, 100);

  return `<div class="chatbi-chart-container">
    <canvas id="${chartId}" width="320" height="200" style="border: 1px solid #e0e0e0; border-radius: 8px; margin: 10px 0;"></canvas>
  </div>`;
}

function renderTable(table) {
  const { headers, rows } = table;

  let html =
    '<table style="width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 13px;">';

  // Headers
  html += "<thead><tr>";
  headers.forEach((header) => {
    html += `<th style="border: 1px solid #e0e0e0; padding: 8px; background: #f5f5f5; font-weight: 600; text-align: left;">${escapeHtml(
      header
    )}</th>`;
  });
  html += "</tr></thead>";

  // Rows
  html += "<tbody>";
  rows.forEach((row) => {
    html += "<tr>";
    row.forEach((cell) => {
      html += `<td style="border: 1px solid #e0e0e0; padding: 8px;">${escapeHtml(
        cell
      )}</td>`;
    });
    html += "</tr>";
  });
  html += "</tbody></table>";

  return html;
}

function escapeHtml(text) {
  const div = document.createElement("div");
  div.textContent = text;
  return div.innerHTML;
}

function showRecommendations(questions = null) {
  const container = document.getElementById("chatbi-recommendations");
  const defaultQuestions = [
    "查询今天的产量数据",
    "分析上周Q235钢种的质量趋势",
    "昨天的成本指标是多少？",
    "查询本月的生产计划完成情况",
  ];

  const recommendedQuestions = questions || defaultQuestions;

  container.innerHTML = `
    <div class="chatbi-recommendations-title">推荐问题：</div>
    <div class="chatbi-recommendations-list">
      ${recommendedQuestions
        .map(
          (q) => `
        <button class="chatbi-recommendation" data-question="${escapeHtml(
          q
        )}">${escapeHtml(q)}</button>
      `
        )
        .join("")}
    </div>
  `;

  container.querySelectorAll(".chatbi-recommendation").forEach((btn) => {
    btn.addEventListener("click", () => {
      document.getElementById("chatbi-input").value = btn.dataset.question;
      sendMessage();
    });
  });
}

let speechWs = null;
let audioContext = null;
let mediaStreamSource = null;
let scriptProcessor = null;
let isRecording = false;

function startVoiceRecognition() {
  const voiceBtn = document.getElementById("chatbi-voice");

  // 如果正在录音，停止录音
  if (isRecording) {
    stopVoiceRecognition();
    return;
  }

  // 请求麦克风权限
  navigator.mediaDevices
    .getUserMedia({
      audio: {
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
      },
    })
    .then((stream) => {
      // 创建 WebSocket 连接到语音识别服务
      speechWs = new WebSocket("ws://localhost:9001/ws/speech");

      speechWs.onopen = () => {
        console.log("Speech recognition WebSocket connected");

        // 发送开始录音命令
        speechWs.send(JSON.stringify({ action: "start" }));

        // 创建音频上下文
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const inputSampleRate = audioContext.sampleRate;
        console.log("Input sample rate:", inputSampleRate);

        mediaStreamSource = audioContext.createMediaStreamSource(stream);

        // 创建脚本处理器用于获取原始音频数据
        const bufferSize = 4096;
        scriptProcessor = audioContext.createScriptProcessor(bufferSize, 1, 1);

        // 处理音频数据
        scriptProcessor.onaudioprocess = (event) => {
          if (!isRecording) return;

          const inputData = event.inputBuffer.getChannelData(0);

          // 降采样到16kHz
          const downsampledData = downsampleBuffer(
            inputData,
            inputSampleRate,
            16000
          );

          // 转换为16位PCM
          const pcmData = floatTo16BitPCM(downsampledData);

          // 发送到WebSocket
          if (speechWs && speechWs.readyState === WebSocket.OPEN) {
            speechWs.send(pcmData);
          }
        };

        // 连接音频节点
        mediaStreamSource.connect(scriptProcessor);
        scriptProcessor.connect(audioContext.destination);

        // 开始录音
        isRecording = true;
        voiceBtn.classList.add("recording");
        voiceBtn.textContent = "🔴";

        // 设置最长录音时间（例如60秒）
        setTimeout(() => {
          if (isRecording) {
            stopVoiceRecognition();
          }
        }, 60000);
      };

      speechWs.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === "recognition") {
          // 实时显示识别结果
          let text = "";
          const sortedKeys = Object.keys(data.data).sort(
            (a, b) => parseInt(a) - parseInt(b)
          );
          sortedKeys.forEach((key) => {
            text += data.data[key];
          });
          document.getElementById("chatbi-input").value = text;
        } else if (data.type === "final_result") {
          // 最终识别结果
          document.getElementById("chatbi-input").value = data.text;
        } else if (data.type === "error") {
          addMessage("system", `语音识别错误：${data.message}`);
        } else if (data.type === "status") {
          console.log("Speech status:", data.message);
        }
      };

      speechWs.onerror = (error) => {
        console.error("Speech WebSocket error:", error);
        addMessage("system", "语音识别连接错误，请稍后重试。");
        stopVoiceRecognition();
      };

      speechWs.onclose = () => {
        console.log("Speech WebSocket disconnected");
        voiceBtn.classList.remove("recording");
        voiceBtn.textContent = "🎤";
      };
    })
    .catch((error) => {
      console.error("Microphone access error:", error);
      addMessage("system", "无法访问麦克风，请检查权限设置。");
    });
}

// 将浮点数音频数据转换为16位PCM
function floatTo16BitPCM(float32Array) {
  const buffer = new ArrayBuffer(float32Array.length * 2);
  const view = new DataView(buffer);
  let offset = 0;
  for (let i = 0; i < float32Array.length; i++, offset += 2) {
    const s = Math.max(-1, Math.min(1, float32Array[i]));
    view.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
  }
  return buffer;
}

// 降采样
function downsampleBuffer(buffer, inputSampleRate, outputSampleRate) {
  if (inputSampleRate === outputSampleRate) {
    return buffer;
  }
  const sampleRateRatio = inputSampleRate / outputSampleRate;
  const newLength = Math.round(buffer.length / sampleRateRatio);
  const result = new Float32Array(newLength);
  let offsetResult = 0;
  let offsetBuffer = 0;
  while (offsetResult < result.length) {
    const nextOffsetBuffer = Math.round((offsetResult + 1) * sampleRateRatio);
    let accum = 0,
      count = 0;
    for (let i = offsetBuffer; i < nextOffsetBuffer && i < buffer.length; i++) {
      accum += buffer[i];
      count++;
    }
    result[offsetResult] = accum / count;
    offsetResult++;
    offsetBuffer = nextOffsetBuffer;
  }
  return result;
}

function stopVoiceRecognition() {
  const voiceBtn = document.getElementById("chatbi-voice");

  isRecording = false;

  // 断开音频处理
  if (scriptProcessor) {
    scriptProcessor.disconnect();
    scriptProcessor = null;
  }

  if (mediaStreamSource) {
    mediaStreamSource.disconnect();
    mediaStreamSource = null;
  }

  if (audioContext) {
    audioContext.close();
    audioContext = null;
  }

  // 发送停止命令
  if (speechWs && speechWs.readyState === WebSocket.OPEN) {
    speechWs.send(JSON.stringify({ action: "stop" }));
    setTimeout(() => {
      speechWs.close();
      speechWs = null;
    }, 1000);
  }

  voiceBtn.classList.remove("recording");
  voiceBtn.textContent = "🎤";
}

function toggleVoiceBroadcast() {
  const ttsBtn = document.getElementById("chatbi-tts");

  // If speaking, stop the speech
  if (isSpeaking) {
    stopSpeaking();
    return;
  }

  // Otherwise toggle the enabled state
  voiceBroadcastEnabled = !voiceBroadcastEnabled;
  if (voiceBroadcastEnabled) {
    ttsBtn.classList.add("active");
    ttsBtn.textContent = "🔊";
  } else {
    ttsBtn.classList.remove("active");
    ttsBtn.textContent = "🔇";
  }
}

function speakText(text) {
  if ("speechSynthesis" in window) {
    window.speechSynthesis.cancel(); // Cancel any ongoing speech

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = "zh-CN";
    utterance.rate = 1.0;
    utterance.pitch = 1.0;
    utterance.volume = 1.0;

    const ttsBtn = document.getElementById("chatbi-tts");

    utterance.onstart = () => {
      isSpeaking = true;
      ttsBtn.textContent = "⏹️";
      ttsBtn.classList.add("speaking");
    };

    utterance.onend = () => {
      isSpeaking = false;
      ttsBtn.textContent = voiceBroadcastEnabled ? "🔊" : "🔇";
      ttsBtn.classList.remove("speaking");
    };

    utterance.onerror = () => {
      isSpeaking = false;
      ttsBtn.textContent = voiceBroadcastEnabled ? "🔊" : "🔇";
      ttsBtn.classList.remove("speaking");
    };

    window.speechSynthesis.speak(utterance);
  }
}

function stopSpeaking() {
  if ("speechSynthesis" in window) {
    window.speechSynthesis.cancel();
    isSpeaking = false;
    const ttsBtn = document.getElementById("chatbi-tts");
    ttsBtn.textContent = voiceBroadcastEnabled ? "🔊" : "🔇";
    ttsBtn.classList.remove("speaking");
  }
}

function extractTextForSpeech(content) {
  // Remove HTML tags and chart/table data
  let text = content.replace(/<[^>]*>/g, "");
  text = text.replace(/```chart[\s\S]*?```/g, "这里是一个图表");
  text = text.replace(/```table[\s\S]*?```/g, "这里是一个表格");
  text = text.replace(/```[\s\S]*?```/g, "");
  return text.trim();
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "toggleChat") {
    toggleChatWindow();
  }
});

if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", createFloatingIcon);
} else {
  createFloatingIcon();
}
