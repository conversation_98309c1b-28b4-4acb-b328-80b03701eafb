from fastapi import WebSocket, WebSocketDisconnect
import json
import uuid
from config.logging import logger
from backend.services import ConnectionManager, WebSocketStreamingCallbackHandler
from agents import DecisionAgent


async def decision_websocket_endpoint(websocket: WebSocket):
    """决策智能体专用WebSocket端点"""
    client_id = str(uuid.uuid4())
    
    connection_manager: ConnectionManager = websocket.app.state.connection_manager
    
    await connection_manager.connect(websocket, client_id)
    
    try:
        decision_agent = DecisionAgent()
        await decision_agent.initialize()  # 确保智能体正确初始化
        
        await connection_manager.send_json(client_id, {
            "type": "connected",
            "client_id": client_id,
            "agent": "decision",
            "message": "欢迎使用智能问数系统！我可以帮助您查询分析生产数据、成本指标和经营情况。"
        })
        
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            try:
                # 优先解析JSON格式消息
                message = json.loads(data)
                message_type = message.get("type", "query")
                
                if message_type == "exit":
                    await connection_manager.send_json(client_id, {
                        "type": "info",
                        "agent": "decision",
                        "message": "感谢使用智能问数系统，再见！"
                    })
                    break
                elif message_type == "history":
                    history = decision_agent.get_history(limit=10)
                    await connection_manager.send_json(client_id, {
                        "type": "history",
                        "agent": "decision",
                        "content": history
                    })
                    continue
                elif message_type == "clear_history":
                    result = decision_agent.clear_history()
                    await connection_manager.send_json(client_id, {
                        "type": "info",
                        "agent": "decision",
                        "message": result
                    })
                    continue
                elif message_type == "query":
                    # 处理查询请求
                    query = message.get("content", "")
                    context = message.get("context", {})
                    
                    if not query.strip():
                        await connection_manager.send_json(client_id, {
                            "type": "error",
                            "agent": "decision",
                            "message": "查询内容不能为空"
                        })
                        continue
                    
                    logger.info(f"Processing query for client {client_id}: {query[:100]}...")
                    
                    # 创建WebSocket流式回调处理器
                    callback_handler = WebSocketStreamingCallbackHandler(
                        connection_manager, client_id, "decision"
                    )
                    
                    # 发送处理开始消息
                    # await connection_manager.send_json(client_id, {
                    #     "type": "processing",
                    #     "agent": "decision",
                    #     "message": "正在分析您的问题..."
                    # })
                    
                    # 使用回调处理器进行流式处理
                    try:
                        # process_stream 通过回调处理器实时发送响应
                        # 我们需要异步等待处理完成
                        stream_generator = decision_agent.process_stream(
                            query, context, callbacks=[callback_handler]
                        )
                        
                        # 消费生成器以确保处理完成
                        async for _ in stream_generator:
                            # 回调处理器已经处理了所有流式输出
                            continue
                            
                    except Exception as e:
                        logger.error(f"Stream processing error: {e}")
                        await connection_manager.send_json(client_id, {
                            "type": "error",
                            "agent": "decision",
                            "message": f"流式处理时发生错误: {str(e)}"
                        })
                            
                elif message_type == "ping":
                    # 响应心跳
                    await connection_manager.send_json(client_id, {"type": "pong"})
                else:
                    # 未知消息类型
                    await connection_manager.send_json(client_id, {
                        "type": "error",
                        "agent": "decision",
                        "message": f"未知的消息类型: {message_type}"
                    })
                    
            except json.JSONDecodeError:
                # 如果不是有效的JSON格式，返回错误
                await connection_manager.send_json(client_id, {
                    "type": "error",
                    "agent": "decision",
                    "message": "消息格式错误，请发送有效的JSON格式消息"
                })
                    
    except WebSocketDisconnect:
        logger.info(f"Client {client_id} disconnected")
        connection_manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
        connection_manager.disconnect(client_id)
        await websocket.close(code=1011, reason="Server error")