/* 吹炼智能体专用样式 */

#chatbi-floating-icon {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(255, 107, 53, 0.3);
  z-index: 10000;
  transition: all 0.3s ease;
  color: white;
}

#chatbi-floating-icon:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(255, 107, 53, 0.4);
}

#chatbi-window {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 450px;
  height: 650px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  z-index: 10001;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe <PERSON>", <PERSON><PERSON>, sans-serif;
  transition: all 0.3s ease;
}

#chatbi-window.chatbi-hidden {
  display: none;
}

.chatbi-header {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  padding: 15px 20px;
  border-radius: 12px 12px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.chatbi-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.chatbi-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-indicator {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.status-indicator.active {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.5);
}

.chatbi-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.chatbi-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.chatbi-messages {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background: #f8f9fa;
}

.chatbi-message {
  margin-bottom: 12px;
  padding: 10px 12px;
  border-radius: 8px;
  max-width: 85%;
  word-wrap: break-word;
}

.chatbi-message.chatbi-user {
  background: #007bff;
  color: white;
  margin-left: auto;
  text-align: right;
}

.chatbi-message.chatbi-assistant {
  background: white;
  border: 1px solid #e9ecef;
  margin-right: auto;
}

.chatbi-message.chatbi-system {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  margin: 0 auto;
  text-align: center;
  font-size: 13px;
  max-width: 95%;
}

.chatbi-message.chatbi-broadcast {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  margin: 0 auto;
  text-align: center;
  font-weight: 600;
  max-width: 95%;
}

.chatbi-message.chatbi-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  margin: 0 auto;
  text-align: center;
  font-size: 13px;
  max-width: 95%;
}

.chatbi-message.chatbi-error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  margin: 0 auto;
  text-align: center;
  font-size: 13px;
  max-width: 95%;
}

.chatbi-message.chatbi-monitoring {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  margin-right: auto;
  max-width: 95%;
  border-radius: 12px;
}

.chatbi-message.chatbi-monitoring .chatbi-message-content {
  line-height: 1.6;
}

.chatbi-message.chatbi-monitoring h1,
.chatbi-message.chatbi-monitoring h2,
.chatbi-message.chatbi-monitoring h3 {
  margin: 10px 0 8px 0;
  font-weight: 600;
}

.chatbi-message.chatbi-monitoring h1 {
  font-size: 16px;
}

.chatbi-message.chatbi-monitoring h2 {
  font-size: 15px;
}

.chatbi-message.chatbi-monitoring h3 {
  font-size: 14px;
}

.chatbi-message.chatbi-monitoring ul {
  margin: 8px 0;
  padding-left: 20px;
}

.chatbi-message.chatbi-monitoring li {
  margin: 4px 0;
}

.chatbi-message.chatbi-monitoring hr {
  border: none;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  margin: 12px 0;
}

.chatbi-message.chatbi-monitoring strong {
  font-weight: 600;
}

/* 表格样式 */
.data-table {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  overflow: hidden;
  display: table;
  table-layout: auto;
}

.data-table th {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 600;
  padding: 8px 6px;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.data-table td {
  padding: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.95);
}

.data-table tr:last-child td {
  border-bottom: none;
}

.data-table tr:nth-child(even) {
  background: rgba(255, 255, 255, 0.05);
}

/* 段落间距 */
.paragraph-break {
  height: 12px;
}

/* 阶段播报中的表格样式 */
.chatbi-message.chatbi-broadcast .data-table {
  background: rgba(255, 255, 255, 0.1);
}

.chatbi-message.chatbi-broadcast .data-table th {
  background: rgba(255, 255, 255, 0.2);
}

.chatbi-message.chatbi-broadcast .data-table td {
  color: rgba(255, 255, 255, 0.95);
}

/* 普通消息中的表格样式 */
.chatbi-message.chatbi-assistant .data-table {
  background: #f8f9fa;
  color: #495057;
}

.chatbi-message.chatbi-assistant .data-table th {
  background: #e9ecef;
  color: #495057;
}

.chatbi-message.chatbi-assistant .data-table td {
  color: #495057;
  border-bottom: 1px solid #dee2e6;
}

.chatbi-message-content {
  margin-bottom: 4px;
  line-height: 1.4;
}

.chatbi-message-time {
  font-size: 11px;
  opacity: 0.7;
}

.chatbi-control-panel {
  background: white;
  border-top: 1px solid #e9ecef;
  padding: 15px;
}

.chatbi-process-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.chatbi-control-btn {
  flex: 1;
  padding: 10px 15px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.chatbi-control-btn.primary {
  background: #28a745;
  color: white;
}

.chatbi-control-btn.primary:hover:not(:disabled) {
  background: #218838;
}

.chatbi-control-btn.danger {
  background: #dc3545;
  color: white;
}

.chatbi-control-btn.danger:hover:not(:disabled) {
  background: #c82333;
}

.chatbi-control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.chatbi-stage-controls h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #495057;
}

.chatbi-stage-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 10px;
}

.chatbi-stage-btn {
  padding: 8px 12px;
  border: 2px solid #ff6b35;
  background: white;
  color: #ff6b35;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.chatbi-stage-btn:hover {
  background: #ff6b35;
  color: white;
}

.chatbi-stage-btn.current {
  background: #ff6b35;
  color: white;
  box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.3);
}

.chatbi-time-input {
  margin-top: 10px;
}

.chatbi-time-input label {
  display: block;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 4px;
}

.chatbi-time-input input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
}

/* 滚动条样式 */
.chatbi-messages::-webkit-scrollbar {
  width: 6px;
}

.chatbi-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chatbi-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chatbi-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 500px) {
  #chatbi-window {
    width: calc(100vw - 40px);
    height: calc(100vh - 40px);
    bottom: 20px;
    right: 20px;
  }

  .chatbi-stage-buttons {
    grid-template-columns: 1fr;
  }
}
