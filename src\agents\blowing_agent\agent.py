from typing import Dict, Any, List, Optional
from datetime import datetime
import asyncio
import json
import mysql.connector
from mysql.connector import Error

from config.logging import logger
from config.settings import settings
from ..common.llms import get_llm


class BlowingAgent:
    """极数吹炼智能体 - 重构版本，专注于实时监控和阶段自动判断"""

    # 类级别的WebSocket服务器，避免重复启动
    _shared_splash_websocket_server = None
    _websocket_server_port = None

    def __init__(self):
        # 大模型相关
        # self.llm = None

        # 实时监测相关属性
        self.monitoring_active = False  # 监测状态标志
        self.monitoring_task = None  # 监测任务句柄
        self.current_heat_id = None  # 当前炉次号
        self.monitoring_start_time = None  # 监控开始时间
        self.current_query_time = None  # 当前查询时间
        self.db_connection = None  # 数据库连接对象
        self.monitoring_callback = None  # 监控报告回调函数
        self.current_stage = None

        # 观火系统事件通知相关
        self.splash_websocket_server = None  # 观火系统事件通知WebSocket服务器
        self.emergency_monitoring_event = asyncio.Event()  # 紧急监控事件
        self.emergency_monitoring_data = None  # 紧急监控数据 (event_time_str, task_type)  # 当前阶段信息
        self.previous_stage = None  # 上一个阶段信息

        # 初始化LLM客户端
        self.llm = get_llm()

    async def initialize(self):
        """初始化智能体"""
        # 初始化大模型
        self.llm = get_llm()

        # 初始化数据库连接
        await self._init_database_connection()

        # 启动观火系统事件通知WebSocket服务器（避免重复启动）
        if BlowingAgent._shared_splash_websocket_server is None:
            await self.start_splash_websocket_server()

        logger.info("BlowingAgent initialized successfully")

    def set_monitoring_callback(self, callback):
        """设置监控报告回调函数"""
        self.monitoring_callback = callback

    # ==================== 数据库连接和查询方法 ====================

    async def _init_database_connection(self):
        """初始化数据库连接"""
        try:
            # 使用配置文件中的数据库连接信息
            self.db_connection = mysql.connector.connect(
                host=settings.DB_HOST,  # 数据库主机地址
                port=int(settings.DB_PORT),  # 数据库端口
                user=settings.DB_USERNAME,  # 数据库用户名
                password=settings.DB_PASSWORD,  # 数据库密码
                database=settings.DB_NAME,  # 数据库名称（llmproduct1）
                charset='utf8mb4',  # 字符集设置
                autocommit=True  # 自动提交事务
            )
            logger.info("Database connection initialized successfully")
        except Error as e:
            logger.error(f"Failed to initialize database connection: {e}")
            self.db_connection = None

    async def _get_current_heat_id(self,
                                   target_time: datetime = None
                                   ) -> Optional[str]:
        """获取当前炉次号 - 根据时间获取对应的炉次"""
        if not self.db_connection:
            logger.error("Database connection not available")
            return None

        try:
            cursor = self.db_connection.cursor(dictionary=True)

            if target_time:
                # 如果指定了时间，查询该时间点附近的炉次
                query = """
                SELECT ID FROM blowing_information
                WHERE datetime <= %s
                ORDER BY datetime DESC
                LIMIT 1
                """
                cursor.execute(query, (target_time, ))
                result = cursor.fetchone()

                if result:
                    heat_id = result['ID']
                    logger.info(
                        f"Found heat ID for time {target_time}: {heat_id}")
                    cursor.close()
                    return heat_id
                else:
                    logger.warning(
                        f"No heat found for time {target_time}, using latest heat"
                    )

            # 如果没有指定时间或找不到对应时间的炉次，使用最新炉次
            query = "SELECT ID FROM blowing_information ORDER BY No DESC LIMIT 1"
            cursor.execute(query)
            result = cursor.fetchone()
            cursor.close()

            if result:
                heat_id = result['ID']
                logger.info(f"Current heat ID: {heat_id}")
                return heat_id
            else:
                logger.warning("No data found in blowing_information table")
                return None

        except Error as e:
            logger.error(f"Failed to get current heat ID: {e}")
            return None

    # ==================== 清理和关闭 ====================

    async def cleanup(self):
        """清理资源"""
        # 停止实时监测
        await self._stop_optimized_monitoring()

        # 停止观火系统事件通知WebSocket服务器
        await self.stop_splash_websocket_server()

        # 关闭数据库连接
        if self.db_connection:
            try:
                self.db_connection.close()
                logger.info("Database connection closed")
            except Exception as e:
                logger.error(f"Error closing database connection: {e}")

        logger.info("BlowingAgent cleanup completed")

    # ==================== 实时监控功能 ====================

    async def real_time_monitoring_function(
            self,
            action: str,
            start_time: Optional[str] = None) -> Dict[str, Any]:
        """
        完整的实时监控函数

        Args:
            action: 操作类型，'start'表示开始监控，'stop'表示停止监控
            start_time: 开始时间，格式为'YYYY-MM-DD HH:MM:SS'，为空则使用当前时间

        Returns:
            操作结果字典
        """
        try:
            if action == 'start':
                # 处理开始时间参数
                if start_time is None:
                    self.monitoring_start_time = datetime.now()  # 使用当前时间
                    self.current_query_time = None  # 使用当前时间模式
                    logger.info("Using current time for monitoring start")
                else:
                    self.monitoring_start_time = datetime.strptime(
                        start_time, '%Y-%m-%d %H:%M:%S')
                    self.current_query_time = self.monitoring_start_time  # 设置查询时间起点
                    logger.info(
                        f"Using provided time for monitoring start: {start_time}"
                    )

                logger.info(
                    f"Real-time monitoring function started at {self.monitoring_start_time}"
                )

                # 启动实时监控
                await self._start_optimized_monitoring()

                return {
                    "success": True,
                    "action": "start",
                    "start_time": self.monitoring_start_time.isoformat(),
                    "message": "实时监控已启动"
                }

            elif action == 'stop':
                logger.info("Real-time monitoring function stop requested")

                # 停止实时监控
                await self._stop_optimized_monitoring()

                return {
                    "success": True,
                    "action": "stop",
                    "message": "实时监控已停止"
                }

            else:
                return {"success": False, "error": f"未知的操作类型: {action}"}

        except Exception as e:
            logger.error(
                f"Failed to execute real-time monitoring function: {e}")
            return {"success": False, "error": str(e)}

    # ==================== 辅助方法 ====================

    async def _query_data_before_time_all(
            self, heat_id: str, query_time: datetime) -> List[Dict[str, Any]]:
        """查询指定时间之前的所有数据（用于实时监控的变化分析）"""
        if not self.db_connection:
            logger.error("Database connection not available")
            return []

        try:
            cursor = self.db_connection.cursor(dictionary=True)

            # 查询指定时间之前的所有数据 - 使用新的字段名
            query = """
            SELECT No, ID, datetime, blowtime, ts_temp, ts_weight, ts_initial_c, ts_initial_si,
                   ts_initial_mn, ts_initial_p, ts_initial_s, ts_source, ts_is_desul, fg_weight,
                   equip, equip_age, pre_steel, steel_goal, temp_goal, c_goal, si_goal, mn_goal,
                   p_goal, s_goal, o2_predict, lime_predict, si_predict, mn_predict,
                   operation, process, temperature, o2_flowrate, co, gun_height, blow_pres, o2_accum,
                   lime_accum, si_accum, mn_accum, pj_time, pj_status, pj_dura, fire_hue, fire_bright, fire_hard
            FROM blowing_information
            WHERE ID = %s AND datetime <= %s
            ORDER BY CAST(blowtime AS UNSIGNED), datetime ASC
            """

            cursor.execute(query, (heat_id, query_time))
            results = cursor.fetchall()
            cursor.close()

            if results:
                logger.debug(
                    f"Found {len(results)} data records before time {query_time}"
                )
                return results
            else:
                logger.warning(f"No data found before time {query_time}")
                return []

        except Error as e:
            logger.error(f"Failed to query data before time {query_time}: {e}")
            return []

    async def _start_optimized_monitoring(self):
        """启动优化后的实时监控"""
        try:
            if self.monitoring_task and not self.monitoring_task.done():
                logger.warning("Monitoring is already running")
                return

            self.monitoring_active = True
            self.monitoring_task = asyncio.create_task(
                self._optimized_monitoring_loop())
            logger.info("Optimized real-time monitoring started")

        except Exception as e:
            logger.error(f"Failed to start optimized monitoring: {e}")

    async def _stop_optimized_monitoring(self):
        """停止优化后的实时监控"""
        try:
            self.monitoring_active = False

            if self.monitoring_task and not self.monitoring_task.done():
                self.monitoring_task.cancel()
                try:
                    await self.monitoring_task
                except asyncio.CancelledError:
                    logger.info("Optimized monitoring loop cancelled")

            logger.info("Optimized real-time monitoring stopped")

        except Exception as e:
            logger.error(f"Failed to stop optimized monitoring: {e}")

    async def _optimized_monitoring_loop(self):
        """优化后的监控循环 - 支持紧急监控打断"""
        logger.info(
            "Starting optimized monitoring loop with emergency support")

        # 启动后立即执行第一次监控
        await self._execute_single_monitoring_cycle(is_emergency=False)

        while self.monitoring_active:
            try:
                # 等待60秒或紧急事件，哪个先到执行哪个
                is_emergency = False
                emergency_time_str = None
                task_type = None

                try:
                    await asyncio.wait_for(
                        self.emergency_monitoring_event.wait(), timeout=60.0)
                    # 紧急事件触发
                    is_emergency = True
                    if self.emergency_monitoring_data:
                        emergency_time_str, task_type = self.emergency_monitoring_data
                    logger.info(f"紧急监控事件触发: {task_type}")

                except asyncio.TimeoutError:
                    # 60秒超时，执行定时监控
                    logger.debug("执行定时监控")

                # 执行监控
                await self._execute_single_monitoring_cycle(
                    is_emergency, emergency_time_str, task_type)

            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                # 如果出错且是紧急监控，清理状态
                if is_emergency:
                    self._clear_emergency_data()

        logger.info("Monitoring loop stopped")

    async def _execute_single_monitoring_cycle(self,
                                               is_emergency=False,
                                               emergency_time_str=None,
                                               task_type=None):
        """执行单次监控周期"""
        try:
            # 设置查询时间
            if is_emergency and emergency_time_str:
                # 紧急监控：使用观火系统提供的时间
                query_time = datetime.strptime(emergency_time_str,
                                               '%Y-%m-%d %H:%M:%S')
            else:
                # 定时监控：使用设定的查询时间或当前时间
                if self.current_query_time:
                    # 如果有设定的查询时间，每次递增60秒
                    from datetime import timedelta
                    self.current_query_time += timedelta(seconds=60)
                    query_time = self.current_query_time
                else:
                    # 如果没有设定查询时间，使用当前时间
                    query_time = datetime.now()
                    self.current_query_time = query_time

            # 根据查询时间获取对应的炉次号
            current_heat_id = await self._get_current_heat_id(query_time)
            if not current_heat_id:
                logger.warning("No current heat ID found, skipping this cycle")
                if is_emergency:
                    self._clear_emergency_data()
                return

            # 查询数据
            all_data = await self._query_data_before_time_all(
                current_heat_id, query_time)
            if not all_data:
                logger.warning(
                    f"No data found for heat {current_heat_id} before {query_time}"
                )
                if is_emergency:
                    self._clear_emergency_data()
                return

            # 获取最新数据
            latest_data = all_data[-1]

            # 生成监控报告
            monitoring_report = await self._generate_optimized_monitoring_report(
                current_heat_id, latest_data, all_data, query_time)

            # 添加监控类型标识
            if is_emergency:
                monitoring_report["report_type"] = "emergency_monitoring"
                monitoring_report["trigger_reason"] = f"观火系统检测到{task_type}"
                monitoring_report["event_type"] = task_type
                monitoring_report["event_detection_time"] = emergency_time_str
                monitoring_report["priority"] = "high"
            else:
                monitoring_report["report_type"] = "scheduled_monitoring"
                monitoring_report["trigger_reason"] = "定时监控"
                monitoring_report["priority"] = "normal"

            # 记录监控报告生成
            stage_info = monitoring_report.get("stage_info", {})
            stage_name = stage_info.get("stage_name", "未知阶段")
            blow_time = monitoring_report.get("blow_time", "0")
            logger.info(
                f"Monitoring report generated - Stage: {stage_name}, Blow time: {blow_time}min, Type: {monitoring_report['report_type']}"
            )

            # 通过回调函数发送监控报告
            if self.monitoring_callback and monitoring_report:
                try:
                    await self.monitoring_callback(monitoring_report)
                except Exception as callback_error:
                    logger.error(
                        f"Error in monitoring callback: {callback_error}")

            # 如果是紧急监控，清理数据
            if is_emergency:
                self._clear_emergency_data()
                logger.info(f"{task_type}紧急监控完成，恢复定时监控")

        except Exception as e:
            logger.error(f"Error in monitoring cycle: {e}")
            if is_emergency:
                self._clear_emergency_data()

    async def _generate_optimized_monitoring_report(
            self, heat_id: str, latest_data: Dict[str, Any],
            all_data: List[Dict[str,
                                Any]], query_time: datetime) -> Dict[str, Any]:
        """生成新的监控报告（包含阶段自动判断和三部分内容）"""
        try:
            current_blow_time = int(latest_data.get('blowtime', 0) or 0)

            # 1. 判断当前所处的吹炼阶段
            current_stage_info = await self._determine_current_stage(
                latest_data, all_data)

            # 2. 生成参数状态与趋势分析
            parameters_analysis = await self._generate_parameters_analysis(
                latest_data, all_data, current_stage_info)

            # 3. 生成风险提示与操作建议
            risk_and_suggestions = await self._generate_risk_and_suggestions(
                latest_data, all_data, current_stage_info, current_blow_time)

            # 组装完整的监控报告，使用查询时间而不是当前时间
            monitoring_report = {
                "timestamp": query_time.isoformat(),
                "heat_id": heat_id,
                "blow_time": str(current_blow_time),
                "current_stage": current_stage_info,
                "parameters_analysis": parameters_analysis,
                "risk_and_suggestions": risk_and_suggestions,
                "report_type": "integrated_monitoring"
            }

            return monitoring_report

        except Exception as e:
            logger.error(f"Failed to generate monitoring report: {e}")
            return {
                "timestamp": query_time.isoformat(),
                "heat_id": heat_id,
                "error": "监控报告生成失败",
                "report_type": "integrated_monitoring"
            }

    # ==================== 新增的阶段判断和报告生成方法 ====================

    async def _determine_current_stage(
            self, latest_data: Dict[str, Any],
            all_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """判断当前所处的吹炼阶段"""
        try:
            current_blow_time = int(latest_data.get('blowtime', 0) or 0)
            current_operation = latest_data.get('operation', '')

            logger.info(
                f"阶段判断 - 吹炼时间: {current_blow_time}分钟, 操作状态: '{current_operation}'"
            )

            # 基于规则判断阶段
            stage_info = None

            # 6. 出钢阶段：operation字段值为"出钢中"
            if current_operation == "出钢中":
                stage_info = {
                    "stage_number":
                    6,
                    "stage_name":
                    "出钢阶段",
                    "stage_description":
                    f"出钢中，{latest_data.get('ID', 'N/A')}炉次正在出钢"
                }

            # 1. 接收与装料阶段：operation为"准备"
            elif current_operation == "准备":
                stage_info = await self._generate_stage_1_description(
                    latest_data)

            # 2. 开吹与点火阶段：operation从"准备"变成"吹炼中"
            elif current_operation == "吹炼中":
                # 检查是否刚从准备阶段转入
                prev_operation = ""
                if len(all_data) >= 2:
                    prev_data = all_data[-2]
                    prev_operation = prev_data.get('operation', '')

                if prev_operation == "准备":
                    stage_info = await self._generate_stage_2_description(
                        latest_data)
                else:
                    # 3-5. 吹炼中的其他阶段：使用大模型判断
                    logger.info(f"调用大模型判断高级阶段，吹炼时间: {current_blow_time}分钟")
                    stage_info = await self._llm_determine_advanced_stage(
                        latest_data, all_data, current_blow_time)
                    logger.info(f"大模型阶段判断结果: {stage_info}")

            # 其他情况使用备选判断
            if not stage_info:
                logger.warning(f"主要阶段判断失败，使用备选判断，吹炼时间: {current_blow_time}分钟")
                stage_info = self._fallback_stage_determination(
                    current_blow_time)
                logger.info(f"备选阶段判断结果: {stage_info}")

            # 检查阶段是否发生变化
            stage_changed = False
            if self.current_stage is None or self.current_stage.get(
                    'stage_number') != stage_info['stage_number']:
                stage_changed = True
                self.previous_stage = self.current_stage
                self.current_stage = stage_info

            stage_info['stage_changed'] = stage_changed
            return stage_info

        except Exception as e:
            logger.error(f"Failed to determine current stage: {e}")
            return {
                "stage_number": 0,
                "stage_name": "未知阶段",
                "stage_description": "阶段判断失败",
                "stage_changed": False
            }

    def _fallback_stage_determination(
            self, current_blow_time: int) -> Dict[str, Any]:
        """备选的简单阶段判断（基于时间）"""
        if current_blow_time == 0:
            return {
                "stage_number": 1,
                "stage_name": "接收与装料",
                "stage_description": "准备阶段"
            }
        elif 1 <= current_blow_time <= 2:
            return {
                "stage_number": 2,
                "stage_name": "开吹与点火",
                "stage_description": "氧枪点火阶段"
            }
        elif 3 <= current_blow_time <= 6:
            return {
                "stage_number": 3,
                "stage_name": "前期脱硅期",
                "stage_description": "主要脱硅反应"
            }
        elif 7 <= current_blow_time <= 12:
            return {
                "stage_number": 4,
                "stage_name": "中期主脱碳期",
                "stage_description": "主要脱碳阶段"
            }
        elif 13 <= current_blow_time <= 16:
            return {
                "stage_number": 5,
                "stage_name": "后期拉碳期",
                "stage_description": "精确控制碳含量"
            }
        else:
            return {
                "stage_number": 6,
                "stage_name": "终点决策与出钢",
                "stage_description": "副枪测量决定出钢"
            }

    async def _generate_stage_1_description(
            self, latest_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成阶段1的描述"""
        try:
            prompt = f"""
作为钢铁冶炼专家，请为接收与装料阶段生成播报内容。

当前炉次数据：
- 炉次号：{latest_data.get('ID', 'N/A')}
- 目标钢种：{latest_data.get('steel_goal', 'N/A')}
- 目标温度：{latest_data.get('temp_goal', 'N/A')}

请按照格式生成播报内容："新任务已接收，XXX炉次开始吹炼，目标钢种XXX，目标温度XXXX"
只返回这句话，不要其他内容。
"""
            response = await self.llm.ainvoke(prompt)
            description = response.content.strip()

            return {
                "stage_number": 1,
                "stage_name": "接收与装料阶段",
                "stage_description": description
            }
        except Exception as e:
            logger.error(f"Failed to generate stage 1 description: {e}")
            return {
                "stage_number": 1,
                "stage_name": "接收与装料阶段",
                "stage_description":
                f"新任务已接收，{latest_data.get('ID', 'N/A')}炉次开始吹炼"
            }

    async def _generate_stage_2_description(
            self, latest_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成阶段2的描述"""
        try:
            prompt = f"""
作为钢铁冶炼专家，请为开吹与点火阶段生成播报内容。

当前炉次数据：
- 炉次号：{latest_data.get('ID', 'N/A')}

请按照格式生成播报内容："开吹，XXX炉次冶炼开始。动态吹炼模型已加载。"
只返回这句话，不要其他内容。
"""
            response = await self.llm.ainvoke(prompt)
            description = response.content.strip()

            return {
                "stage_number": 2,
                "stage_name": "开吹与点火阶段",
                "stage_description": description
            }
        except Exception as e:
            logger.error(f"Failed to generate stage 2 description: {e}")
            return {
                "stage_number":
                2,
                "stage_name":
                "开吹与点火阶段",
                "stage_description":
                f"开吹，{latest_data.get('ID', 'N/A')}炉次冶炼开始。动态吹炼模型已加载。"
            }

    async def _llm_determine_advanced_stage(
            self, latest_data: Dict[str, Any], all_data: List[Dict[str, Any]],
            current_blow_time: int) -> Dict[str, Any]:
        """使用大模型判断高级阶段（3-5阶段）- 基于operation之后字段的历史数据"""
        try:
            # 提取operation之后字段的历史数据用于分析
            operation_data_history = []
            for data in all_data:
                if data.get('operation') == '吹炼中':
                    operation_data_history.append({
                        'blowtime':
                        data.get('blowtime', '0'),
                        'temperature':
                        data.get('temperature', ''),
                        'o2_flowrate':
                        data.get('o2_flowrate', ''),
                        'co':
                        data.get('co', ''),
                        'gun_height':
                        data.get('gun_height', ''),
                        'blow_pres':
                        data.get('blow_pres', ''),
                        'o2_accum':
                        data.get('o2_accum', ''),
                        'lime_accum':
                        data.get('lime_accum', ''),
                        'si_accum':
                        data.get('si_accum', ''),
                        'mn_accum':
                        data.get('mn_accum', ''),
                        'fire_hue':
                        data.get('fire_hue', ''),
                        'fire_bright':
                        data.get('fire_bright', ''),
                        'fire_hard':
                        data.get('fire_hard', '')
                    })

            # 构建历史数据摘要
            history_summary = ""
            if operation_data_history:
                history_summary = f"历史吹炼数据（共{len(operation_data_history)}条记录）:\n"
                for i, data in enumerate(
                        operation_data_history[-60:]):  # 只显示最近5条
                    history_summary += f"第{data['blowtime']}分钟: 温度{data['temperature']}℃, 氧流量{data['o2_flowrate']}Nm³/min, 一氧化碳浓度{data['co']}%, 氧枪枪位高度{data['gun_height']}mm, 吹炼压力{data['blow_pres']}MPa, 累计氧量{data['o2_accum']}Nm³, 火焰色相{data['fire_hue']}, 火焰亮度{data['fire_bright']}, 火焰硬度{data['fire_hard']}\n"

            prompt = f"""
作为钢铁冶炼专家，请基于历史吹炼数据判断当前处于哪个吹炼阶段。

{history_summary}

当前最新数据：
- 吹炼时间：第{current_blow_time}分钟
- 当前温度：{latest_data.get('temperature', 'N/A')}℃
- 一氧化碳浓度：{latest_data.get('co', 'N/A')}%
- 氧气流量：{latest_data.get('o2_flowrate', 'N/A')} Nm³/min
- 累计氧量：{latest_data.get('o2_accum', 'N/A')} Nm³
- 火焰色相：{latest_data.get('fire_hue', 'N/A')}
- 火焰亮度：{latest_data.get('fire_bright', 'N/A')}  
- 火焰硬度：{latest_data.get('fire_hard', 'N/A')}
- 累计石灰：{latest_data.get('lime_accum', 'N/A')} kg

阶段选择：
3. 前期(脱硅期) - 播报："当前处于前期(脱硅期)"
4. 中期(主脱碳期) - 播报："当前处于中期(主脱碳期)"
5. 后期(拉碳期) - 播报："已进入拉碳期，预计X分钟后到达终点。请准备执行终点副枪测量。"，其中X请你来进行估计。

请判断当前阶段并生成对应的播报内容，以JSON格式返回：
{{
    "stage_number": 3-5,
    "stage_name": "阶段名称",
    "stage_description": "播报内容"
}}
"""
            response = await self.llm.ainvoke(prompt)
            content = response.content.strip()

            # 尝试解析JSON
            import json
            try:
                # 清理响应内容，提取JSON部分
                if "```json" in content:
                    content = content.split("```json")[1].split(
                        "```")[0].strip()
                elif "```" in content:
                    content = content.split("```")[1].strip()

                result = json.loads(content)
                return {
                    "stage_number":
                    result.get("stage_number", 3),
                    "stage_name":
                    result.get("stage_name", "前期(脱硅期)"),
                    "stage_description":
                    result.get("stage_description", "当前处于前期(脱硅期)")
                }
            except (json.JSONDecodeError, IndexError, KeyError) as e:
                logger.warning(f"Advanced stage determination failed: {e}")
                # 默认返回前期
                return {
                    "stage_number": 3,
                    "stage_name": "前期(脱硅期)",
                    "stage_description": "当前处于前期(脱硅期)"
                }
        except Exception as e:
            logger.error(f"Failed to determine advanced stage: {e}")
            return {
                "stage_number": 3,
                "stage_name": "前期(脱硅期)",
                "stage_description": "当前处于前期(脱硅期)"
            }

    async def _generate_parameters_analysis(
            self, latest_data: Dict[str, Any], all_data: List[Dict[str, Any]],
            current_stage_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成参数状态与趋势分析"""
        try:
            current_blow_time = int(latest_data.get('blowtime', 0) or 0)
            stage_number = current_stage_info.get('stage_number', 0)

            # 如果是接收与装料阶段，展示No到mn_predict之间的所有字段
            if stage_number == 1:
                # 展示No到mn_predict之间的准备阶段数据，处理None值
                def format_value(value, unit=""):
                    if value is None or value == "" or str(
                            value).lower() == 'none':
                        return "未设定"
                    return f"{value}{unit}"

                parameters_table = {
                    "记录编号": {
                        "current_value": format_value(latest_data.get('No')),
                        "change_value": "-",
                        "status": "待分析"
                    },
                    "炉次号": {
                        "current_value": format_value(latest_data.get('ID')),
                        "change_value": "-",
                        "status": "待分析"
                    },
                    "当前时间": {
                        "current_value":
                        format_value(latest_data.get('datetime')),
                        "change_value": "-",
                        "status": "待分析"
                    },
                    "吹炼时间": {
                        "current_value":
                        format_value(latest_data.get('blowtime'), "分钟"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "铁水温度": {
                        "current_value":
                        format_value(latest_data.get('ts_temp'), "℃"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "铁水重量": {
                        "current_value":
                        format_value(latest_data.get('ts_weight'), "吨"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "铁水初始含碳量": {
                        "current_value":
                        format_value(latest_data.get('ts_initial_c'), "%"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "铁水初始含硅量": {
                        "current_value":
                        format_value(latest_data.get('ts_initial_si'), "%"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "铁水初始含锰量": {
                        "current_value":
                        format_value(latest_data.get('ts_initial_mn'), "%"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "铁水初始含磷量": {
                        "current_value":
                        format_value(latest_data.get('ts_initial_p'), "%"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "铁水初始含硫量": {
                        "current_value":
                        format_value(latest_data.get('ts_initial_s'), "%"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "铁水来源": {
                        "current_value":
                        format_value(latest_data.get('ts_source')),
                        "change_value": "-",
                        "status": "待分析"
                    },
                    "是否脱硫": {
                        "current_value":
                        format_value(latest_data.get('ts_is_desul')),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "废钢重量": {
                        "current_value":
                        format_value(latest_data.get('fg_weight'), "吨"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "设备信息": {
                        "current_value":
                        format_value(latest_data.get('equip')),
                        "change_value": "-",
                        "status": "待分析"
                    },
                    "炉龄": {
                        "current_value":
                        format_value(latest_data.get('equip_age'), "炉次"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "前炉钢种": {
                        "current_value":
                        format_value(latest_data.get('pre_steel')),
                        "change_value": "-",
                        "status": "待分析"
                    },
                    "目标钢种": {
                        "current_value":
                        format_value(latest_data.get('steel_goal')),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "目标温度": {
                        "current_value":
                        format_value(latest_data.get('temp_goal'), "℃"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "目标碳含量": {
                        "current_value":
                        format_value(latest_data.get('c_goal'), "%"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "目标硅含量": {
                        "current_value":
                        format_value(latest_data.get('si_goal'), "%"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "目标锰含量": {
                        "current_value":
                        format_value(latest_data.get('mn_goal'), "%"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "目标磷含量": {
                        "current_value":
                        format_value(latest_data.get('p_goal'), "%"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "目标硫含量": {
                        "current_value":
                        format_value(latest_data.get('s_goal'), "%"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "预测总耗氧量": {
                        "current_value":
                        format_value(latest_data.get('o2_predict'), "Nm³"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "预测石灰消耗量": {
                        "current_value":
                        format_value(latest_data.get('lime_predict'), "kg"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "预测硅铁消耗量": {
                        "current_value":
                        format_value(latest_data.get('si_predict'), "kg"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    },
                    "预测锰铁消耗量": {
                        "current_value":
                        format_value(latest_data.get('mn_predict'), "kg"),
                        "change_value":
                        "-",
                        "status":
                        "待分析"
                    }
                }
            else:
                # 第二阶段及之后：展示operation之后的所有字段
                parameters_table = {}

                # 获取前一条数据进行对比
                prev_data = None
                if len(all_data) >= 2:
                    prev_data = all_data[-2]

                # 当前实际操作
                operation_current = latest_data.get('operation', '')
                operation_change = ""
                if prev_data:
                    operation_prev = prev_data.get('operation', '')
                    if operation_prev != operation_current:
                        operation_change = f"{operation_prev} → {operation_current}"

                parameters_table["当前实际操作"] = {
                    "current_value":
                    operation_current,
                    "change_value":
                    operation_change
                    if prev_data and operation_change else "-",
                    "status":
                    "待分析"
                }

                # 当前实际工艺
                process_current = latest_data.get('process', '')
                process_change = ""
                if prev_data:
                    process_prev = prev_data.get('process', '')
                    if process_prev != process_current:
                        process_change = f"{process_prev} → {process_current}"

                parameters_table["当前实际工艺"] = {
                    "current_value":
                    process_current,
                    "change_value":
                    process_change if prev_data and process_change else "-",
                    "status":
                    "待分析"
                }

                # 当前实际温度
                temp_current = float(latest_data.get('temperature', 0) or 0)
                temp_change = 0
                if prev_data:
                    temp_prev = float(prev_data.get('temperature', 0) or 0)
                    temp_change = temp_current - temp_prev

                parameters_table["当前实际温度"] = {
                    "current_value":
                    f"{temp_current}℃",
                    "change_value":
                    f"{temp_change:+.0f}"
                    if prev_data and temp_change != 0 else "-",
                    "status":
                    "待分析"
                }

                # 氧气流量
                o2_current = float(latest_data.get('o2_flowrate', 0) or 0)
                o2_change = 0
                if prev_data:
                    o2_prev = float(prev_data.get('o2_flowrate', 0) or 0)
                    o2_change = o2_current - o2_prev

                parameters_table["氧气流量"] = {
                    "current_value":
                    f"{o2_current} Nm³/min",
                    "change_value":
                    f"{o2_change:+.0f}"
                    if prev_data and o2_change != 0 else "-",
                    "status":
                    "待分析"
                }

                # 氧气流量
                co_current = float(latest_data.get('co', 0) or 0)
                co_change = 0
                if prev_data:
                    co_prev = float(prev_data.get('co', 0) or 0)
                    co_change = co_current - co_prev

                parameters_table["一氧化碳浓度"] = {
                    "current_value":
                    f"{co_current}%",
                    "change_value":
                    f"{co_change:+.0f}"
                    if prev_data and co_change != 0 else "-",
                    "status":
                    "待分析"
                }

                # 枪位高度
                gun_current = float(latest_data.get('gun_height', 0) or 0)
                gun_change = 0
                if prev_data:
                    gun_prev = float(prev_data.get('gun_height', 0) or 0)
                    gun_change = gun_current - gun_prev

                parameters_table["枪位高度"] = {
                    "current_value":
                    f"{gun_current} mm",
                    "change_value":
                    f"{gun_change:+.0f}"
                    if prev_data and gun_change != 0 else "-",
                    "status":
                    "待分析"
                }

                # 吹炼压力
                pressure_current = float(latest_data.get('blow_pres', 0) or 0)
                pressure_change = 0
                if prev_data:
                    pressure_prev = float(prev_data.get('blow_pres', 0) or 0)
                    pressure_change = pressure_current - pressure_prev

                parameters_table["吹炼压力"] = {
                    "current_value":
                    f"{pressure_current} MPa",
                    "change_value":
                    f"{pressure_change:+.2f}"
                    if prev_data and pressure_change != 0 else "-",
                    "status":
                    "待分析"
                }

                # 累计氧量
                o2_accum_current = float(latest_data.get('o2_accum', 0) or 0)
                o2_accum_change = 0
                if prev_data:
                    o2_accum_prev = float(prev_data.get('o2_accum', 0) or 0)
                    o2_accum_change = o2_accum_current - o2_accum_prev

                parameters_table["累计氧量"] = {
                    "current_value":
                    f"{o2_accum_current} Nm³",
                    "change_value":
                    f"+{o2_accum_change:.0f}"
                    if prev_data and o2_accum_change > 0 else "-",
                    "status":
                    "待分析"
                }

                # 累计石灰消耗量
                lime_accum_current = float(
                    latest_data.get('lime_accum', 0) or 0)
                lime_accum_change = 0
                if prev_data:
                    lime_accum_prev = float(
                        prev_data.get('lime_accum', 0) or 0)
                    lime_accum_change = lime_accum_current - lime_accum_prev

                parameters_table["累计石灰消耗量"] = {
                    "current_value":
                    f"{lime_accum_current} kg",
                    "change_value":
                    f"+{lime_accum_change:.0f}"
                    if prev_data and lime_accum_change > 0 else "-",
                    "status":
                    "待分析"
                }

                # 累计硅铁消耗量
                si_accum_current = float(latest_data.get('si_accum', 0) or 0)
                si_accum_change = 0
                if prev_data:
                    si_accum_prev = float(prev_data.get('si_accum', 0) or 0)
                    si_accum_change = si_accum_current - si_accum_prev

                parameters_table["累计硅铁消耗量"] = {
                    "current_value":
                    f"{si_accum_current} kg",
                    "change_value":
                    f"+{si_accum_change:.0f}"
                    if prev_data and si_accum_change > 0 else "-",
                    "status":
                    "待分析"
                }

                # 累计锰铁消耗量
                mn_accum_current = float(latest_data.get('mn_accum', 0) or 0)
                mn_accum_change = 0
                if prev_data:
                    mn_accum_prev = float(prev_data.get('mn_accum', 0) or 0)
                    mn_accum_change = mn_accum_current - mn_accum_prev

                parameters_table["累计锰铁消耗量"] = {
                    "current_value":
                    f"{mn_accum_current} kg",
                    "change_value":
                    f"+{mn_accum_change:.0f}"
                    if prev_data and mn_accum_change > 0 else "-",
                    "status":
                    "待分析"
                }

                # 喷溅发生时间
                pj_time_current = latest_data.get('pj_time', '')
                if pj_time_current:
                    parameters_table["喷溅发生时间"] = {
                        "current_value": str(pj_time_current),
                        "change_value": "-",
                        "status": "待分析"
                    }

                # 喷溅状态
                pj_status_current = latest_data.get('pj_status', '')
                if pj_status_current:
                    parameters_table["喷溅状态"] = {
                        "current_value": pj_status_current,
                        "change_value": "-",
                        "status": "待分析"
                    }

                # 喷溅持续时间
                pj_dura_current = latest_data.get('pj_dura', '')
                if pj_dura_current:
                    parameters_table["喷溅持续时间"] = {
                        "current_value": f"{pj_dura_current}s",
                        "change_value": "-",
                        "status": "待分析"
                    }

                # 火焰色相
                fire_hue_current = float(latest_data.get('fire_hue', 0) or 0)
                fire_hue_change = 0
                if prev_data:
                    fire_hue_prev = float(prev_data.get('fire_hue', 0) or 0)
                    fire_hue_change = fire_hue_current - fire_hue_prev

                parameters_table["火焰色相"] = {
                    "current_value":
                    f"{fire_hue_current}",
                    "change_value":
                    f"{fire_hue_change:+.0f}"
                    if prev_data and fire_hue_change != 0 else "-",
                    "status":
                    "待分析"
                }

                # 火焰亮度
                fire_bright_current = float(
                    latest_data.get('fire_bright', 0) or 0)
                fire_bright_change = 0
                if prev_data:
                    fire_bright_prev = float(
                        prev_data.get('fire_bright', 0) or 0)
                    fire_bright_change = fire_bright_current - fire_bright_prev

                parameters_table["火焰亮度"] = {
                    "current_value":
                    f"{fire_bright_current}",
                    "change_value":
                    f"{fire_bright_change:+.0f}"
                    if prev_data and fire_bright_change != 0 else "-",
                    "status":
                    "待分析"
                }

                # 火焰硬度
                fire_hard_current = float(latest_data.get('fire_hard', 0) or 0)
                fire_hard_change = 0
                if prev_data:
                    fire_hard_prev = float(prev_data.get('fire_hard', 0) or 0)
                    fire_hard_change = fire_hard_current - fire_hard_prev

                parameters_table["火焰硬度"] = {
                    "current_value":
                    f"{fire_hard_current}",
                    "change_value":
                    f"{fire_hard_change:+.2f}"
                    if prev_data and fire_hard_change != 0 else "-",
                    "status":
                    "待分析"
                }

            # 使用大模型分析参数状态
            analyzed_parameters_table = await self._llm_analyze_parameters_status(
                parameters_table, latest_data, current_stage_info)

            return {
                "stage_number":
                stage_number,
                "parameters_table":
                analyzed_parameters_table,
                "analysis_type":
                "preparation_data" if stage_number == 1 else "time_series_data"
            }

        except Exception as e:
            logger.error(f"Failed to generate parameters analysis: {e}")
            return {
                "stage_number": 0,
                "parameters_table": {},
                "analysis_type": "error",
                "error": "参数分析生成失败"
            }

    async def _llm_analyze_parameters_status(
            self, parameters_table: Dict[str, Dict[str, str]],
            latest_data: Dict[str, Any],
            current_stage_info: Dict[str, Any]) -> Dict[str, Dict[str, str]]:
        """使用大模型分析参数状态"""
        try:
            # 构建参数分析提示
            param_info = []
            for param_name, param_data in parameters_table.items():
                param_info.append(
                    f"- {param_name}: {param_data['current_value']}, 变化: {param_data['change_value']}"
                )

            prompt = f"""
作为钢铁冶炼专家，请分析以下参数的状态是否正常。

当前阶段：{current_stage_info.get('stage_name', '未知')}
吹炼时间：第{latest_data.get('blowtime', 0)}分钟

参数列表：
{chr(10).join(param_info)}

请对每个参数判断状态（正常/异常/警告），以JSON格式返回：
{{
    "参数名1": "正常/异常/警告",
    "参数名2": "正常/异常/警告",
    ...
}}

判断标准：
- 正常：参数值在合理范围内，变化趋势正常
- 警告：参数值接近边界或变化趋势需要关注
- 异常：参数值超出正常范围或变化异常
"""

            # 调用大模型
            response = await self.llm.ainvoke(prompt)

            # 解析响应
            content = response.content.strip()

            # 尝试解析JSON
            import json
            try:
                # 清理响应内容，提取JSON部分
                if "```json" in content:
                    content = content.split("```json")[1].split(
                        "```")[0].strip()
                elif "```" in content:
                    content = content.split("```")[1].strip()

                status_results = json.loads(content)
                # 更新参数表格的状态
                for param_name, param_data in parameters_table.items():
                    if param_name in status_results:
                        param_data["status"] = status_results[param_name]
                    else:
                        param_data["status"] = "正常"  # 默认正常
                return parameters_table
            except (json.JSONDecodeError, IndexError, KeyError) as e:
                logger.warning(
                    f"Parameter status analysis JSON parse failed: {e}")
                # 如果JSON解析失败，设置默认状态
                for param_name, param_data in parameters_table.items():
                    param_data["status"] = "正常"
                return parameters_table

        except Exception as e:
            logger.error(f"LLM parameter analysis failed: {e}")
            # 如果分析失败，设置默认状态并返回原表格
            for param_name, param_data in parameters_table.items():
                if param_data.get("status") == "待分析":
                    param_data["status"] = "正常"
            return parameters_table

    def _detect_material_additions(
            self, latest_data: Dict[str, Any],
            all_data: List[Dict[str, Any]]) -> List[str]:
        """检测累计消耗量变化，生成加料记录"""
        additions = []

        # 获取前一条数据进行对比
        if len(all_data) < 2:
            return additions

        prev_data = all_data[-2]

        # 检查石灰累计消耗量变化
        current_lime = float(latest_data.get('lime_accum', 0) or 0)
        prev_lime = float(prev_data.get('lime_accum', 0) or 0)
        lime_addition = current_lime - prev_lime
        if lime_addition > 0:
            additions.append(
                f"加入石灰 {lime_addition:.0f}kg，累计消耗量从 {prev_lime:.0f}kg 增加到 {current_lime:.0f}kg"
            )

        # 检查硅铁累计消耗量变化
        current_si = float(latest_data.get('si_accum', 0) or 0)
        prev_si = float(prev_data.get('si_accum', 0) or 0)
        si_addition = current_si - prev_si
        if si_addition > 0:
            additions.append(
                f"加入硅铁 {si_addition:.0f}kg，累计消耗量从 {prev_si:.0f}kg 增加到 {current_si:.0f}kg"
            )

        # 检查锰铁累计消耗量变化
        current_mn = float(latest_data.get('mn_accum', 0) or 0)
        prev_mn = float(prev_data.get('mn_accum', 0) or 0)
        mn_addition = current_mn - prev_mn
        if mn_addition > 0:
            additions.append(
                f"加入锰铁 {mn_addition:.0f}kg，累计消耗量从 {prev_mn:.0f}kg 增加到 {current_mn:.0f}kg"
            )

        return additions

    async def _generate_risk_and_suggestions(
            self, latest_data: Dict[str, Any], all_data: List[Dict[str, Any]],
            current_stage_info: Dict[str, Any],
            current_blow_time: int) -> Dict[str, Any]:
        """生成风险提示与操作建议（使用大模型分析）"""
        try:
            material_reminders = []

            # 检查累计消耗量变化，生成加料记录播报
            material_additions = self._detect_material_additions(
                latest_data, all_data)
            if material_additions:
                for addition in material_additions:
                    material_reminders.append(f"🔥 加料记录：{addition}")

            # 根据阶段选择不同的分析方式
            stage_number = current_stage_info.get('stage_number', 0)
            if stage_number == 1:
                # 第一阶段：只分析准备数据
                analysis_result = await self._llm_analyze_preparation_risks(
                    latest_data, current_blow_time)
            else:
                # 其他阶段：分析完整的吹炼数据
                analysis_result = await self._llm_analyze_blowing_risks(
                    latest_data, all_data, current_stage_info,
                    current_blow_time)

            # 合并加料提醒
            analysis_result["material_reminders"] = material_reminders

            return analysis_result

        except Exception as e:
            logger.error(f"Failed to generate risk and suggestions: {e}")
            return {
                "status": "error",
                "message": "风险分析生成失败",
                "risks": [],
                "suggestions": [],
                "material_reminders": [],
                "error": str(e)
            }

    async def _llm_analyze_preparation_risks(
            self, latest_data: Dict[str, Any],
            current_blow_time: int) -> Dict[str, Any]:
        """分析第一阶段准备数据的风险"""
        try:
            prompt = f"""
作为钢铁冶炼专家，请分析接收与装料阶段的风险和建议。

准备数据：
{self._format_data_for_analysis(latest_data, [
    ('ts_temp', '铁水温度', '℃'),
    ('ts_weight', '铁水重量', '吨'),
    ('ts_initial_c', '铁水初始含碳量', '%'),
    ('ts_initial_si', '铁水初始含硅量', '%'),
    ('ts_initial_mn', '铁水初始含锰量', '%'),
    ('ts_initial_p', '铁水初始含磷量', '%'),
    ('ts_initial_s', '铁水初始含硫量', '%'),
    ('ts_source', '铁水来源', ''),
    ('ts_is_desul', '是否脱硫', ''),
    ('fg_weight', '废钢重量', '吨'),
    ('steel_goal', '目标钢种', ''),
    ('temp_goal', '目标终点温度', '℃'),
    ('c_goal', '目标终点碳含量', '%'),
    ('si_goal', '目标终点硅含量', '%'),
    ('mn_goal', '目标终点锰含量', '%'),
    ('p_goal', '目标终点磷含量', '%'),
    ('s_goal', '目标终点硫含量', '%'),
    ('o2_predict', '预测总耗氧量', 'Nm³'),
    ('lime_predict', '预测石灰消耗量', 'kg'),
    ('si_predict', '预测硅铁消耗量', 'kg'),
    ('mn_predict', '预测锰铁消耗量', 'kg'),
    ('equip_age', '炉龄', '炉次')
])}

要求：()
1. 风险分析中对这些吹炼开始之前的数据进行初步评价；
2. 操作建议中预估整个吹炼过程的持续时间，以及辅料跟冷料的加入规划；
3. 只分析有具体数据的参数，忽略无数据的参数；
4. 所有的风险和建议都要尽量精简，不要说废话。

请以JSON格式返回：
{{
    "status": "normal/attention_needed/critical",
    "message": "准备阶段状态简述",
    "risks": ["具体风险1", "具体风险2"],
    "suggestions": ["具体建议1", "具体建议2"]
}}
"""
            response = await self.llm.ainvoke(prompt)
            content = response.content.strip()

            # 解析JSON
            import json
            try:
                if "```json" in content:
                    content = content.split("```json")[1].split(
                        "```")[0].strip()
                elif "```" in content:
                    content = content.split("```")[1].strip()

                result = json.loads(content)
                return {
                    "status": result.get("status", "normal"),
                    "message": result.get("message", "准备阶段分析完成"),
                    "risks": result.get("risks", []),
                    "suggestions": result.get("suggestions", [])
                }
            except (json.JSONDecodeError, IndexError, KeyError) as e:
                logger.warning(
                    f"Preparation risk analysis JSON parse failed: {e}")
                return {
                    "status": "normal",
                    "message": "准备阶段数据正常",
                    "risks": [],
                    "suggestions": []
                }
        except Exception as e:
            logger.error(f"Preparation risk analysis failed: {e}")
            return {
                "status": "error",
                "message": "准备阶段分析失败",
                "risks": [],
                "suggestions": []
            }

    async def _llm_analyze_blowing_risks(
            self, latest_data: Dict[str, Any], all_data: List[Dict[str, Any]],
            current_stage_info: Dict[str, Any],
            current_blow_time: int) -> Dict[str, Any]:
        """分析吹炼阶段的风险和建议"""
        try:
            # 检测加料记录
            material_additions = self._detect_material_additions(
                latest_data, all_data)
            material_info = ""
            if material_additions:
                material_info = f"""加料记录：
{chr(10).join([f"- {addition}" for addition in material_additions])}"""

            # 构建历史数据（只包含operation之后的关键数据）
            history_data = ""
            blowing_data = [
                data for data in all_data
                if data.get('operation') in ['吹炼中', '出钢中']
            ]
            if blowing_data:
                history_data = "历史吹炼数据（按时间顺序）:\n"
                for i, data in enumerate(blowing_data):
                    history_data += f"第{data.get('blowtime', '0')}分钟: "
                    history_data += f"温度{data.get('temperature', 'N/A')}℃, "
                    history_data += f"氧气流量{data.get('o2_flowrate', 'N/A')}, "
                    history_data += f"一氧化碳浓度{data.get('co', 'N/A')}%, "
                    history_data += f"累计氧量{data.get('o2_accum', 'N/A')}, "
                    history_data += f"氧枪枪位{data.get('gun_height', 'N/A')}mm, "
                    history_data += f"吹炼压力{data.get('blow_pres', 'N/A')}MPa, "
                    history_data += f"石灰{data.get('lime_accum', 'N/A')}kg, "
                    history_data += f"硅铁{data.get('si_accum', 'N/A')}kg, "
                    history_data += f"锰铁{data.get('mn_accum', 'N/A')}kg, "
                    history_data += f"火焰色相{data.get('fire_hue', 'N/A')}, "
                    history_data += f"火焰亮度{data.get('fire_bright', 'N/A')}, "
                    history_data += f"火焰硬度{data.get('fire_hard', 'N/A')}"
                    if data.get('pj_time'):
                        history_data += f", 喷溅状态{data.get('pj_status', 'N/A')}, 持续{data.get('pj_dura', 'N/A')}s"
                    history_data += "\n"

            # 构建分析提示
            prompt = f"""
作为钢铁冶炼专家，请基于整个炉次的历史数据分析当前吹炼过程的风险和操作建议。

{history_data}

当前炉次数据：
- 炉次号：{latest_data.get('ID', 'N/A')}
- 当前吹炼时间：第{current_blow_time}分钟
- 当前阶段：{current_stage_info.get('stage_name', '未知阶段')}

关键参数：
{self._format_data_for_analysis(latest_data, [
    ('operation', '当前实际操作', ''),
    ('process', '当前实际工艺', ''),
    ('temperature', '当前实际温度', '℃'),
    ('o2_flowrate', '氧气流量', ' Nm³/min'),
    ('co', '一氧化碳浓度', '%'),
    ('gun_height', '枪位高度', ' mm'),
    ('blow_pres', '吹炼压力', ' MPa'),
    ('o2_accum', '累计氧量', ' Nm³'),
    ('lime_accum', '累计石灰消耗量', ' kg'),
    ('si_accum', '累计硅铁消耗量', ' kg'),
    ('mn_accum', '累计锰铁消耗量', ' kg'),
    ('fire_hue', '火焰色相', ''),
    ('fire_bright', '火焰亮度', ''),
    ('fire_hard', '火焰硬度', '')
])}

喷溅信息：
{self._format_data_for_analysis(latest_data, [
    ('pj_time', '喷溅发生时间', ''),
    ('pj_status', '喷溅状态', ''),
    ('pj_dura', '喷溅持续时间', 's')
])}

{material_info}

分析要求：
1. 基于历史吹炼数据分析当前工艺状态是否正常，识别参数变化趋势和异常点
2. 如果有喷溅发生，结合历史数据分析喷溅原因和对后续工艺的影响。喷溅状态范围为-0.5到4.5，其中，-0.5到0是金属喷溅，0到1是正常，1到2是喷溅，2以上是大喷溅，只有在喷溅状态大于等于1时才需要进行分析
3. 如果有加料记录，分析加料时机和数量是否合理，对工艺参数的影响
4. 建议要基于历史趋势，具体可操作，内容精简
5. 用陈述性语言描述，不要直接输出JSON格式的数据

请以JSON格式返回：
{{
    "status": "normal/attention_needed/critical",
    "message": "基于历史数据的工艺状态简述",
    "risks": ["具体风险1", "具体风险2"],
    "suggestions": ["具体建议1", "具体建议2"]
}}
"""

            # 调用大模型
            response = await self.llm.ainvoke(prompt)

            # 解析响应
            content = response.content.strip()

            # 尝试解析JSON
            import json
            try:
                # 清理响应内容，提取JSON部分
                if "```json" in content:
                    content = content.split("```json")[1].split(
                        "```")[0].strip()
                elif "```" in content:
                    content = content.split("```")[1].strip()

                result = json.loads(content)
                return {
                    "status": result.get("status", "normal"),
                    "message": result.get("message", "分析完成"),
                    "risks": result.get("risks", []),
                    "suggestions": result.get("suggestions", []),
                    "material_reminders": []  # 将在调用处添加
                }
            except (json.JSONDecodeError, IndexError, KeyError) as e:
                logger.warning(f"Risk analysis JSON parse failed: {e}")
                # 如果JSON解析失败，返回默认结果
                return {
                    "status": "normal",
                    "message": "大模型分析完成，当前工艺状态正常",
                    "risks": [],
                    "suggestions": ["继续按当前参数进行吹炼"],
                    "material_reminders": []
                }

        except Exception as e:
            logger.error(f"LLM risk analysis failed: {e}")
            return {
                "status": "error",
                "message": "风险分析失败",
                "risks": [],
                "suggestions": [],
                "material_reminders": []
            }

    def _format_data_for_analysis(self, data: Dict[str, Any],
                                  fields: List[tuple]) -> str:
        """格式化数据用于分析，只包含有数据的字段"""
        formatted_lines = []
        for field_key, field_name, unit in fields:
            value = data.get(field_key)
            if value is not None and value != '' and str(
                    value).lower() != 'none':
                unit_str = unit if unit else ''
                formatted_lines.append(f"- {field_name}：{value}{unit_str}")
        return '\n'.join(formatted_lines) if formatted_lines else "- 暂无有效数据"

    def _clear_emergency_data(self):
        """清理紧急监控数据"""
        self.emergency_monitoring_data = None
        self.emergency_monitoring_event.clear()

    async def trigger_immediate_monitoring(self, event_time_str, task_type):
        """立即触发监控（由观火系统调用）- 打断式执行"""
        try:
            logger.info(f"收到{task_type}紧急监控请求，时间: {event_time_str}")

            # 设置紧急监控数据
            self.emergency_monitoring_data = (event_time_str, task_type)

            # 触发紧急监控事件（这会立即打断当前的60秒等待）
            self.emergency_monitoring_event.set()

            logger.info(f"{task_type}紧急监控已触发，将打断当前定时监控")

        except Exception as e:
            logger.error(f"触发{task_type}紧急监控失败: {e}")

    # ==================== 观火系统事件通知WebSocket服务器 ====================

    async def start_splash_websocket_server(self, port=8005):
        """启动观火系统事件通知WebSocket服务器"""
        try:
            import websockets

            BlowingAgent._shared_splash_websocket_server = await websockets.serve(
                self.handle_vision_system_notification, "0.0.0.0", port)
            BlowingAgent._websocket_server_port = port
            logger.info(f"观火系统事件通知WebSocket服务器已启动，端口: {port}")
        except Exception as e:
            logger.error(f"启动观火系统WebSocket服务器失败: {e}")
            # 即使启动失败，也标记为已尝试，避免重复启动
            BlowingAgent._shared_splash_websocket_server = "failed"

    async def stop_splash_websocket_server(self):
        """停止观火系统事件通知WebSocket服务器"""
        try:
            if BlowingAgent._shared_splash_websocket_server and BlowingAgent._shared_splash_websocket_server != "failed":
                BlowingAgent._shared_splash_websocket_server.close()
                await BlowingAgent._shared_splash_websocket_server.wait_closed(
                )
                logger.info("观火系统事件通知WebSocket服务器已停止")
            BlowingAgent._shared_splash_websocket_server = None
            BlowingAgent._websocket_server_port = None
        except Exception as e:
            logger.error(f"停止观火系统WebSocket服务器时出错: {e}")
            BlowingAgent._shared_splash_websocket_server = None
            BlowingAgent._websocket_server_port = None

    async def handle_vision_system_notification(self, websocket, path):
        """处理观火系统的事件通知"""
        # path参数是WebSocket服务器回调函数的标准参数
        _ = path  # 标记为已使用，避免警告
        try:
            await websocket.send("connected")
            logger.info("观火系统已连接到事件通知服务")

            async for message in websocket:
                logger.info(f"收到观火系统事件通知: {message}")

                try:
                    # 解析序列化字符串: {task: "喷溅", time: "2025-08-28 14:30:25"}
                    task = ""
                    event_time = ""

                    if "task:" in message and "time:" in message:
                        # 提取task
                        task_start = message.find('task:') + 5
                        task_end = message.find(',', task_start)
                        if task_end == -1:
                            task_end = message.find('}', task_start)
                        task = message[task_start:task_end].strip().strip(
                            '"').strip("'").strip()

                        # 提取time
                        time_start = message.find('time:') + 5
                        time_end = message.find('}', time_start)
                        event_time = message[time_start:time_end].strip(
                        ).strip('"').strip("'").strip()

                    if task and event_time:
                        # 立即触发紧急监控
                        await self.trigger_immediate_monitoring(
                            event_time, task)

                        await websocket.send(
                            json.dumps({
                                "status": "success",
                                "message": f"{task}事件通知已处理",
                                "task": task,
                                "processed_time": event_time
                            }))
                    else:
                        await websocket.send(
                            json.dumps({
                                "status": "error",
                                "message": "无法解析task或time参数"
                            }))

                except Exception as parse_error:
                    logger.error(f"解析观火系统消息失败: {parse_error}")
                    await websocket.send(
                        json.dumps({
                            "status": "error",
                            "message": "消息格式错误"
                        }))

        except Exception as conn_error:
            if "ConnectionClosed" in str(type(conn_error)):
                logger.info("观火系统断开连接")
            else:
                logger.error(f"处理观火系统消息时出错: {conn_error}")
