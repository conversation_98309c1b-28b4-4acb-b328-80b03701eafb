"""测试数据库连接功能"""
import pytest
from unittest.mock import Mock, patch, MagicMock
import sqlalchemy
from sqlalchemy.exc import OperationalError

from database.connection import (
    get_db_uri,
    create_engine,
    get_engine,
    get_db_session,
    test_connection as db_test_connection  # 重命名以避免pytest将其识别为测试
)


class TestDatabaseConnection:
    """测试数据库连接相关功能"""
    
    def test_get_db_uri(self, mock_settings):
        """测试数据库URI生成"""
        with patch("database.connection.settings", mock_settings):
            uri = get_db_uri()
            expected_uri = "mysql+mysqlconnector://test_user:test_password@localhost:3306/test_db"
            assert uri == expected_uri
    
    def test_create_engine_success(self, mock_settings):
        """测试成功创建数据库引擎"""
        with patch("database.connection.settings", mock_settings):
            with patch("sqlalchemy.create_engine") as mock_create_engine:
                mock_engine = Mock()
                mock_create_engine.return_value = mock_engine
                
                engine = create_engine()
                
                # 验证create_engine被正确调用
                mock_create_engine.assert_called_once()
                call_args = mock_create_engine.call_args
                
                # 验证URI
                assert call_args[0][0] == "mysql+mysqlconnector://test_user:test_password@localhost:3306/test_db"
                
                # 验证连接池配置
                assert call_args[1]["pool_size"] == 10
                assert call_args[1]["max_overflow"] == 20
                assert call_args[1]["pool_pre_ping"] is True
                assert call_args[1]["pool_recycle"] == 3600
                
                assert engine == mock_engine
    
    def test_create_engine_failure(self, mock_settings):
        """测试创建数据库引擎失败"""
        with patch("database.connection.settings", mock_settings):
            with patch("sqlalchemy.create_engine") as mock_create_engine:
                mock_create_engine.side_effect = Exception("Connection failed")
                
                with pytest.raises(Exception) as exc_info:
                    create_engine()
                
                assert "数据库连接失败" in str(exc_info.value)
    
    def test_get_engine_singleton(self, mock_settings):
        """测试get_engine单例模式"""
        with patch("database.connection.settings", mock_settings):
            with patch("database.connection.create_engine") as mock_create:
                mock_engine = Mock()
                mock_create.return_value = mock_engine
                
                # 重置全局引擎
                import database.connection
                database.connection._engine = None
                
                # 第一次调用
                engine1 = get_engine()
                assert engine1 == mock_engine
                mock_create.assert_called_once()
                
                # 第二次调用应该返回相同的实例
                engine2 = get_engine()
                assert engine2 == engine1
                # create_engine应该只被调用一次
                mock_create.assert_called_once()
                
                # 清理
                database.connection._engine = None
    
    def test_get_db_session(self, mock_settings):
        """测试获取数据库会话"""
        with patch("database.connection.settings", mock_settings):
            with patch("database.connection.get_engine") as mock_get_engine:
                mock_engine = Mock()
                mock_get_engine.return_value = mock_engine
                
                with patch("database.connection.sessionmaker") as mock_sessionmaker:
                    mock_session_class = Mock()
                    mock_session = Mock()
                    mock_session_class.return_value = mock_session
                    mock_sessionmaker.return_value = mock_session_class
                    
                    session = get_db_session()
                    
                    # 验证sessionmaker配置
                    mock_sessionmaker.assert_called_once_with(
                        autocommit=False,
                        autoflush=False,
                        bind=mock_engine
                    )
                    
                    assert session == mock_session
    
    def test_test_connection_success(self, mock_settings):
        """测试数据库连接测试成功"""
        with patch("database.connection.settings", mock_settings):
            with patch("database.connection.get_engine") as mock_get_engine:
                # 创建一个新的mock引擎，确保正确配置
                mock_engine = Mock()
                
                # 创建mock连接
                mock_conn = Mock()
                mock_result = Mock()
                mock_result.fetchone.return_value = (1,)
                mock_conn.execute.return_value = mock_result
                
                # 创建mock上下文管理器
                mock_context_manager = Mock()
                mock_context_manager.__enter__ = Mock(return_value=mock_conn)
                mock_context_manager.__exit__ = Mock(return_value=None)
                
                mock_engine.connect.return_value = mock_context_manager
                mock_get_engine.return_value = mock_engine
                
                # 还需要mock sqlalchemy.text
                with patch("database.connection.sqlalchemy.text") as mock_text:
                    mock_text.return_value = "SELECT 1"
                    
                    result = db_test_connection()
                    
                    assert result is True
                    mock_engine.connect.assert_called_once()
                    mock_conn.execute.assert_called_once()
    
    def test_test_connection_failure(self, mock_settings):
        """测试数据库连接测试失败"""
        with patch("database.connection.settings", mock_settings):
            with patch("database.connection.get_engine") as mock_get_engine:
                mock_engine = Mock()
                mock_engine.connect.side_effect = OperationalError(
                    "Connection refused", None, None
                )
                mock_get_engine.return_value = mock_engine
                
                result = db_test_connection()
                
                assert result is False
                mock_engine.connect.assert_called_once()


class TestDatabaseURI:
    """测试数据库URI生成的各种情况"""
    
    def test_uri_with_special_characters(self):
        """测试包含特殊字符的URI"""
        mock_settings = Mock()
        mock_settings.DB_DIALECT = "mysql"
        mock_settings.DB_DRIVER = "mysqlconnector"
        mock_settings.DB_USERNAME = "user@host"
        mock_settings.DB_PASSWORD = "pass@word#123"
        mock_settings.DB_HOST = "***********"
        mock_settings.DB_PORT = "3307"
        mock_settings.DB_NAME = "test_db"
        
        with patch("database.connection.settings", mock_settings):
            uri = get_db_uri()
            # 注意：实际使用时可能需要URL编码特殊字符
            expected = "mysql+mysqlconnector://user@host:pass@word#123@***********:3307/test_db"
            assert uri == expected
    
    def test_uri_with_different_dialects(self):
        """测试不同的数据库方言"""
        test_cases = [
            ("postgresql", "psycopg2", "postgresql+psycopg2://"),
            ("sqlite", "pysqlite", "sqlite+pysqlite://"),
            ("oracle", "cx_oracle", "oracle+cx_oracle://"),
        ]
        
        for dialect, driver, expected_prefix in test_cases:
            mock_settings = Mock()
            mock_settings.DB_DIALECT = dialect
            mock_settings.DB_DRIVER = driver
            mock_settings.DB_USERNAME = "user"
            mock_settings.DB_PASSWORD = "pass"
            mock_settings.DB_HOST = "host"
            mock_settings.DB_PORT = "5432"
            mock_settings.DB_NAME = "db"
            
            with patch("database.connection.settings", mock_settings):
                uri = get_db_uri()
                assert uri.startswith(expected_prefix)
