"""
最简单的WebSocket测试客户端
用于调试连接和消息接收问题
"""

import asyncio
import websockets
import json


async def test_client():
    """测试客户端"""
    url = "ws://localhost:8001/ws/agent"
    
    print(f"正在连接到 {url}...")
    
    try:
        async with websockets.connect(url) as websocket:
            print("连接成功！")
            
            # 接收消息的任务
            async def receive_messages():
                try:
                    while True:
                        message = await websocket.recv()
                        data = json.loads(message)
                        print(f"\n[收到消息] 类型: {data.get('type')}")
                        print(f"完整数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                        
                        # 特别处理 content 类型的消息
                        if data.get('type') == 'content':
                            print(f"[内容]: {data.get('content')}", end='', flush=True)
                            
                except websockets.exceptions.ConnectionClosed:
                    print("\n连接已关闭")
                except Exception as e:
                    print(f"\n接收错误: {e}")
                    
            # 发送消息的任务
            async def send_messages():
                await asyncio.sleep(1)  # 等待接收欢迎消息
                
                # 发送测试消息
                test_message = {
                    "type": "query",
                    "content": "你好",
                    "context": {}
                }
                
                print(f"\n发送消息: {test_message}")
                await websocket.send(json.dumps(test_message))
                
                # 等待响应
                await asyncio.sleep(10)  # 等待10秒接收响应
                
            # 同时运行接收和发送
            receive_task = asyncio.create_task(receive_messages())
            send_task = asyncio.create_task(send_messages())
            
            # 等待任务完成
            await asyncio.gather(receive_task, send_task)
            
    except Exception as e:
        print(f"连接错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("=== 简单WebSocket测试客户端 ===")
    asyncio.run(test_client())