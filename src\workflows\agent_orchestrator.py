from typing import AsyncGenerator, Dict, Any, Optional
from enum import Enum
from src.config.logging import logger
from backend.services import ConnectionManager
from agents import BlowingAgent, VisionAgent, AnalyticsAgent, DecisionAgent


class IntentType(Enum):
    """意图类型枚举（临时定义，仅用于展示）"""
    BLOWING_CONTROL = "吹炼控制"
    VISION_ANALYSIS = "视觉分析"
    DATA_ANALYTICS = "数据分析"
    DECISION_SUPPORT = "决策支持"
    GENERAL = "通用查询"


class AgentOrchestrator:
    """智能体编排器，负责路由和协调不同智能体
    
    注意：这是一个展示性的实现，当前项目暂不使用此功能
    """

    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager

        # 初始化各智能体
        self.agents = {
            IntentType.BLOWING_CONTROL: BlowingAgent(),
            IntentType.VISION_ANALYSIS: VisionAgent(),
            IntentType.DATA_ANALYTICS: AnalyticsAgent(),
            IntentType.DECISION_SUPPORT: DecisionAgent(),
        }

        self.initialized = False

    async def initialize(self):
        """初始化编排器"""
        logger.info("Initializing AgentOrchestrator...")

        # 初始化各智能体（如果需要）
        for agent_type, agent in self.agents.items():
            if hasattr(agent, 'initialize'):
                await agent.initialize()
            logger.info(f"Initialized {agent_type.value} agent")

        self.initialized = True

    async def cleanup(self):
        """清理资源"""
        logger.info("Cleaning up AgentOrchestrator...")

        # 清理各智能体
        for agent_type, agent in self.agents.items():
            if hasattr(agent, 'cleanup'):
                await agent.cleanup()

        self.initialized = False

    async def process_query(
            self, client_id: str, query: str,
            context: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """处理用户查询"""
        try:
            # 1. 简单的意图分类（仅用于展示）
            intent = self._simple_classify(query)

            yield {
                "type": "intent_detected",
                "intent": intent.value,
                "confidence": context.get("confidence", 0.9)
            }

            # 2. 获取对应的智能体
            agent = self.agents.get(intent)
            if not agent:
                yield {
                    "type": "error",
                    "message": f"未找到处理 {intent.value} 的智能体"
                }
                return

            # 3. 处理查询
            yield {
                "type": "processing",
                "agent": intent.value,
                "message": f"正在使用{intent.value}处理您的请求..."
            }

            # 4. 调用智能体处理
            if hasattr(agent, 'process_stream'):
                # 如果智能体支持流式处理
                async for result in agent.process_stream(query, context):
                    yield {
                        "type": "stream_response",
                        "agent": intent.value,
                        "content": result
                    }
            else:
                # 否则使用普通处理
                result = await agent.process(query, context)
                yield {
                    "type": "response",
                    "agent": intent.value,
                    "content": result,
                    "status": "completed"
                }

        except Exception as e:
            logger.error(f"Error processing query: {e}")
            yield {"type": "error", "message": f"处理请求时发生错误：{str(e)}"}

    def _simple_classify(self, query: str) -> IntentType:
        """简单的意图分类（仅用于展示）"""
        query_lower = query.lower()

        if any(word in query_lower for word in ["吹炼", "转炉", "氧气", "温度"]):
            return IntentType.BLOWING_CONTROL
        elif any(word in query_lower for word in ["火焰", "图像", "视频", "视觉"]):
            return IntentType.VISION_ANALYSIS
        elif any(word in query_lower for word in ["数据", "分析", "统计", "趋势"]):
            return IntentType.DATA_ANALYTICS
        elif any(word in query_lower for word in ["决策", "建议", "优化", "KPI"]):
            return IntentType.DECISION_SUPPORT
        else:
            return IntentType.GENERAL
