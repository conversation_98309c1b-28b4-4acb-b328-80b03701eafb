# 吹炼智能体 WebSocket 部署和测试指南

## 部署步骤

### 1. 环境准备

确保已激活正确的虚拟环境：
```bash
conda activate AISteelMaking
```

### 2. 检查依赖

确保所有必要的依赖已安装：
```bash
# 检查 FastAPI 和 WebSocket 相关依赖
pip list | grep -E "(fastapi|websockets|uvicorn)"

# 如果缺少依赖，安装：
pip install fastapi websockets uvicorn
```

### 3. 验证代码结构

确保以下文件存在且正确：
```
src/backend/api/websocket/
├── router.py                    # ✅ 已更新，包含 blowing 路由
├── blowing.py                   # ✅ 已完全重写
├── blowing_client_example.py    # ✅ 新增客户端示例
├── BLOWING_WEBSOCKET_API.md     # ✅ 新增 API 文档
└── DEPLOYMENT_GUIDE.md          # ✅ 本文件
```

### 4. 启动服务器

在项目根目录下启动服务器：
```bash
# 方法1：使用 uvicorn 直接启动
uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

# 方法2：如果有启动脚本
python src/main.py

# 方法3：如果有 Docker
docker-compose up -d
```

### 5. 验证服务器启动

检查服务器是否正常启动：
```bash
# 检查端口是否监听
netstat -an | grep 8000

# 或者使用 curl 检查 HTTP 端点
curl http://localhost:8000/health
```

## 测试步骤

### 1. 基础连接测试

使用提供的客户端示例进行测试：
```bash
# 进入 WebSocket 目录
cd src/backend/api/websocket

# 运行客户端示例
python blowing_client_example.py
```

### 2. 手动测试流程

#### 测试1：简单连接测试
```bash
# 使用 websocat 工具测试（如果已安装）
websocat ws://localhost:8000/ws/agent/blowing

# 发送连接测试消息
{"type": "ping"}

# 预期响应
{"type": "pong", "agent": "blowing"}
```

#### 测试2：完整吹炼流程测试
```bash
# 1. 启动吹炼过程
{"type": "start_process", "start_time": "2025-08-13 08:00:00"}

# 2. 第一阶段播报
{"type": "stage_broadcast", "stage_info": {"stage_number": 1, "stage_name": "准备阶段"}, "broadcast_time": "2025-08-13 08:00:00"}

# 3. 查询状态
{"type": "status"}

# 4. 第二阶段播报
{"type": "stage_broadcast", "stage_info": {"stage_number": 2, "stage_name": "脱碳阶段"}, "broadcast_time": "2025-08-13 08:05:00"}

# 5. 停止吹炼过程
{"type": "stop_process"}
```

### 3. 自动化测试

运行吹炼智能体的单元测试：
```bash
# 运行阶段播报测试
python src/agents/blowing_agent/test_stage_broadcast.py

# 运行实时监控测试
python src/agents/blowing_agent/test_real_time_monitoring.py

# 运行优化功能测试
python src/agents/blowing_agent/test_optimized_functions.py
```

## 故障排除

### 常见问题及解决方案

#### 1. 连接失败
```
错误: 无法连接到 WebSocket 服务器
解决: 
- 检查服务器是否启动
- 检查端口 8000 是否被占用
- 检查防火墙设置
```

#### 2. 导入错误
```
错误: ModuleNotFoundError: No module named 'agents'
解决:
- 检查 PYTHONPATH 设置
- 确保在正确的虚拟环境中
- 检查 agents 模块的导入路径
```

#### 3. 数据库连接错误
```
错误: 数据库连接失败
解决:
- 检查数据库服务是否启动
- 检查数据库连接配置
- 检查数据库用户权限
```

#### 4. 智能体初始化失败
```
错误: BlowingAgent 初始化失败
解决:
- 检查 BlowingAgent 类的依赖
- 检查大模型配置
- 检查日志文件获取详细错误信息
```

### 调试命令

#### 查看服务器日志
```bash
# 如果使用 systemd
journalctl -u your-service-name -f

# 如果使用 Docker
docker logs -f container-name

# 如果直接运行
tail -f logs/app.log
```

#### 检查进程状态
```bash
# 查看 Python 进程
ps aux | grep python

# 查看端口占用
lsof -i :8000
```

#### 网络连接测试
```bash
# 测试 WebSocket 连接
curl --include \
     --no-buffer \
     --header "Connection: Upgrade" \
     --header "Upgrade: websocket" \
     --header "Sec-WebSocket-Key: SGVsbG8sIHdvcmxkIQ==" \
     --header "Sec-WebSocket-Version: 13" \
     http://localhost:8000/ws/agent/blowing
```

## 性能监控

### 监控指标

1. **连接数**: 当前活跃的 WebSocket 连接数
2. **响应时间**: 消息处理的平均响应时间
3. **错误率**: 错误消息的比例
4. **内存使用**: 智能体实例的内存占用
5. **数据库查询**: 数据库查询的频率和响应时间

### 监控命令

```bash
# 内存使用监控
ps -o pid,ppid,cmd,%mem,%cpu -p $(pgrep -f "uvicorn")

# 网络连接监控
ss -tuln | grep 8000

# 系统资源监控
htop
```

## 生产环境配置

### 1. 环境变量配置
```bash
export ENVIRONMENT=production
export LOG_LEVEL=INFO
export DATABASE_URL=your_production_db_url
export LLM_API_KEY=your_llm_api_key
```

### 2. 服务器配置
```bash
# 使用 Gunicorn + Uvicorn 运行
gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# 或者使用 systemd 服务
sudo systemctl enable your-service
sudo systemctl start your-service
```

### 3. 反向代理配置 (Nginx)
```nginx
location /ws/ {
    proxy_pass http://localhost:8000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 安全注意事项

1. **认证**: 在生产环境中添加 WebSocket 认证
2. **限流**: 实施连接数和消息频率限制
3. **日志**: 记录所有 WebSocket 连接和消息
4. **监控**: 监控异常连接和恶意请求
5. **HTTPS**: 在生产环境中使用 WSS (WebSocket Secure)

## 联系支持

如果遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 检查本文档的故障排除部分
3. 运行相关的测试脚本验证功能
4. 提供完整的错误信息和环境配置
