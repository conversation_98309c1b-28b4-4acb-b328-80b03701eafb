# 钢铁大模型智能体技术方案
本方案将详细阐述构成“炼钢智能体”系统的四个核心功能模块：“极数吹炼智能体”、“极目观火智能体”、“智能问数智能体”以及“经营决策智能问数智能体”。它们各自承担不同职责，协同工作，共同构成一个完整的、覆盖从生产操作到经营决策的认知化辅助系统。

## 一、极数吹炼智能体
“极数吹炼智能体”是直接面向一线操作人员的核心交互界面和决策执行中枢。它的定位并非一个取代人的“黑盒”控制器，而是一个智能“副驾驶”，通过实时感知、智能分析和主动导航，辅助操作员完成整个吹炼过程，实现人机共融的智能冶炼。“极数吹炼智能体”在系统架构中扮演着承上启下的关键角色。它接收来自底层“极目观火智能体”的实时炉况感知数据，并调用云端“浪潮钢铁大模型”的宏观决策能力，最终将其转化为具体、可执行的操作指令，呈现给操作员。

在项目初期和中期，该智能体将以开环辅助模式运行，即系统提供推荐指令，但最终的操作决策权和执行权仍掌握在人类操作员手中。这种模式是建立操作员信任、确保生产安全、平滑引入新技术的必要步骤。随着系统稳定性和可靠性的验证，未来可逐步探索半闭环乃至全闭环的控制模式。

“极数吹炼智能体”的工作流程遵循一个持续的“感知-规划-分解-交付”循环：

* **感知**：智能体以高频率从“极目观火智能体”和其他数据源（MES, L2）接收一个动态更新的“炉况状态向量”。该向量全面描述了当前熔池的物理化学状态，包括：熔池温度、碳含量、炉渣成分（估算值）、炉渣泡沫化指数、喷溅概率、脱碳速率等。

* **规划：** 智能体将这个实时的“炉况状态向量”与本炉次的终点目标（目标钢种、成分、温度）打包成一个结构化的提示，向云端的钢铁大模型发起查询。查询内容类似于：“当前炉况为X，终点目标为Y。请生成未来3分钟内的最优吹炼策略，以实现路径最短、消耗最低、稳定性最高的冶炼。”

* **分解：** 大模型返回一个高级别的、描述性的操作计划（例如，“当前阶段应以稳渣为先，适度降低吹炼强度，待炉渣熔化良好后再提升脱碳速率”）。“极数吹炼智能体”内置的逻辑模块（可以是小型专用模型或基于知识图谱的规则引擎）负责将这个高级计划“翻译”成一系列具体的、带时间戳的指令序列。

* **交付：** 分解后的指令通过主动语音提示或者HMI可视化导航清晰地传达给操作员，或者直接参与实际控制。

## 二、极目观火智能体
“极目观火智能体”是整个系统的“超级传感器”和前哨。它模仿并超越了经验最丰富的炉长“看火”的技艺，通过融合多种异构数据源，对炉内瞬息万变的物理化学过程进行深度、实时的解读。

该智能体的核心是多模态融合。要实现对炉况的整体性、深层次理解，仅靠单一数据源是远远不够的。火焰的形态、炉渣的声音、烟气的成分，这些信息互为补充，共同描绘了熔池内部的真实图景。因此，我们将摒弃为每个数据流单独建模的传统方法，通过微调多模态大模型，利用其核心的“交叉注意力”机制，学习不同模态数据之间复杂的、非线性的内在关联。

“极目观火智能体”输入包含来自炉口工业相机的高清视频流、烟气分析仪的烟气数据、工艺相关数据（如氧枪枪位、吹氧流量）、炉口麦克风的音频（在九羊没有音频数据）等多模态数据。所有模态的Token序列被输入到一个统一的Transformer编码器中。在其中，交叉注意力层允许例如视觉Token去“关注”声学Token和烟气Token，从而让模型自主学习到诸如“某种特定的火焰纹理，总是伴随着一种特殊的炉声嘶吼声，以及烟气CO/CO2比值的急剧下降”这样的深层跨模态模式。

通过这种深度的多模态融合，“极目观火智能体”能够生成一系列传统传感器无法直接测量、但对精细化控制至关重要的“软测量”指标和预测。这些输出将作为“极数吹炼智能体”进行决策规划的主要依据。该智能体的输出主要包含如下内容：

* **实时炉渣状态指数：** 输出一个或多个连续变化的数值，量化表征炉渣的关键状态。例如，“炉渣泡沫化指数”（Slag Foaming Index）和“炉渣活性指数”（Slag Activity Index）。这些指数是综合了火焰覆盖度、火焰稳定性、炉声频谱能量分布等多个维度信息后计算得出的，为操作员提供了判断“渣况”的客观依据 。

* **动态终点预测：** 基于融合了最新多模态信息的输入，模型将以秒级或几秒级的频率，持续滚动更新对终点碳含量和温度的预测值。相比于仅依赖静态模型或副枪测量的传统方法，这种动态预测能够更早、更准确地反映炉内变化趋势，为实现精准“拉碳”提供可能 。

*  **异常工况预警：** 这是该智能体最重要的能力之一。通过学习历史数据中异常事件（如喷溅、返干）发生前的多模态特征组合，模型能够提前数秒甚至数十秒，以概率形式发出预警。例如，当模型检测到火焰形态、颜色和炉声的组合模式与历史上的“喷溅”前兆高度相似时，会立刻向“极数吹炼智能体”发出高优先级警报，为操作员采取抑制措施（如提枪、降氧、加料）争取宝贵的时间窗口 。

## 三、智能问数智能体
“智能问数智能体”是面向生产管理人员、工艺工程师和技术专家的决策支持工具。它旨在回答“为什么”类型的问题，通过结合大语言模型的自然语言理解能力和知识图谱的结构化推理能力，实现从数据监控到知识发现的飞跃。

该智能体的核心是构建一个精心设计的炼钢知识图谱。与传统数据库不同，知识图谱不仅存储数据，更重要的是存储实体之间的丰富语义关系，特别是因果关系。它将炼钢过程中的碎片化知识和数据，编织成一张巨大的、可计算的语义网络。

“智能问数智能体”通过以下的“大模型+知识图谱”协同机制实现：

* **自然语言理解：** 管理者或工程师可以通过一个简单的对话框，用日常语言提出复杂的分析请求。例如：“对比分析上周甲班和乙班在生产HRB400E钢种时，终点温度命中率差异的根本原因是什么？”

* **意图识别与查询生成：** 大模型首先解析这个自然语言问题，识别出用户的核心意图（根因分析）、关键实体（甲班、乙班、HRB400E）、时间范围（上周）和KPI（终点温度命中率）。然后，LLM将这个分析任务自动翻译成一段结构化的图谱查询语言。

* **图谱遍历与因果推理：** 生成的查询在知识图谱上执行。查询引擎会从指定的炉次集合出发，沿着图谱中的影响和导致 等关系边进行深度遍历，寻找与“终点温度命中率”差异显著相关的上游因素。例如，图谱可能会发现，甲班的炉次普遍使用了来自某个特定供应商的废钢，该批废钢的残余元素（如Cu, Cr）含量较高，这在图谱中被标注为影响终点升温模型准确性的因素。

* **答案生成与可视化：** 图谱查询返回一系列结构化的潜在原因及其置信度。这些结构化的结果被送回给大模型。大模型负责将这些冰冷的数据“翻译”成一段通顺、有逻辑、易于理解的自然语言报告，并自动生成支持性的图表。例如，系统会回答：“甲班上周终点温度命中率较低（85% vs乙班的92%），主要原因可能在于其使用的2号废钢料仓中的废钢残余元素铜含量偏高，影响了终点热平衡计算的准确性。以下是两个班组所用废钢的铜含量分布对比图。”

## 四、经营决策智能问数智能体
“经营决策智能问数智能体”是专为企业管理者、经营分析师及营销决策者打造的高级决策支持工具，扮演着“智能经营分析师”的角色。它超越了生产现场的工艺优化，旨在打通“生产数据”与“经营效益”之间的壁垒。该智能体具备**“主动预警与引导式分析”和“被动式自由问答”**两种核心工作模式，将数据洞察无缝转化为商业价值。

该智能体的核心是构建一个覆盖“产-供-销-财”全链条的经营知识图谱。此图谱不仅包含生产过程数据，更关键的是融入了决定企业盈利能力的各类经营要素（如成本、价格、订单、库存、市场行情等），形成一个动态、可计算的商业语义网络。

其功能通过“大模型+经营知识图谱”的协同机制实现：

* **主动预警与引导式分析 (Proactive & Guided Analysis)**

    * **主动发现：** 智能体7x24小时不间断地监控关键经营指标（KPIs），通过与历史同期、目标值、行业基准进行对比，主动发现潜在的风险与机会点。例如，系统会自动标记出：“本月HRB400E螺纹钢的吨钢毛利环比下降15%，已偏离目标阈值。”

    * **主动推送：** 一旦发现异常，智能体会立即向相关管理者推送一条简洁的预警信息，并附上交互式选项：“检测到吨钢毛利异常下降，是否立即启动根因分析？”

    * **引导式追问：** 当管理者确认分析后，智能体不会一次性抛出所有数据，而是像一位真正的分析师一样，通过对话引导管理者进行层层钻取和逻辑推理。

        * **第一层（现象定位）：**“吨钢毛利由‘销售价格’和‘单位成本’决定。数据显示，销售价格基本稳定，问题主要出在‘单位成本’显著上升。我们继续分析成本构成，好吗？”

        * **第二层（范围聚焦）：**“单位成本主要由‘原料’、‘能源’和‘人工’三部分构成。数据显示‘原料成本’是本次上升的主要驱动因素。您想查看原料中具体是哪一项，还是想对比不同班组的原料消耗情况？”

        * **第三层（根因锁定）：** 根据管理者的选择，系统最终定位到：“根本原因已锁定：本月采购的2号废钢料仓中的废钢价格上涨了12%，对总成本影响最大。”

* **被动式自由问答 (Reactive Q&A)**

    * **自然语言理解与交互：** 支持管理者随时使用日常业务语言进行自由提问，无需学习复杂的查询语法。例如：“帮我查一下三季度签订的几个大客户订单，在当前原料库存和市场价格下，哪个订单的预期利润率最高？”

    * **查询意图分解与执行：** 大模型接收问题后，会将其分解为清晰的分析意图，自动生成图谱查询指令，并在经营知识图谱上进行跨域遍历和计算。

    * **整合式答案生成：** 图谱查询返回结构化结果后，大模型会将其“翻译”成一份逻辑清晰、重点突出的多维分析报告，并附上支持性的数据和可视化图表，直接呈现给管理者。

通过这两种模式的结合，“经营决策智能问数智能体”从一个被动的“数据查询工具”，升级为一个主动的、能够与管理者进行深度思考和逻辑推理的“智能业务伙伴”。