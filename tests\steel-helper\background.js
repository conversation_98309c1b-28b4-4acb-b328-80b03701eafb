let ws = null;
let reconnectCount = 0;
const maxReconnectCount = 5;
let pendingMessages = [];
let clientId = null; // 新增：存储客户端ID

// 修改WebSocket连接地址
function connectWebSocket() {
    if (ws) {
        ws.close();
    }
    // 更新为新的WebSocket地址
    ws = new WebSocket('ws://10.206.20.123:9000/ws/agent/decision');

    ws.onopen = () => {
        console.log('WebSocket 连接已建立');
        reconnectCount = 0;

        // 发送待发送消息
        while (pendingMessages.length > 0) {
            const message = pendingMessages.shift();
            sendQueryMessage(message);
        }
    };

    ws.onmessage = (event) => {
        try {
            const data = JSON.parse(event.data);
            console.log('收到WebSocket消息:', data);

            // 处理连接成功消息
            if (data.type === 'connected') {
                clientId = data.client_id;
                // 发送欢迎消息到前端
                sendToContentScript({
                    type: 'receiveMessage',
                    content: data.message,
                    is_finished: true
                });
                return;
            }

            // 忽略ping消息
            if (data.type === 'ping') {
                return;
            }

            // 处理流式响应
            if (data.type === 'stream_start') {
                sendToContentScript({
                    type: 'receiveMessage',
                    content: data.message,
                    is_finished: false
                });
            } else if (data.type === 'stream') {
                sendToContentScript({
                    type: 'receiveMessage',
                    content: data.content,
                    is_finished: data.is_finished
                });
            } else if (data.type === 'stream_end') {
                // 使用total_content确保内容完整
                sendToContentScript({
                    type: 'receiveMessage',
                    content: data.total_content || data.content,
                    is_finished: true
                });
            }

        } catch (e) {
            console.error('解析消息失败:', e);
        }
    };

    ws.onerror = (error) => {
        console.error('WebSocket 错误:', error);
    };

    ws.onclose = () => {
        if (reconnectCount < maxReconnectCount) {
            console.log('WebSocket 连接已关闭，正在重连...');
            setTimeout(connectWebSocket, 3000);
            reconnectCount++;
        } else {
            console.log('重连次数达到上限，停止重连');
        }
    };
}

// 新增：发送标准化查询消息
function sendQueryMessage(content) {
    if (ws && ws.readyState === WebSocket.OPEN) {
        const message = JSON.stringify({
            type: "query",
            content: content,
            context: {} // 可根据需要添加上下文信息
        });
        console.log('发送消息到WebSocket:', message);
        ws.send(message);
    }
}

// 新增：统一发送消息到content.js的方法
function sendToContentScript(message) {
    chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
        if (tabs.length > 0) {
            chrome.tabs.sendMessage(tabs[0].id, message, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('发送消息到content.js出错:', chrome.runtime.lastError.message);
                }
            });
        }
    });
}

connectWebSocket();

// 接收来自content.js的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'sendMessage') {
        if (ws && ws.readyState === WebSocket.OPEN) {
            sendQueryMessage(request.content);
        } else {
            console.log('WebSocket未就绪，加入待发送队列:', request.content);
            pendingMessages.push(request.content);
        }
    }
});