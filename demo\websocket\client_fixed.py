"""
WebSocket客户端演示脚本（修复版）
使用线程处理输入，避免阻塞消息接收
"""

import asyncio
import json
import websockets
from datetime import datetime
from typing import Optional, Any
import threading
import queue
import sys


class SteelAgentClient:
    """钢铁大模型WebSocket客户端"""
    
    def __init__(self, url: str = "ws://localhost:8001/ws/agent"):
        self.url = url
        self.websocket: Optional[Any] = None  # websockets connection object
        self.client_id: Optional[str] = None
        self.running = False
        self.input_queue = queue.Queue()
        
    async def connect(self):
        """连接到服务器"""
        print(f"正在连接到 {self.url}...")
        self.websocket = await websockets.connect(self.url)
        self.running = True
        print("连接成功！")
        
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            print("\n已断开连接")
            
    async def send_query(self, content: str):
        """发送查询"""
        if not self.websocket:
            raise RuntimeError("WebSocket未连接")
        message = {
            "type": "query",
            "content": content,
            "context": {}
        }
        await self.websocket.send(json.dumps(message))
        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 已发送查询")
        
    async def send_action(self, action: str, data: dict):
        """发送动作"""
        if not self.websocket:
            raise RuntimeError("WebSocket未连接")
        message = {
            "type": "action",
            "action": action,
            "data": data
        }
        await self.websocket.send(json.dumps(message))
        
    async def receive_messages(self):
        """接收消息"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await self.handle_message(data)
        except websockets.exceptions.ConnectionClosed:
            print("\n连接已关闭")
            self.running = False
        except Exception as e:
            print(f"\n接收消息错误: {e}")
            self.running = False
            
    async def handle_message(self, data: dict):
        """处理接收到的消息"""
        msg_type = data.get("type")
        
        if msg_type == "connected":
            self.client_id = data.get("client_id")
            print(f"\n{data.get('message')}")
            print(f"客户端ID: {self.client_id}")
            print("\n" + "="*50)
            print("钢铁大模型智能助手 - Demo客户端")
            print("="*50)
            print("\n输入 'quit' 退出，'help' 查看帮助")
            print("-"*50)
            
        elif msg_type == "status":
            print(f"\n[状态] {data.get('content')}")
            
        elif msg_type == "intent":
            print(f"[意图识别] {data.get('description')} (置信度: {data.get('confidence'):.2f})")
            
        elif msg_type == "content":
            # 流式输出内容
            content = data.get('content', '')
            print(content, end='', flush=True)
            
        elif msg_type == "alert":
            print(f"\n{'='*50}")
            print(f"[预警] {data.get('title')}")
            print(f"严重程度: {data.get('severity')}")
            print(f"内容: {data.get('content')}")
            print(f"时间: {data.get('timestamp')}")
            
            # 显示可用操作
            if data.get('actions'):
                print("\n可用操作:")
                for i, action in enumerate(data['actions']):
                    print(f"  {i+1}. {action['label']}")
                    
            print(f"{'='*50}\n")
            
        elif msg_type == "interactive":
            print("\n[交互选项]")
            for option in data.get('options', []):
                print(f"- {option.get('text')}")
                
        elif msg_type == "done":
            print(f"\n[完成] 会话ID: {data.get('session_id')}")
            print("-" * 50)
            print("\n您: ", end='', flush=True)
            
        elif msg_type == "error":
            print(f"\n[错误] {data.get('content')}")
            print("\n您: ", end='', flush=True)
            
        elif msg_type == "ping":
            # 自动回复pong
            if self.websocket:
                await self.websocket.send(json.dumps({"type": "pong"}))
    
    def input_thread(self):
        """在单独的线程中处理用户输入"""
        while self.running:
            try:
                user_input = input("\n您: ")
                self.input_queue.put(user_input)
            except EOFError:
                break
            except KeyboardInterrupt:
                self.input_queue.put('quit')
                break
                
    async def process_input(self):
        """处理用户输入队列"""
        while self.running:
            try:
                # 非阻塞地检查输入队列
                try:
                    user_input = self.input_queue.get_nowait()
                except queue.Empty:
                    await asyncio.sleep(0.1)  # 短暂休眠，避免CPU占用过高
                    continue
                    
                if user_input.lower() == 'quit':
                    self.running = False
                    break
                    
                elif user_input.lower() == 'help':
                    self.show_help()
                    print("\n您: ", end='', flush=True)
                    
                elif user_input.lower() == 'alert':
                    await self.trigger_mock_alert()
                    print("\n您: ", end='', flush=True)
                    
                elif user_input.strip():  # 只有非空输入才发送
                    await self.send_query(user_input)
                    
            except Exception as e:
                print(f"\n处理输入错误: {e}")
                
    def show_help(self):
        """显示帮助信息"""
        print("\n" + "="*50)
        print("帮助信息:")
        print("="*50)
        print("1. 经营决策相关查询示例:")
        print("   - 分析本月吨钢毛利下降的原因")
        print("   - 查看最近一周的成本构成变化")
        print("   - 哪些产品的利润率最高？")
        print("   - 分析HRB400E的市场价格趋势")
        print("\n2. 生产控制相关查询示例:")
        print("   - 当前炉况如何？")
        print("   - 氧枪位置应该调整到多少？")
        print("   - 如何预防喷溅？")
        print("\n3. 质量分析相关查询示例:")
        print("   - 最近的质量合格率如何？")
        print("   - 分析质量不合格的原因")
        print("\n4. 特殊命令:")
        print("   - help: 显示此帮助信息")
        print("   - alert: 模拟触发一个KPI预警")
        print("   - quit: 退出程序")
        print("="*50)
        
    async def trigger_mock_alert(self):
        """触发模拟预警"""
        print("\n正在模拟KPI异常...")
        
    async def run(self):
        """运行客户端"""
        try:
            await self.connect()
            
            # 启动输入线程
            input_thread = threading.Thread(target=self.input_thread, daemon=True)
            input_thread.start()
            
            # 创建异步任务
            receive_task = asyncio.create_task(self.receive_messages())
            process_input_task = asyncio.create_task(self.process_input())
            
            # 等待任一任务完成
            done, pending = await asyncio.wait(
                [receive_task, process_input_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # 取消未完成的任务
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                
        finally:
            self.running = False
            await self.disconnect()


async def main():
    """主函数"""
    client = SteelAgentClient()
    await client.run()


if __name__ == "__main__":
    print("钢铁大模型智能助手客户端启动中...")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序已退出")