@echo off
echo 钢铁大模型智能体Demo启动脚本
echo ==============================

REM 获取脚本所在目录
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%\..\..

REM 切换到项目根目录
cd /d "%PROJECT_ROOT%"

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.10+
    exit /b 1
)

REM 安装依赖
echo 正在检查依赖...
pip install poetry
poetry install

REM 启动后端服务
echo 启动后端服务...
start /B cmd /c "cd src && python -m backend.main"
echo 后端服务已启动

REM 等待服务启动
timeout /t 5 /nobreak >nul

REM 启动客户端
echo 启动Demo客户端...
cd /d "%SCRIPT_DIR%"
python client.py

REM 结束
echo Demo已结束
echo 请手动关闭后端服务窗口
pause