<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <canvas id="canvas"></canvas>
    <script>
        function generateIcon(size) {
            const canvas = document.getElementById('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Background
            ctx.fillStyle = '#5c6bc0';
            ctx.fillRect(0, 0, size, size);
            
            // Draw BI text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.4}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('BI', size / 2, size / 2);
            
            // Convert to PNG
            return canvas.toDataURL('image/png');
        }
        
        // Generate icons
        const sizes = [16, 48, 128];
        sizes.forEach(size => {
            const dataUrl = generateIcon(size);
            console.log(`icon-${size}.png:`, dataUrl);
            // You can save these manually or use a download function
        });
    </script>
</body>
</html>