# ChatBI WebSocket API 文档

## 概述

ChatBI 使用 WebSocket 进行实时双向通信。客户端连接到 `ws://localhost:8080/ws` 进行通信。

## 连接流程

1. 客户端建立 WebSocket 连接到 `ws://localhost:8080/ws`
2. 服务器发送初始推荐问题
3. 客户端发送用户消息
4. 服务器以流式方式返回响应
5. 响应结束后，服务器发送新的推荐问题

## 消息格式

所有消息使用 JSON 格式，包含 `type` 字段标识消息类型。

### 客户端到服务器

#### 发送消息
```json
{
  "type": "message",
  "content": "用户输入的文本内容"
}
```

### 服务器到客户端

#### 1. 流式响应开始
```json
{
  "type": "stream_start"
}
```

#### 2. 流式响应内容块
```json
{
  "type": "stream_chunk",
  "content": "响应内容的一部分"
}
```

#### 3. 流式响应结束
```json
{
  "type": "stream_end"
}
```

#### 4. 推荐问题
```json
{
  "type": "recommendations",
  "questions": [
    "推荐问题1",
    "推荐问题2",
    "推荐问题3",
    "推荐问题4"
  ]
}
```

#### 5. 错误消息
```json
{
  "type": "error",
  "message": "错误描述"
}
```

## 图表数据格式

在响应内容中，图表数据使用特殊的 Markdown 代码块格式：

```markdown
```chart
{
  "type": "bar|line|pie",
  "title": "图表标题",
  "data": {
    "labels": ["标签1", "标签2", ...],
    "values": [值1, 值2, ...]
  }
}
```
```

### 图表类型

- `bar`: 柱状图
- `line`: 折线图
- `pie`: 饼图

## 响应内容格式

响应支持 Markdown 格式，包括：
- **粗体文本**: `**文本**`
- *斜体文本*: `*文本*`
- 代码块: ` ```language\n代码\n``` `
- 图表: 使用上述图表格式

## 错误处理

- WebSocket 连接断开后，客户端应自动重连
- 消息解析失败时，服务器返回 error 类型消息
- 客户端应处理各种网络异常情况

## 示例交互流程

1. **连接建立**
   ```
   客户端 -> 连接到 ws://localhost:8080/ws
   服务器 <- {"type": "recommendations", "questions": [...]}
   ```

2. **发送消息**
   ```
   客户端 -> {"type": "message", "content": "显示上月销售趋势"}
   服务器 <- {"type": "stream_start"}
   服务器 <- {"type": "stream_chunk", "content": "根据数据分析，"}
   服务器 <- {"type": "stream_chunk", "content": "这里是关键洞察："}
   ...
   服务器 <- {"type": "stream_end"}
   服务器 <- {"type": "recommendations", "questions": [...]}
   ```

## HTTP API

### 健康检查
```
GET /health
```

响应：
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```