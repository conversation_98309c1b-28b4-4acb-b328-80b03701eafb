"""
测试KPI监控和预警推送功能
"""

import asyncio
import aiohttp
import json
from datetime import datetime


async def trigger_kpi_alert():
    """触发KPI预警（通过API广播）"""
    url = "http://localhost:8000/api/broadcast"
    
    # 构造预警消息
    alert_message = {
        "type": "alert",
        "severity": "high",
        "title": "KPI异常预警: 吨钢毛利",
        "content": (
            "检测到吨钢毛利指标异常：\n"
            "- 当前值: 280.50\n"
            "- 目标值: 300.00\n"
            "- 偏差率: -6.5%\n\n"
            "是否需要立即分析原因？"
        ),
        "timestamp": datetime.now().isoformat(),
        "actions": [
            {
                "label": "立即分析",
                "action": "analyze_now",
                "data": {
                    "kpi": "吨钢毛利",
                    "current_value": 280.50,
                    "target_value": 300.00,
                    "deviation": -0.065
                }
            },
            {
                "label": "稍后处理",
                "action": "dismiss"
            }
        ]
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(url, json=alert_message) as response:
            result = await response.json()
            print(f"预警已发送: {result}")


async def check_connections():
    """检查当前活跃连接"""
    url = "http://localhost:8000/api/connections"
    
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            result = await response.json()
            print(f"\n当前活跃连接数: {result['count']}")
            print("连接详情:")
            for client_id, info in result['active_connections'].items():
                print(f"  - {client_id}: 连接时间 {info['connected_at']}")


async def simulate_multiple_alerts():
    """模拟多个KPI预警"""
    alerts = [
        {
            "kpi": "成本每吨",
            "current": 2650,
            "target": 2500,
            "severity": "medium"
        },
        {
            "kpi": "成材率",
            "current": 0.92,
            "target": 0.95,
            "severity": "high"
        },
        {
            "kpi": "库存周转率",
            "current": 10.5,
            "target": 12.0,
            "severity": "low"
        }
    ]
    
    url = "http://localhost:8000/api/broadcast"
    
    for alert in alerts:
        message = {
            "type": "alert",
            "severity": alert["severity"],
            "title": f"KPI预警: {alert['kpi']}",
            "content": f"{alert['kpi']}偏离目标值",
            "timestamp": datetime.now().isoformat()
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=message) as response:
                print(f"发送预警: {alert['kpi']}")
                await asyncio.sleep(2)  # 间隔2秒


async def main():
    """主函数"""
    print("KPI监控测试工具")
    print("="*50)
    print("1. 检查活跃连接")
    print("2. 触发单个KPI预警")
    print("3. 模拟多个KPI预警")
    print("4. 退出")
    
    while True:
        choice = input("\n请选择操作 (1-4): ")
        
        if choice == "1":
            await check_connections()
            
        elif choice == "2":
            await trigger_kpi_alert()
            print("预警已触发，请查看客户端")
            
        elif choice == "3":
            await simulate_multiple_alerts()
            print("多个预警已触发")
            
        elif choice == "4":
            break
            
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序已退出")