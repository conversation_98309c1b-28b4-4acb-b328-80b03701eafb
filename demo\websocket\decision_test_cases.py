"""
经营决策智能体测试用例集合
包含各种场景的测试用例
"""

# 测试用例分类
TEST_CASES = {
    "基础查询": {
        "description": "测试基本的数据查询功能",
        "cases": [
            {
                "query": "查询昨天的吨钢毛利",
                "expected": "返回具体的吨钢毛利数值和分析"
            },
            {
                "query": "本月的钢坯成本是多少",
                "expected": "返回钢坯成本数据"
            },
            {
                "query": "查看最近一周的成材率",
                "expected": "返回成材率趋势数据"
            },
            {
                "query": "炼钢工序加工成本的当前值",
                "expected": "返回炼钢工序成本"
            }
        ]
    },
    
    "异常分析": {
        "description": "测试异常指标的根因分析能力",
        "cases": [
            {
                "query": "分析吨钢毛利低于目标值的原因",
                "expected": "识别异常原因并给出分析"
            },
            {
                "query": "钢坯成本异常上升的根本原因是什么",
                "expected": "进行根因分析，找出成本上升原因"
            },
            {
                "query": "为什么铁水温度合格率偏低",
                "expected": "分析温度控制问题"
            },
            {
                "query": "成材率下降的主要影响因素",
                "expected": "识别影响成材率的关键因素"
            }
        ]
    },
    
    "趋势分析": {
        "description": "测试时间序列和趋势分析功能",
        "cases": [
            {
                "query": "分析本月吨钢毛利的变化趋势",
                "expected": "展示趋势图表和分析"
            },
            {
                "query": "对比本月和上月的生产成本",
                "expected": "进行月度对比分析"
            },
            {
                "query": "最近三个月原料成本的走势如何",
                "expected": "分析原料成本趋势"
            },
            {
                "query": "预测下周的钢坯成本",
                "expected": "基于历史数据进行预测"
            }
        ]
    },
    
    "决策建议": {
        "description": "测试决策支持和建议生成功能",
        "cases": [
            {
                "query": "如何提高吨钢毛利",
                "expected": "提供具体的改进建议"
            },
            {
                "query": "降低钢坯成本的措施有哪些",
                "expected": "给出成本控制方案"
            },
            {
                "query": "提升成材率的最佳实践",
                "expected": "提供工艺优化建议"
            },
            {
                "query": "优化原料采购策略的建议",
                "expected": "给出采购优化方案"
            }
        ]
    },
    
    "复杂查询": {
        "description": "测试复杂的综合分析能力",
        "cases": [
            {
                "query": "分析1号高炉和2号高炉的成本差异，并给出优化建议",
                "expected": "对比分析+建议"
            },
            {
                "query": "评估最近订单的盈利能力，哪些客户利润率最高",
                "expected": "盈利能力分析"
            },
            {
                "query": "从成本、效率、质量三个维度分析本月的生产情况",
                "expected": "多维度综合分析"
            },
            {
                "query": "如果原料价格上涨10%，对吨钢毛利的影响有多大",
                "expected": "情景分析和影响评估"
            }
        ]
    },
    
    "直接回答": {
        "description": "测试不需要查询数据库的问题",
        "cases": [
            {
                "query": "什么是吨钢毛利",
                "expected": "概念解释"
            },
            {
                "query": "如何计算成材率",
                "expected": "公式说明"
            },
            {
                "query": "炼钢工序包含哪些环节",
                "expected": "流程介绍"
            },
            {
                "query": "KPI异常的判断标准是什么",
                "expected": "标准说明"
            }
        ]
    }
}

# KPI异常测试数据
KPI_ALERTS = [
    {
        "name": "吨钢毛利严重偏低",
        "data": {
            "kpi": "吨钢毛利",
            "current_value": 245.0,
            "target_value": 300.0,
            "threshold": 285.0,
            "unit": "元/吨",
            "time": "2024-01-15",
            "trend": "下降",
            "duration": "连续3天"
        }
    },
    {
        "name": "钢坯成本异常上升",
        "data": {
            "kpi": "钢坯成本",
            "current_value": 2750.0,
            "target_value": 2500.0,
            "threshold": 2625.0,
            "unit": "元/吨",
            "time": "2024-01-15",
            "trend": "上升",
            "duration": "连续5天"
        }
    },
    {
        "name": "成材率低于标准",
        "data": {
            "kpi": "成材率",
            "current_value": 93.5,
            "target_value": 95.0,
            "threshold": 94.5,
            "unit": "%",
            "time": "2024-01-15",
            "trend": "波动",
            "duration": "本周平均"
        }
    },
    {
        "name": "铁水温度合格率偏低",
        "data": {
            "kpi": "铁水温度合格率",
            "current_value": 85.0,
            "target_value": 95.0,
            "threshold": 90.0,
            "unit": "%",
            "time": "2024-01-15",
            "trend": "下降",
            "duration": "连续2天"
        }
    }
]

# 批量测试场景
BATCH_TEST_SCENARIOS = [
    {
        "name": "早班交接分析",
        "description": "模拟早班交接时的常规查询",
        "queries": [
            "昨天夜班的生产情况如何",
            "有哪些KPI指标异常",
            "需要重点关注哪些问题",
            "今天的生产计划建议"
        ]
    },
    {
        "name": "周报数据准备",
        "description": "模拟准备周报时的数据查询",
        "queries": [
            "本周的吨钢毛利平均值",
            "本周成本控制情况分析",
            "与上周相比有哪些改善",
            "下周需要改进的重点"
        ]
    },
    {
        "name": "月度经营分析",
        "description": "模拟月度经营分析会议准备",
        "queries": [
            "本月经营指标完成情况",
            "主要成本驱动因素分析",
            "与去年同期对比情况",
            "下月经营改善建议"
        ]
    }
]

# 性能测试用例
PERFORMANCE_TEST = {
    "并发查询": [
        "查询吨钢毛利",
        "分析成本构成",
        "计算成材率",
        "统计产量数据",
        "对比班组效率"
    ],
    "复杂计算": [
        "分析最近30天所有指标的异常情况并给出综合报告",
        "对比分析所有高炉的效率和成本，找出最优运行参数",
        "预测未来一个月的成本走势并制定应对策略"
    ]
}

def get_test_case_by_category(category: str) -> list:
    """根据类别获取测试用例"""
    if category in TEST_CASES:
        return TEST_CASES[category]["cases"]
    return []

def get_all_categories() -> list:
    """获取所有测试类别"""
    return list(TEST_CASES.keys())

def get_random_test_case():
    """随机获取一个测试用例"""
    import random
    category = random.choice(list(TEST_CASES.keys()))
    case = random.choice(TEST_CASES[category]["cases"])
    return {
        "category": category,
        "query": case["query"],
        "expected": case["expected"]
    }

if __name__ == "__main__":
    # 打印所有测试用例
    print("经营决策智能体测试用例集合")
    print("=" * 60)
    
    for category, info in TEST_CASES.items():
        print(f"\n【{category}】- {info['description']}")
        print("-" * 40)
        for i, case in enumerate(info['cases'], 1):
            print(f"  {i}. {case['query']}")
            print(f"     预期: {case['expected']}")
    
    print(f"\n\nKPI异常测试用例: {len(KPI_ALERTS)}个")
    print(f"批量测试场景: {len(BATCH_TEST_SCENARIOS)}个")
    print(f"性能测试类别: {len(PERFORMANCE_TEST)}个")