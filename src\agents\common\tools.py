from typing import Optional, Type, Dict, Any, List
from langchain.tools import BaseTool, StructuredTool
from langchain.callbacks.manager import (
    AsyncCallbackManagerForToolRun,
    CallbackManagerForToolRun,
)
from pydantic import BaseModel, Field
import aiohttp
import asyncio
from datetime import datetime, timedelta
import json


class FurnaceStateInput(BaseModel):
    """炉况查询输入"""
    heat_no: str = Field(..., description="炉次号")
    params: Optional[List[str]] = Field(None, description="要查询的参数列表")


class FurnaceStateTool(BaseTool):
    """查询炉况状态工具"""
    name: str = "query_furnace_state"
    description: str = "查询指定炉次的实时炉况状态，包括温度、碳含量、氧气流量等参数"
    args_schema: Type[BaseModel] = FurnaceStateInput
    
    def _run(
        self,
        heat_no: str,
        params: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """同步运行"""
        # 模拟查询炉况
        mock_data = {
            "heat_no": heat_no,
            "timestamp": datetime.utcnow().isoformat(),
            "temperature": 1650.5,
            "carbon_content": 0.15,
            "oxygen_flow": 18000,
            "lance_position": 1800,
            "foam_index": 2.1,
            "splash_probability": 0.12
        }
        
        if params:
            filtered_data = {k: v for k, v in mock_data.items() if k in params}
            return json.dumps(filtered_data, ensure_ascii=False)
        
        return json.dumps(mock_data, ensure_ascii=False)
        
    async def _arun(
        self,
        heat_no: str,
        params: Optional[List[str]] = None,
        run_manager: Optional[AsyncCallbackManagerForToolRun] = None
    ) -> str:
        """异步运行"""
        await asyncio.sleep(0.1)  # 模拟网络延迟
        # 模拟查询炉况
        mock_data = {
            "heat_no": heat_no,
            "timestamp": datetime.utcnow().isoformat(),
            "temperature": 1650.5,
            "carbon_content": 0.15,
            "oxygen_flow": 18000,
            "lance_position": 1800,
            "foam_index": 2.1,
            "splash_probability": 0.12
        }
        
        if params:
            filtered_data = {k: v for k, v in mock_data.items() if k in params}
            return json.dumps(filtered_data, ensure_ascii=False)
        
        return json.dumps(mock_data, ensure_ascii=False)


class MaterialCalculationInput(BaseModel):
    """物料计算输入"""
    hot_metal_weight: float = Field(..., description="铁水重量(吨)")
    target_carbon: float = Field(..., description="目标碳含量(%)")
    current_carbon: float = Field(..., description="当前碳含量(%)")
    target_temp: float = Field(..., description="目标温度(℃)")
    current_temp: float = Field(..., description="当前温度(℃)")


class MaterialCalculationTool(BaseTool):
    """辅料计算工具"""
    name: str = "calculate_materials"
    description: str = "根据当前炉况和目标值计算所需的辅料添加量"
    args_schema: Type[BaseModel] = MaterialCalculationInput
    
    def _run(
        self,
        hot_metal_weight: float,
        target_carbon: float,
        current_carbon: float,
        target_temp: float,
        current_temp: float,
        run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """计算辅料添加量"""
        # 简化的计算逻辑
        carbon_diff = current_carbon - target_carbon
        temp_diff = target_temp - current_temp
        
        lime_amount = hot_metal_weight * carbon_diff * 15  # 简化公式
        coolant_amount = 0 if temp_diff > 0 else abs(temp_diff) * 2
        
        result = {
            "lime": round(max(0, lime_amount), 2),
            "dolomite": round(lime_amount * 0.3, 2),
            "coolant": round(coolant_amount, 2),
            "calculation_basis": {
                "carbon_reduction": round(carbon_diff, 3),
                "temperature_adjustment": round(temp_diff, 1)
            }
        }
        
        return json.dumps(result, ensure_ascii=False)


class HistoricalDataInput(BaseModel):
    """历史数据查询输入"""
    steel_grade: str = Field(..., description="钢种")
    days: int = Field(default=7, description="查询最近N天的数据")
    limit: int = Field(default=10, description="返回结果数量限制")


class HistoricalDataTool(BaseTool):
    """历史数据查询工具"""
    name: str = "query_historical_data"
    description: str = "查询相似钢种的历史生产数据，用于参考和分析"
    args_schema: Type[BaseModel] = HistoricalDataInput
    
    def _run(
        self,
        steel_grade: str,
        days: int = 7,
        limit: int = 10,
        run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """查询历史数据"""
        # 模拟历史数据
        historical_data = []
        base_date = datetime.utcnow() - timedelta(days=days)
        
        for i in range(min(limit, 5)):
            heat_date = base_date + timedelta(days=i)
            historical_data.append({
                "heat_no": f"{heat_date.strftime('%Y%m%d')}-{100+i:03d}",
                "steel_grade": steel_grade,
                "date": heat_date.isoformat(),
                "quality_score": 90 + i,
                "endpoint_carbon": 0.045 + i * 0.001,
                "endpoint_temp": 1650 + i * 5,
                "total_oxygen": 4500 + i * 100,
                "duration_minutes": 16 + i * 0.5
            })
            
        return json.dumps({
            "count": len(historical_data),
            "data": historical_data
        }, ensure_ascii=False)


class ProcessOptimizationInput(BaseModel):
    """工艺优化输入"""
    current_state: Dict[str, float] = Field(..., description="当前炉况状态")
    target_state: Dict[str, float] = Field(..., description="目标状态")
    constraints: Optional[Dict[str, Any]] = Field(None, description="约束条件")


class ProcessOptimizationTool(BaseTool):
    """工艺优化建议工具"""
    name: str = "optimize_process"
    description: str = "根据当前状态和目标，提供工艺优化建议"
    args_schema: Type[BaseModel] = ProcessOptimizationInput
    
    def _run(
        self,
        current_state: Dict[str, float],
        target_state: Dict[str, float],
        constraints: Optional[Dict[str, Any]] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """生成优化建议"""
        suggestions = []
        
        # 温度控制建议
        if "temperature" in current_state and "temperature" in target_state:
            temp_diff = target_state["temperature"] - current_state["temperature"]
            if abs(temp_diff) > 10:
                if temp_diff > 0:
                    suggestions.append({
                        "type": "temperature_control",
                        "action": "increase_oxygen_flow",
                        "reason": f"需要升温{temp_diff:.1f}℃",
                        "params": {"oxygen_flow_increase": 2000}
                    })
                else:
                    suggestions.append({
                        "type": "temperature_control", 
                        "action": "add_coolant",
                        "reason": f"需要降温{abs(temp_diff):.1f}℃",
                        "params": {"coolant_amount": abs(temp_diff) * 2}
                    })
                    
        # 碳含量控制建议
        if "carbon" in current_state and "carbon" in target_state:
            carbon_diff = current_state["carbon"] - target_state["carbon"]
            if carbon_diff > 0.01:
                suggestions.append({
                    "type": "carbon_control",
                    "action": "adjust_lance_pattern",
                    "reason": f"需要降碳{carbon_diff:.3f}%",
                    "params": {
                        "lance_position": 1600,
                        "oxygen_pressure": 1.2
                    }
                })
                
        return json.dumps({
            "suggestions": suggestions,
            "estimated_time": len(suggestions) * 2,
            "confidence": 0.85
        }, ensure_ascii=False)


class KnowledgeQueryInput(BaseModel):
    """知识查询输入"""
    query: str = Field(..., description="查询问题")
    category: Optional[str] = Field(None, description="知识类别")


class KnowledgeQueryTool(BaseTool):
    """知识库查询工具"""
    name: str = "query_knowledge"
    description: str = "查询炼钢知识库，获取专业知识和最佳实践"
    args_schema: Type[BaseModel] = KnowledgeQueryInput
    
    def _run(
        self,
        query: str,
        category: Optional[str] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """查询知识库"""
        # 模拟知识库查询
        knowledge_base = {
            "splash_prevention": {
                "title": "喷溅预防措施",
                "content": "当检测到喷溅风险时，应立即：1)提高氧枪至2000mm以上；2)降低氧气流量20%；3)适量加入石灰调整炉渣流动性",
                "confidence": 0.95
            },
            "endpoint_control": {
                "title": "终点控制技术",
                "content": "终点控制的关键在于：1)准确预测脱碳速率；2)合理控制供氧强度；3)适时进行温度调节",
                "confidence": 0.90
            },
            "slag_control": {
                "title": "炉渣控制",
                "content": "良好的炉渣应具备：1)适当的碱度(2.8-3.5)；2)良好的流动性；3)合适的氧化性",
                "confidence": 0.88
            }
        }
        
        # 简单的关键词匹配
        relevant_knowledge = []
        for key, value in knowledge_base.items():
            if any(keyword in query.lower() for keyword in key.split('_')):
                relevant_knowledge.append(value)
                
        if not relevant_knowledge and category:
            # 如果没有匹配，尝试用类别查找
            if category in knowledge_base:
                relevant_knowledge.append(knowledge_base[category])
                
        return json.dumps({
            "query": query,
            "results": relevant_knowledge,
            "count": len(relevant_knowledge)
        }, ensure_ascii=False)


def create_steel_tools() -> List[BaseTool]:
    """创建钢铁领域专用工具集"""
    return [
        FurnaceStateTool(),
        MaterialCalculationTool(),
        HistoricalDataTool(),
        ProcessOptimizationTool(),
        KnowledgeQueryTool()
    ]


class KPIAnalysisInput(BaseModel):
    """KPI分析输入"""
    kpi_name: str = Field(..., description="KPI名称")
    time_range: str = Field(..., description="时间范围(如'last_week', 'last_month')")
    analysis_type: str = Field(default="trend", description="分析类型(trend/comparison/decomposition)")


class KPIAnalysisTool(BaseTool):
    """KPI分析工具"""
    name: str = "analyze_kpi"
    description: str = "分析关键经营指标(KPI)的趋势、对比和构成"
    args_schema: Type[BaseModel] = KPIAnalysisInput
    
    def _run(
        self,
        kpi_name: str,
        time_range: str,
        analysis_type: str = "trend",
        run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """分析KPI"""
        # 模拟KPI分析
        base_value = 300  # 基准值
        
        if analysis_type == "trend":
            # 趋势分析
            trend_data: List[Dict[str, Any]] = []
            for i in range(7):
                date = (datetime.utcnow() - timedelta(days=6-i)).strftime("%Y-%m-%d")
                value = base_value + (i-3) * 10 + (i % 2) * 5
                trend_data.append({"date": date, "value": value})
            
            result = {
                "kpi": kpi_name,
                "type": "trend",
                "data": trend_data,
                "trend": "increasing" if trend_data[-1]["value"] > trend_data[0]["value"] else "decreasing",
                "change_rate": round((trend_data[-1]["value"] - trend_data[0]["value"]) / trend_data[0]["value"] * 100, 2)
            }
            
        elif analysis_type == "comparison":
            # 对比分析
            result = {
                "kpi": kpi_name,
                "type": "comparison",
                "current_period": {"value": base_value, "period": "本周"},
                "previous_period": {"value": base_value * 0.95, "period": "上周"},
                "yoy": {"value": base_value * 0.92, "period": "去年同期"},
                "variance": {
                    "vs_previous": round((base_value - base_value * 0.95) / (base_value * 0.95) * 100, 2),
                    "vs_yoy": round((base_value - base_value * 0.92) / (base_value * 0.92) * 100, 2)
                }
            }
            
        else:  # decomposition
            # 构成分析
            result = {
                "kpi": kpi_name,
                "type": "decomposition",
                "components": [
                    {"name": "原料成本", "value": base_value * 0.6, "percentage": 60},
                    {"name": "能源成本", "value": base_value * 0.25, "percentage": 25},
                    {"name": "人工成本", "value": base_value * 0.15, "percentage": 15}
                ],
                "total": base_value
            }
            
        return json.dumps(result, ensure_ascii=False)


class CostAnalysisInput(BaseModel):
    """成本分析输入"""
    cost_type: str = Field(..., description="成本类型(material/energy/labor/total)")
    product: Optional[str] = Field(None, description="产品类型")
    period: str = Field(..., description="分析周期")


class CostAnalysisTool(BaseTool):
    """成本分析工具"""
    name: str = "analyze_cost"
    description: str = "分析各类成本的构成、变化和驱动因素"
    args_schema: Type[BaseModel] = CostAnalysisInput
    
    def _run(
        self,
        cost_type: str,
        period: str,
        product: Optional[str] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """成本分析"""
        # 模拟成本数据
        base_costs = {
            "material": 1500,
            "energy": 600,
            "labor": 400,
            "total": 2500
        }
        
        cost_value = base_costs.get(cost_type, 2500)
        
        analysis = {
            "cost_type": cost_type,
            "period": period,
            "product": product or "全部产品",
            "current_value": cost_value,
            "unit": "元/吨",
            "breakdown": {
                "主要驱动因素": [
                    {"factor": "原料价格上涨", "impact": "+8%", "amount": cost_value * 0.08},
                    {"factor": "能源效率提升", "impact": "-3%", "amount": -cost_value * 0.03},
                    {"factor": "规模效应", "impact": "-2%", "amount": -cost_value * 0.02}
                ],
                "净影响": "+3%"
            },
            "recommendations": [
                "优化原料采购策略，考虑长期合同锁价",
                "持续提升能源利用效率",
                "扩大生产规模以降低单位成本"
            ]
        }
        
        return json.dumps(analysis, ensure_ascii=False)


class ProfitabilityAnalysisInput(BaseModel):
    """盈利能力分析输入"""
    analysis_dimension: str = Field(..., description="分析维度(product/customer/order)")
    filters: Optional[Dict[str, Any]] = Field(None, description="筛选条件")


class ProfitabilityAnalysisTool(BaseTool):
    """盈利能力分析工具"""
    name: str = "analyze_profitability"
    description: str = "分析产品、客户或订单的盈利能力"
    args_schema: Type[BaseModel] = ProfitabilityAnalysisInput
    
    def _run(
        self,
        analysis_dimension: str,
        filters: Optional[Dict[str, Any]] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """盈利能力分析"""
        # 模拟盈利数据
        if analysis_dimension == "product":
            results = {
                "dimension": "产品",
                "data": [
                    {"product": "HRB400E", "revenue": 3500, "cost": 2800, "profit": 700, "margin": 20.0},
                    {"product": "Q235B", "revenue": 3200, "cost": 2700, "profit": 500, "margin": 15.6},
                    {"product": "Q345B", "revenue": 3800, "cost": 3100, "profit": 700, "margin": 18.4}
                ],
                "summary": {
                    "highest_margin": "HRB400E (20.0%)",
                    "lowest_margin": "Q235B (15.6%)",
                    "avg_margin": 18.0
                }
            }
        elif analysis_dimension == "customer":
            results = {
                "dimension": "客户",
                "data": [
                    {"customer": "客户A", "revenue": 10000, "profit": 1800, "margin": 18.0},
                    {"customer": "客户B", "revenue": 8000, "profit": 1600, "margin": 20.0},
                    {"customer": "客户C", "revenue": 6000, "profit": 900, "margin": 15.0}
                ],
                "summary": {
                    "top_customer": "客户A (收入最高)",
                    "most_profitable": "客户B (利润率最高)"
                }
            }
        else:  # order
            results = {
                "dimension": "订单",
                "data": [
                    {"order_id": "ORD001", "customer": "客户A", "product": "HRB400E", "profit": 50000, "margin": 22.0},
                    {"order_id": "ORD002", "customer": "客户B", "product": "Q235B", "profit": 30000, "margin": 16.0}
                ],
                "recommendations": [
                    "优先承接高利润率订单",
                    "与高价值客户建立长期合作关系"
                ]
            }
            
        return json.dumps(results, ensure_ascii=False)


class MarketAnalysisInput(BaseModel):
    """市场分析输入"""
    analysis_type: str = Field(..., description="分析类型(price/demand/competition)")
    product: Optional[str] = Field(None, description="产品类型")


class MarketAnalysisTool(BaseTool):
    """市场分析工具"""
    name: str = "analyze_market"
    description: str = "分析市场价格、需求和竞争态势"
    args_schema: Type[BaseModel] = MarketAnalysisInput
    
    def _run(
        self,
        analysis_type: str,
        product: Optional[str] = None,
        run_manager: Optional[CallbackManagerForToolRun] = None
    ) -> str:
        """市场分析"""
        if analysis_type == "price":
            result = {
                "type": "价格分析",
                "product": product or "螺纹钢",
                "current_price": 3850,
                "price_trend": "上涨",
                "change_rate": "+2.5%",
                "forecast": {
                    "short_term": "预计继续小幅上涨",
                    "factors": ["原料成本支撑", "需求季节性回升"]
                },
                "competitor_prices": {
                    "竞争对手A": 3820,
                    "竞争对手B": 3880,
                    "市场均价": 3850
                }
            }
        elif analysis_type == "demand":
            result = {
                "type": "需求分析",
                "current_demand": "稳定",
                "yoy_growth": "+5.2%",
                "key_drivers": [
                    "基建投资增长",
                    "房地产市场回暖"
                ],
                "demand_forecast": {
                    "Q1": "+3%",
                    "Q2": "+5%",
                    "Q3": "+4%",
                    "Q4": "+2%"
                }
            }
        else:  # competition
            result = {
                "type": "竞争分析",
                "market_share": {
                    "本公司": 15.5,
                    "竞争对手A": 18.2,
                    "竞争对手B": 12.8,
                    "其他": 53.5
                },
                "competitive_advantages": [
                    "产品质量稳定",
                    "交付及时率高",
                    "客户服务优秀"
                ],
                "threats": [
                    "新进入者增多",
                    "原料成本压力"
                ]
            }
            
        return json.dumps(result, ensure_ascii=False)


def get_decision_tools() -> List[BaseTool]:
    """获取经营决策智能体工具集"""
    return [
        KPIAnalysisTool(),
        CostAnalysisTool(),
        ProfitabilityAnalysisTool(),
        MarketAnalysisTool(),
        # 也可以包含一些通用工具
        HistoricalDataTool(),
        KnowledgeQueryTool()
    ]