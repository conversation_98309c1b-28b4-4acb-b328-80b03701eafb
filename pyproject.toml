[project]
name = "ai-steelmaking"
version = "0.1.0"
description = "智能炼钢系统后端，包含极目观火智能体、极数吹炼智能体、智能问数智能体"
authors = [
    {name = "HOME\\liujiazhi",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10, <4.0"
dependencies = [
    "langchain (>=0.3,<0.4)",
    "fastapi (>=0.116.1,<0.117.0)",
    "websockets (>=15.0.1,<16.0.0)",
    "aiohttp (>=3.11.11,<4.0.0)",
    "pydantic (>=2.11.7,<3.0.0)",
    "openai (>=1.97.1,<2.0.0)",
    "langchain-xinference (>=0.1.2,<0.2.0)",
    "langchain-community (>=0.3.27,<0.4.0)",
    "langchain-openai (>=0.3.28,<0.4.0)",
    "loguru (>=0.7.3,<0.8.0)",
    "uvicorn (>=0.35.0,<0.36.0)",
    "dashscope (>=1.24.0,<2.0.0)",
    "pyyaml (>=6.0.2,<7.0.0)",
    "psutil (>=7.0.0,<8.0.0)",
    "python-dotenv (>=1.1.1,<2.0.0)",
    "pandas (>=2.3.1,<3.0.0)",
    "aioconsole (>=0.8.1,<0.9.0)",
    "sqlalchemy (>=2.0.0,<3.0.0)",
    "mysql-connector-python (>=8.0.0,<9.0.0)",
    "apscheduler (>=3.11.0,<4.0.0)",
    "ws4py (>=0.6.0,<0.7.0)",
    "pyaudio (>=0.2.14,<0.3.0)"

]

[tool.poetry]
packages = [
    { include = "agents", from = "src" },
    { include = "backend", from = "src" },
    { include = "config", from = "src" },
    { include = "database", from = "src" },
]

[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
black = "^25.1.0"
mypy = "^1.17.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
mypy_path = "src"
warn_return_any = true

# 忽略找不到导入的库的错误（例如，你用了一个没有类型存根的第三方库）
ignore_missing_imports = true
