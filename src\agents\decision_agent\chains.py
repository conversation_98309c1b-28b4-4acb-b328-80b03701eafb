import asyncio
import json
from typing import Generator, List, Dict, Any, Optional
from langchain_core.prompts import PromptTemplate
from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from datetime import datetime
from config.logging import logger
from config.settings import settings
from ..common.llms import get_llm

# 全局配置
current_date = datetime.today().date()


# 已移除不需要的辅助类，直接使用 LangChain 的标准方式


class DecisionChains:
    """决策智能体业务链集合"""

    def __init__(self):
        self.llm = get_llm()

    async def classify_question_chain(self, user_query, context='', callbacks=None):
        """问题分类链"""
        from .prompts import QUESTION_CLASSIFICATION_PROMPT
        
        full_query = context if context else user_query
        prompt = PromptTemplate(
            template=QUESTION_CLASSIFICATION_PROMPT,
            input_variables=["input"]
        )
        prompt_text = prompt.format(input=full_query)

        try:
            # 非流式调用需要设置enable_thinking=False
            result = await self.llm.ainvoke(
                prompt_text,
                extra_body={"enable_thinking": False},
                config={"callbacks": callbacks} if callbacks else None
            )
            content = result.content if isinstance(result.content, str) else str(result.content)
            classification = json.loads(content)
            return classification[0] if (isinstance(classification, list) and classification) else "直接回答"
        except Exception as e:
            logger.warning(f"Question classification failed: {e}")
            return "直接回答"

    async def classify_process_chain(self, user_query, context='', callbacks=None):
        """工序分类链"""
        from .prompts import PROCESS_CLASSIFICATION_PROMPT  # 调整为相对路径导入

        full_query = context if context else user_query
        prompt = PromptTemplate(
            template=PROCESS_CLASSIFICATION_PROMPT,
            input_variables=["input"]
        )
        prompt_text = prompt.format(input=full_query)

        try:
            # 使用类实例化的LLM对象，采用ainvoke同步调用（非流式）
            result = await self.llm.ainvoke(
                prompt_text,
                extra_body={"enable_thinking": False},
                config={"callbacks": callbacks} if callbacks else None
            )
            # 确保结果为字符串类型
            content = result.content if isinstance(result.content, str) else str(result.content)

            # 解析工序分类结果
            process = json.loads(content)
            return process[0] if (isinstance(process, list) and process) else "未知"
        except json.JSONDecodeError as e:
            logger.warning(f"Process classification JSON decode failed: {e}")
            return "未知"

    async def generate_sql_chain(self, dialect, table_info, user_query, callbacks=None):
        """生成SQL链"""
        from .prompts import GENERATE_SQL_PROMPT

        prompt = PromptTemplate(
            template=GENERATE_SQL_PROMPT,
            input_variables=["dialect", "table_info", "input", "current_date"]
        )
        prompt_text = prompt.format(
            dialect=dialect,
            table_info=table_info,
            input=user_query,
            current_date=current_date
        )

        result = ""

        # 如果有回调，使用流式输出
        try:
            if callbacks:
                async for chunk in self.llm.astream(prompt_text, config={"callbacks": callbacks}):
                    if chunk.content:
                        content = chunk.content if isinstance(chunk.content, str) else str(chunk.content)
                        result += content
            else:
                response = await self.llm.ainvoke(prompt_text)
                result = response.content if isinstance(response.content, str) else str(response.content)
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            raise

        sql = result.replace("```", "").replace("sql", "").strip()
        logger.info(f"Generated SQL: {sql}")
        return sql

    async def analyze_result_chain(self, user_query, data_str, callbacks=None):
        """结果分析链"""
        from .prompts import ANALYSIS_PROMPT

        prompt = PromptTemplate(
            template=ANALYSIS_PROMPT,
            input_variables=["input", "data"]
        )
        prompt_text = prompt.format(input=user_query, data=data_str)

        result = ""

        # 如果有回调，使用流式输出
        try:
            if callbacks:
                async for chunk in self.llm.astream(prompt_text, config={"callbacks": callbacks}):
                    if chunk.content:
                        content = chunk.content if isinstance(chunk.content, str) else str(chunk.content)
                        result += content
            else:
                response = await self.llm.ainvoke(prompt_text)
                result = response.content if isinstance(response.content, str) else str(response.content)
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            raise

        return result

    async def direct_answer_chain(self, user_query, knowledge_base, context='', callbacks=None):
        """直接回答链"""
        from .prompts import DIRECT_ANSWER_PROMPT

        full_query = context if context else user_query
        prompt = PromptTemplate(
            template=DIRECT_ANSWER_PROMPT,
            input_variables=["knowledge_base", "input"]
        )
        prompt_text = prompt.format(knowledge_base=knowledge_base, input=full_query)

        result = ""

        # 如果有回调，使用流式输出
        try:
            if callbacks:
                async for chunk in self.llm.astream(prompt_text, config={"callbacks": callbacks}):
                    if chunk.content:
                        content = chunk.content if isinstance(chunk.content, str) else str(chunk.content)
                        result += content
            else:
                response = await self.llm.ainvoke(prompt_text)
                result = response.content if isinstance(response.content, str) else str(response.content)
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            raise

        return result
