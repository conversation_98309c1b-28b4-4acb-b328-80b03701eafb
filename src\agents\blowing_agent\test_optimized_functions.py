#!/usr/bin/env python3
"""
优化后功能测试脚本
专门测试优化后的阶段播报和实时监控功能
"""

import asyncio
import sys
import os

# 确保能够导入项目模块
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.agents.blowing_agent.agent import BlowingAgent


async def test_optimized_stage_broadcast():
    """测试优化后的阶段播报功能"""
    print("=" * 70)
    print("优化后阶段播报功能测试")
    print("=" * 70)

    agent = BlowingAgent()

    try:
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 测试不同时间点的阶段播报
        test_cases = [{
            "stage_info": {
                "stage_number": 1,
                "stage_name": "准备阶段"
            },
            "time": "2025-08-13 08:00:00",
            "description": "查询08:00:00之前的数据（应该是第0分钟数据）"
        }, {
            "stage_info": {
                "stage_number": 2,
                "stage_name": "脱碳阶段"
            },
            "time": "2025-08-13 08:10:00",
            "description": "查询08:10:00之前的数据（应该是第10分钟数据）"
        }, {
            "stage_info": {
                "stage_number": 3,
                "stage_name": "精炼阶段"
            },
            "time": "2025-08-13 08:15:00",
            "description": "查询08:15:00之前的数据（应该是第13分钟数据）"
        }, {
            "stage_info": {
                "stage_number": 4,
                "stage_name": "吹炼结束"
            },
            "time": "2025-08-13 08:25:00",
            "description": "查询08:25:00之前的数据（应该是最新数据）"
        }]

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- 测试案例 {i} ---")
            print(f"阶段: {test_case['stage_info']['stage_name']}")
            print(f"时间: {test_case['time']}")
            print(f"说明: {test_case['description']}")
            print("-" * 50)

            result = await agent.stage_broadcast_function(
                stage_info=test_case["stage_info"],
                broadcast_time=test_case["time"])

            if result.get("success"):
                report = result.get("report", {})
                print("✓ 阶段播报成功")
                print(f"播报类型: {report.get('stage_type')}")
                print(f"炉次号: {report.get('heat_id')}")
                print(f"播报时间: {report.get('timestamp')}")

                if report.get('blow_time'):
                    print(f"吹炼时间: 第{report.get('blow_time')}分钟")

                # 显示播报内容预览
                content = report.get('broadcast_content', '')
                if content:
                    print(f"\n播报内容预览: {content[:150]}...")

            else:
                print(f"❌ 阶段播报失败: {result.get('error')}")

            print("-" * 50)

            # 在测试案例之间稍作停顿
            if i < len(test_cases):
                await asyncio.sleep(2)

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.cleanup()


async def test_optimized_real_time_monitoring():
    """测试优化后的实时监控功能（包含数据变化分析和时间参数限制）"""
    print("\n" + "=" * 70)
    print("优化后实时监控功能测试")
    print("=" * 70)

    agent = BlowingAgent()

    try:
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 测试1：使用指定时间参数的监控
        start_time = "2025-08-13 08:10:00"
        print(f"\n【测试1】启动实时监控，指定开始时间: {start_time}")
        print("注意：监控会查询该时间之前的所有数据进行变化分析")

        start_result = await agent.real_time_monitoring_function(
            action="start", start_time=start_time)

        if start_result.get("success"):
            print("✓ 实时监控启动成功")
            print(f"监控开始时间: {start_result.get('start_time')}")

            # 运行监控60秒
            print("\n监控运行中，将运行60秒...")
            print("预期：查询08:10:00之前的数据，应该包含第0-10分钟的数据")
            print("-" * 50)

            await asyncio.sleep(60)

            print("\n60秒监控时间结束")

        else:
            print(f"❌ 实时监控启动失败: {start_result.get('error')}")
            return

        # 停止监控
        print("\n停止实时监控...")
        stop_result = await agent.real_time_monitoring_function(action="stop")

        if stop_result.get("success"):
            print("✓ 实时监控停止成功")
        else:
            print(f"❌ 实时监控停止失败: {stop_result.get('error')}")

        # 等待一下再进行下一个测试
        await asyncio.sleep(3)

        # 测试2：使用当前时间的监控
        print(f"\n【测试2】启动实时监控，使用当前时间")
        print("注意：不传入时间参数，应该使用当前时间")

        start_result2 = await agent.real_time_monitoring_function(
            action="start")

        if start_result2.get("success"):
            print("✓ 当前时间监控启动成功")
            print(f"监控开始时间: {start_result2.get('start_time')}")

            # 运行监控30秒
            print("\n监控运行中，将运行30秒...")
            print("预期：查询当前时间之前的数据，应该包含所有现有数据")
            print("-" * 50)

            await asyncio.sleep(30)

            print("\n30秒监控时间结束")

        else:
            print(f"❌ 当前时间监控启动失败: {start_result2.get('error')}")

        # 停止监控
        print("\n停止实时监控...")
        stop_result2 = await agent.real_time_monitoring_function(action="stop")

        if stop_result2.get("success"):
            print("✓ 实时监控停止成功")
        else:
            print(f"❌ 实时监控停止失败: {stop_result2.get('error')}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.cleanup()


async def test_data_change_analysis():
    """专门测试数据变化分析功能"""
    print("\n" + "=" * 70)
    print("数据变化分析功能测试")
    print("=" * 70)

    agent = BlowingAgent()

    try:
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 获取当前炉次的所有数据
        heat_id = await agent._get_current_heat_id()
        if not heat_id:
            print("❌ 无法获取当前炉次号")
            return

        print(f"当前炉次号: {heat_id}")

        # 查询所有数据
        all_data = await agent._query_current_data(heat_id)
        if not all_data or len(all_data) < 2:
            print("❌ 数据不足，无法进行变化分析")
            return

        print(f"查询到 {len(all_data)} 条数据记录")

        # 分析最后两条数据的变化
        latest_data = all_data[-1]
        prev_data = all_data[-2]

        print(f"\n数据变化分析:")
        print(f"前一条数据: 第{prev_data.get('blowtime')}分钟")
        print(f"最新数据: 第{latest_data.get('blowtime')}分钟")

        # 计算关键参数变化
        o2_change = float(latest_data.get('o2_flowrate', 0) or 0) - float(
            prev_data.get('o2_flowrate', 0) or 0)
        gun_change = float(latest_data.get('gun_height', 0) or 0) - float(
            prev_data.get('gun_height', 0) or 0)
        pressure_change = float(latest_data.get('blow_pres', 0) or 0) - float(
            prev_data.get('blow_pres', 0) or 0)

        print(f"\n关键参数变化:")
        print(f"- 氧气流量变化: {o2_change:+.0f} Nm³/min")
        print(f"- 枪位高度变化: {gun_change:+.0f} mm")
        print(f"- 吹炼压力变化: {pressure_change:+.2f} MPa")
        print(
            f"- 火焰颜色变化: {prev_data.get('fire_color')} → {latest_data.get('fire_color')}"
        )

        # 生成完整的监控报告
        print(f"\n生成完整监控报告...")
        report = await agent._generate_optimized_monitoring_report(
            heat_id, latest_data, all_data)

        if report and not report.get('error'):
            print("✓ 监控报告生成成功")
            print(f"报告时间: {report.get('timestamp')}")
            print(f"吹炼时间: 第{report.get('blow_time')}分钟")

            # 显示趋势分析
            trend = report.get('trend_analysis', '')
            if trend:
                print(f"\n趋势分析: {trend[:200]}...")

            # 显示大模型分析预览
            llm_analysis = report.get('llm_analysis', '')
            if llm_analysis:
                print(f"\n大模型分析预览: {llm_analysis[:300]}...")

        else:
            print(f"❌ 监控报告生成失败: {report.get('error') if report else '未知错误'}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.cleanup()


async def test_time_parameter_limitation():
    """专门测试时间参数限制功能"""
    print("\n" + "=" * 70)
    print("时间参数限制功能测试")
    print("=" * 70)

    agent = BlowingAgent()

    try:
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 获取当前炉次号
        heat_id = await agent._get_current_heat_id()
        if not heat_id:
            print("❌ 无法获取当前炉次号")
            return

        print(f"当前炉次号: {heat_id}")

        # 测试不同时间参数的数据查询
        test_times = [
            "2025-08-13 08:05:00",  # 应该查询到第0-5分钟的数据
            "2025-08-13 08:10:00",  # 应该查询到第0-10分钟的数据
            "2025-08-13 08:15:00",  # 应该查询到第0-13分钟的数据
        ]

        for i, test_time in enumerate(test_times, 1):
            print(f"\n--- 测试 {i}: 时间参数 {test_time} ---")

            # 解析时间
            from datetime import datetime
            query_time = datetime.strptime(test_time, '%Y-%m-%d %H:%M:%S')

            # 查询该时间之前的所有数据
            data_before_time = await agent._query_data_before_time_all(
                heat_id, query_time)

            if data_before_time:
                print(f"✓ 查询到 {len(data_before_time)} 条数据")

                # 显示数据范围
                blow_times = [
                    int(d.get('blowtime', 0) or 0) for d in data_before_time
                ]
                min_time = min(blow_times)
                max_time = max(blow_times)
                print(f"数据时间范围: 第{min_time}分钟 - 第{max_time}分钟")

                # 验证所有数据都在指定时间之前
                all_before = True
                for data in data_before_time:
                    data_datetime = data.get('datetime')
                    if data_datetime and data_datetime > query_time:
                        all_before = False
                        break

                if all_before:
                    print("✓ 所有数据都在指定时间之前")
                else:
                    print("❌ 发现超出时间范围的数据")

            else:
                print("❌ 未查询到数据")

            print("-" * 50)

        # 对比：查询所有数据（不限制时间）
        print(f"\n--- 对比：查询所有数据（不限制时间） ---")
        all_data = await agent._query_current_data(heat_id)
        if all_data:
            blow_times_all = [int(d.get('blowtime', 0) or 0) for d in all_data]
            min_time_all = min(blow_times_all)
            max_time_all = max(blow_times_all)
            print(f"✓ 查询到 {len(all_data)} 条数据")
            print(f"数据时间范围: 第{min_time_all}分钟 - 第{max_time_all}分钟")
        else:
            print("❌ 未查询到数据")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await agent.cleanup()


async def main():
    """主测试函数"""
    print("开始优化后功能测试...")

    # 测试优化后的阶段播报
    await test_optimized_stage_broadcast()

    # 测试优化后的实时监控
    await test_optimized_real_time_monitoring()

    # 测试数据变化分析
    await test_data_change_analysis()

    # 测试时间参数限制功能
    await test_time_parameter_limitation()

    print("\n" + "=" * 70)
    print("所有优化功能测试完成！")
    print("包含：阶段播报、实时监控、数据变化分析、时间参数限制")
    print("=" * 70)


if __name__ == "__main__":
    asyncio.run(main())
