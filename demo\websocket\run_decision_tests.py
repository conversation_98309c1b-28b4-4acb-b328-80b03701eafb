#!/usr/bin/env python3
"""
经营决策智能体自动化测试脚本
运行所有测试用例并生成测试报告
"""

import asyncio
import json
import websockets
from datetime import datetime
import sys
from typing import Dict, Any, List
from colorama import init, Fore, Style
from decision_test_cases import TEST_CASES, KPI_ALERTS, BATCH_TEST_SCENARIOS

# 初始化colorama
init()


class AutomatedTestRunner:
    """自动化测试运行器"""
    
    def __init__(self, url: str = "ws://localhost:8001/ws/agent"):
        self.url = url
        self.websocket = None
        self.test_results = []
        self.current_test = None
        self.response_buffer = []
        
    async def connect(self):
        """连接到服务器"""
        print(f"{Fore.CYAN}连接到 {self.url}...{Style.RESET_ALL}")
        self.websocket = await websockets.connect(self.url)
        # 等待欢迎消息
        welcome = await self.websocket.recv()
        print(f"{Fore.GREEN}✓ 连接成功{Style.RESET_ALL}")
        
    async def disconnect(self):
        """断开连接"""
        if self.websocket:
            await self.websocket.close()
            
    async def send_and_receive(self, query: str, timeout: float = 5.0) -> str:
        """发送查询并等待响应"""
        # 清空响应缓冲
        self.response_buffer = []
        
        # 发送查询
        message = {
            "type": "query",
            "content": query,
            "context": {}
        }
        await self.websocket.send(json.dumps(message))
        
        # 收集响应
        start_time = asyncio.get_event_loop().time()
        responses = []
        
        while asyncio.get_event_loop().time() - start_time < timeout:
            try:
                response = await asyncio.wait_for(self.websocket.recv(), timeout=0.5)
                data = json.loads(response)
                responses.append(data)
                
                # 如果收到分析完成的信号，停止等待
                if data.get("type") == "analysis" or data.get("is_complete"):
                    break
                    
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                print(f"{Fore.RED}接收响应错误: {e}{Style.RESET_ALL}")
                break
                
        return responses
        
    async def run_single_test(self, category: str, test_case: dict) -> dict:
        """运行单个测试用例"""
        query = test_case["query"]
        expected = test_case["expected"]
        
        print(f"\n  {Fore.BLUE}测试: {query}{Style.RESET_ALL}")
        
        try:
            # 发送查询并获取响应
            responses = await self.send_and_receive(query)
            
            # 分析响应
            success = len(responses) > 0
            response_text = ""
            
            for resp in responses:
                if "content" in resp:
                    response_text += resp["content"] + "\n"
                elif "message" in resp:
                    response_text += resp["message"] + "\n"
                    
            # 简单的验证逻辑
            if success and response_text:
                print(f"  {Fore.GREEN}✓ 收到响应{Style.RESET_ALL}")
                # 可以在这里添加更复杂的验证逻辑
            else:
                print(f"  {Fore.RED}✗ 未收到有效响应{Style.RESET_ALL}")
                
            return {
                "category": category,
                "query": query,
                "expected": expected,
                "success": success,
                "response": response_text[:200] + "..." if len(response_text) > 200 else response_text,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"  {Fore.RED}✗ 测试失败: {e}{Style.RESET_ALL}")
            return {
                "category": category,
                "query": query,
                "expected": expected,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            
    async def run_category_tests(self, category: str, cases: list) -> list:
        """运行一个类别的所有测试"""
        print(f"\n{Fore.YELLOW}[{category}] 开始测试 ({len(cases)} 个用例){Style.RESET_ALL}")
        print("-" * 60)
        
        results = []
        for case in cases:
            result = await self.run_single_test(category, case)
            results.append(result)
            await asyncio.sleep(1)  # 避免请求过快
            
        # 统计结果
        success_count = sum(1 for r in results if r["success"])
        print(f"\n{Fore.CYAN}[{category}] 完成: {success_count}/{len(cases)} 成功{Style.RESET_ALL}")
        
        return results
        
    async def run_alert_tests(self) -> list:
        """运行KPI预警测试"""
        print(f"\n{Fore.RED}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.RED}KPI预警测试{Style.RESET_ALL}")
        print(f"{Fore.RED}{'='*60}{Style.RESET_ALL}")
        
        results = []
        
        for alert_case in KPI_ALERTS:
            print(f"\n{Fore.YELLOW}测试: {alert_case['name']}{Style.RESET_ALL}")
            
            # 发送预警
            message = {
                "type": "action",
                "action": "analyze_now",
                "data": alert_case["data"]
            }
            
            try:
                await self.websocket.send(json.dumps(message))
                responses = await self.send_and_receive("", timeout=5.0)
                
                success = len(responses) > 0
                print(f"  {Fore.GREEN if success else Fore.RED}{'✓' if success else '✗'} {'收到分析结果' if success else '未收到响应'}{Style.RESET_ALL}")
                
                results.append({
                    "type": "alert",
                    "name": alert_case["name"],
                    "success": success,
                    "timestamp": datetime.now().isoformat()
                })
                
            except Exception as e:
                print(f"  {Fore.RED}✗ 测试失败: {e}{Style.RESET_ALL}")
                results.append({
                    "type": "alert",
                    "name": alert_case["name"],
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
                
            await asyncio.sleep(2)
            
        return results
        
    async def run_all_tests(self):
        """运行所有测试"""
        print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}经营决策智能体自动化测试{Style.RESET_ALL}")
        print(f"{Fore.CYAN}开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        
        all_results = {
            "start_time": datetime.now().isoformat(),
            "test_results": {},
            "alert_results": [],
            "summary": {}
        }
        
        try:
            # 连接服务器
            await self.connect()
            
            # 运行查询测试
            for category, info in TEST_CASES.items():
                results = await self.run_category_tests(category, info["cases"])
                all_results["test_results"][category] = results
                
            # 运行预警测试
            alert_results = await self.run_alert_tests()
            all_results["alert_results"] = alert_results
            
            # 生成总结
            total_tests = sum(len(info["cases"]) for info in TEST_CASES.values()) + len(KPI_ALERTS)
            success_tests = sum(
                sum(1 for r in results if r["success"]) 
                for results in all_results["test_results"].values()
            ) + sum(1 for r in alert_results if r["success"])
            
            all_results["summary"] = {
                "total_tests": total_tests,
                "success_tests": success_tests,
                "failed_tests": total_tests - success_tests,
                "success_rate": f"{(success_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%",
                "end_time": datetime.now().isoformat()
            }
            
            # 打印总结
            self._print_summary(all_results["summary"])
            
            # 保存结果
            self._save_results(all_results)
            
        except Exception as e:
            print(f"{Fore.RED}测试运行错误: {e}{Style.RESET_ALL}")
        finally:
            await self.disconnect()
            
    def _print_summary(self, summary: dict):
        """打印测试总结"""
        print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}测试总结{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        
        print(f"总测试数: {summary['total_tests']}")
        print(f"{Fore.GREEN}成功: {summary['success_tests']}{Style.RESET_ALL}")
        print(f"{Fore.RED}失败: {summary['failed_tests']}{Style.RESET_ALL}")
        print(f"成功率: {summary['success_rate']}")
        
        # 计算测试时长
        start = datetime.fromisoformat(summary.get("end_time", ""))
        duration = (datetime.now() - start).total_seconds()
        print(f"测试时长: {duration:.1f} 秒")
        
    def _save_results(self, results: dict):
        """保存测试结果"""
        filename = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n{Fore.GREEN}测试结果已保存到: {filename}{Style.RESET_ALL}")


async def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help":
            print("用法: python run_decision_tests.py [选项]")
            print("选项:")
            print("  --help    显示帮助信息")
            print("  --quick   只运行快速测试")
            print("  --full    运行完整测试套件")
            return
            
    # 运行测试
    runner = AutomatedTestRunner()
    await runner.run_all_tests()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}测试被用户中断{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}错误: {e}{Style.RESET_ALL}")