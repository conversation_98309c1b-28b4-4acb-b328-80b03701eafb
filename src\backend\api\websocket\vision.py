from fastapi import WebSocket, WebSocketDisconnect
import json
import uuid
import base64
from config.logging import logger
from backend.services import ConnectionManager
from agents import VisionAgent


async def vision_websocket_endpoint(websocket: WebSocket):
    """视觉智能体专用WebSocket端点"""
    client_id = str(uuid.uuid4())
    
    connection_manager: ConnectionManager = websocket.app.state.connection_manager
    
    await connection_manager.connect(websocket, client_id)
    
    try:
        vision_agent = VisionAgent()
        
        await connection_manager.send_json(client_id, {
            "type": "connected",
            "client_id": client_id,
            "agent": "vision",
            "message": "欢迎使用视觉分析智能体！我可以帮助您分析炉口火焰图像和生产过程视觉数据。"
        })
        
        while True:
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                message_type = message.get("type", "query")
                
                if message_type == "analyze_image":
                    image_data = message.get("image", "")
                    image_format = message.get("format", "base64")
                    analysis_type = message.get("analysis_type", "flame")
                    
                    await connection_manager.send_json(client_id, {
                        "type": "processing",
                        "agent": "vision",
                        "message": "正在分析图像..."
                    })
                    
                    if image_format == "base64":
                        image_bytes = base64.b64decode(image_data)
                    else:
                        image_bytes = image_data
                    
                    if analysis_type == "flame":
                        result = await vision_agent.analyze_flame(image_bytes)
                    elif analysis_type == "surface":
                        result = await vision_agent.analyze_surface(image_bytes)
                    else:
                        result = await vision_agent.analyze_general(image_bytes)
                    
                    await connection_manager.send_json(client_id, {
                        "type": "analysis_result",
                        "agent": "vision",
                        "result": result,
                        "status": "completed"
                    })
                    
                elif message_type == "stream_analysis":
                    stream_url = message.get("stream_url", "")
                    
                    await connection_manager.send_json(client_id, {
                        "type": "stream_started",
                        "agent": "vision",
                        "message": "开始实时视频分析..."
                    })
                    
                    async for frame_result in vision_agent.analyze_stream(stream_url):
                        await connection_manager.send_json(client_id, {
                            "type": "frame_result",
                            "agent": "vision",
                            "result": frame_result
                        })
                        
                elif message_type == "ping":
                    await connection_manager.send_json(client_id, {"type": "pong", "agent": "vision"})
                    
            except json.JSONDecodeError:
                await connection_manager.send_json(client_id, {
                    "type": "error",
                    "agent": "vision",
                    "message": "无效的消息格式"
                })
                    
    except WebSocketDisconnect:
        logger.info(f"Vision agent client {client_id} disconnected")
        connection_manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"Vision WebSocket error for client {client_id}: {e}")
        connection_manager.disconnect(client_id)
        await websocket.close(code=1011, reason="Server error")