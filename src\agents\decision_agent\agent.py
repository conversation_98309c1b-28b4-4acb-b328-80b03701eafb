import asyncio
import json
import websocket
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime
from config.logging import logger
from config.settings import settings
from database.connection import get_engine
from database.schema import get_table_info
from .chains import DecisionChains
from .knowledge_base import load_knowledge_base
from .history_manager import HistoryManager
from .database_query import execute_sql


class DecisionAgent:
    """经营决策智能体 - 智能问数功能实现"""

    def __init__(self):
        self.history_manager = HistoryManager(max_history_rounds=2)
        self.knowledge_base = load_knowledge_base()
        self.chains = DecisionChains()
        self.engine = None
        self.initialized = False

    async def initialize(self):
        """初始化智能体"""
        try:
            self.engine = get_engine()
            logger.info("DecisionAgent数据库连接成功")
            self.initialized = True
        except Exception as e:
            logger.error(f"DecisionAgent数据库连接失败: {e}")
            self.initialized = False

    async def process(self, query: str, context: Dict[str, Any]) -> str:
        """处理用户查询 - 简单实现"""
        if not self.initialized:
            return "数据库未连接，无法处理查询"

        try:
            # 生成带历史的上下文
            context_prompt = self.history_manager.get_context_prompt(query)

            # 问题分类
            question_type = await self.chains.classify_question_chain(
                query, context=context_prompt)

            if question_type == "适合数据库查询":
                # 新增工序判断步骤
                process_type = await self.chains.classify_process_chain(
                    query, context=context)
                if process_type == "未知":
                    return "无法确定查询的工序类型，请明确说明要询问的工序"

                # 根据工序类型筛选表信息，减少提示词内容
                if process_type == "炼钢工序":
                    target_tables = [
                        "steelmaking_indicator_info", "steelmaking_daily_data"
                    ]
                elif process_type == "轧钢工序":
                    target_tables = [
                        "steelrolling_indicator_info",
                        "steelrolling_daily_data"
                    ]
                else:  # 炼铁工序
                    target_tables = [
                        "ironmaking_indicator_info", "ironmaking_daily_data"
                    ]
                indicator_info_sql = 'select idx_name as 指标名称 from ' + target_tables[
                    0] + ';'
                # 获取表结构
                table_info = get_table_info(target_tables)
                if self.engine is None:
                    return "数据库连接不可用"
                _df, data_str = execute_sql(self.engine, indicator_info_sql)
                table_info = table_info + "\n其中indicator_info表中可选择的指标选项如下:\n" + data_str

                # 生成SQL
                sql = await self.chains.generate_sql_chain(
                    dialect="mysql", table_info=table_info, user_query=query)

                # 执行查询
                if self.engine is None:
                    return "数据库连接不可用"
                df, data_str = execute_sql(self.engine, sql)
                if df is None:
                    return "查询执行失败"

                # 分析结果
                analysis = await self.chains.analyze_result_chain(
                    query, data_str)
                analysis = str(analysis) if not isinstance(analysis,
                                                           str) else analysis

                # 记录历史
                self.history_manager.add_record(user_query=query,
                                                question_type=question_type,
                                                result=data_str + analysis,
                                                sql=sql)
                self.history_manager.add_dialogue(query, data_str + analysis)

                return analysis
            else:
                # 直接回答
                answer = await self.chains.direct_answer_chain(
                    user_query=query,
                    knowledge_base=self.knowledge_base,
                    context=context_prompt)
                answer = str(answer) if not isinstance(answer, str) else answer

                # 记录历史
                self.history_manager.add_record(user_query=query,
                                                question_type=question_type,
                                                result=answer)
                self.history_manager.add_dialogue(query, answer)

                return answer

        except Exception as e:
            logger.error(f"处理查询失败: {e}")
            return f"处理查询时发生错误: {str(e)}"

    async def process_stream(
            self,
            query: str,
            context: Dict[str, Any],
            callbacks: Optional[List[Any]] = None
    ) -> AsyncGenerator[str, None]:
        """流式处理查询"""
        if not self.initialized:
            yield "数据库未连接，无法处理查询"
            return

        try:
            # 生成带历史的上下文
            context_prompt = self.history_manager.get_context_prompt(query)

            # 问题分类
            question_type = await self.chains.classify_question_chain(
                query, context=context_prompt, callbacks=callbacks)

            if question_type == "适合数据库查询":

                # 新增工序判断步骤
                process_type = await self.chains.classify_process_chain(
                    query, context=context)
                if process_type == "未知":
                    yield "无法确定查询的工序类型，请明确说明要询问的工序"
                    return

                # 根据工序类型筛选表信息，减少提示词内容
                if process_type == "炼钢工序":
                    target_tables = [
                        "steelmaking_indicator_info", "steelmaking_daily_data"
                    ]
                elif process_type == "轧钢工序":
                    target_tables = [
                        "steelrolling_indicator_info",
                        "steelrolling_daily_data"
                    ]
                else:  # 炼铁工序
                    target_tables = [
                        "ironmaking_indicator_info", "ironmaking_daily_data"
                    ]
                indicator_info_sql = 'select idx_name as 指标名称 from ' + target_tables[
                    0] + ';'
                # 获取表结构
                table_info = get_table_info(target_tables)
                if self.engine is None:
                    yield "数据库连接不可用"
                    return
                _df, data_str = execute_sql(self.engine, indicator_info_sql)
                table_info = table_info + "\n其中indicator_info表中可选择的指标选项如下:\n" + data_str
                print("table_info:", table_info)
                # 生成SQL
                sql = await self.chains.generate_sql_chain(
                    dialect="mysql",
                    table_info=table_info,
                    user_query=query,
                    # callbacks=callbacks
                    callbacks=None)
                print("sql:", sql)
                # 执行查询
                if self.engine is None:
                    yield "数据库连接不可用"
                    return
                df, data_str = execute_sql(self.engine, sql)
                if df is None:
                    yield "查询执行失败"
                    return

                # 先输出数据查询结果
                yield f"查询到 {len(df)} 条数据：\n\n"

                # 流式分析结果 - 注意这里已经通过回调实时发送了
                analysis = await self.chains.analyze_result_chain(
                    query, data_str, callbacks=callbacks)
                analysis = str(analysis) if not isinstance(analysis,
                                                           str) else analysis

                # 记录历史
                self.history_manager.add_record(user_query=query,
                                                question_type=question_type,
                                                result=data_str + "\n" +
                                                analysis,
                                                sql=sql)
                self.history_manager.add_dialogue(query,
                                                  data_str + "\n" + analysis)

                # 因为已经通过回调流式发送了，这里不需要再yield
            else:
                # 流式直接回答 - 注意这里已经通过回调实时发送了
                answer = await self.chains.direct_answer_chain(
                    user_query=query,
                    knowledge_base=self.knowledge_base,
                    context=context_prompt,
                    callbacks=callbacks)
                answer = str(answer) if not isinstance(answer, str) else answer

                # 记录历史
                self.history_manager.add_record(user_query=query,
                                                question_type=question_type,
                                                result=answer)
                self.history_manager.add_dialogue(query, answer)

                # 因为已经通过回调流式发送了，这里返回一个空值表示完成
                yield ""

        except Exception as e:
            logger.error(f"流式处理查询失败: {e}")
            yield f"处理查询时发生错误: {str(e)}"

    def get_history(self, limit: int = 10) -> str:
        """获取历史记录"""
        return self.history_manager.format_full_history(limit)

    def clear_history(self) -> str:
        """清空历史记录"""
        return self.history_manager.clear_all()

    # 保留原有的接口以保持兼容性
    async def make_production_decision(
            self, context: Dict[str, Any],
            constraints: Dict[str, Any]) -> Dict[str, Any]:
        """生产决策"""
        query = f"分析生产数据，给出生产决策建议。约束条件：{constraints}"
        result = await self.process(query, context)
        return {
            "status": "success",
            "decision": {
                "recommendation": result,
                "confidence": 0.85
            }
        }

    async def make_quality_decision(
            self, context: Dict[str, Any],
            constraints: Dict[str, Any]) -> Dict[str, Any]:
        """质量决策"""
        query = f"分析质量数据，给出质量改进建议。约束条件：{constraints}"
        result = await self.process(query, context)
        return {
            "status": "success",
            "decision": {
                "recommendation": result,
                "confidence": 0.85
            }
        }

    async def make_cost_decision(
            self, context: Dict[str, Any],
            constraints: Dict[str, Any]) -> Dict[str, Any]:
        """成本决策"""
        query = f"分析成本数据，给出成本优化建议。约束条件：{constraints}"
        result = await self.process(query, context)
        return {
            "status": "success",
            "decision": {
                "recommendation": result,
                "confidence": 0.85
            }
        }

    async def analyze_kpi(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """KPI分析"""
        query = "分析当前的KPI指标表现"
        result = await self.process(query, context)
        return {"status": "success", "analysis": result}

    async def make_general_decision(
            self, context: Dict[str, Any],
            constraints: Dict[str, Any]) -> Dict[str, Any]:
        """通用决策"""
        query = context.get("query", "请给出经营建议")
        result = await self.process(query, context)
        return {
            "status": "success",
            "decision": {
                "recommendation": result,
                "confidence": 0.85
            }
        }

    async def optimize_parameters(self, target: str, current_params: Dict[str, Any], constraints: Dict[str, Any]) -> \
    Dict[str, Any]:
        """参数优化"""
        query = f"优化{target}相关参数，当前参数：{current_params}，约束：{constraints}"
        result = await self.process(query, {"current_params": current_params})
        return {
            "status": "success",
            "optimization": {
                "target": target,
                "recommendation": result
            }
        }

    async def analyze_scenario(self, scenario_type: str,
                               params: Dict[str, Any]) -> Dict[str, Any]:
        """场景分析"""
        query = f"进行{scenario_type}场景分析，参数：{params}"
        result = await self.process(query, params)
        return {
            "status": "success",
            "scenario": scenario_type,
            "analysis": result
        }
