# 智能体WebSocket API接口规范

## 概述

本文档定义了钢铁大模型智能体系统对外提供的WebSocket API接口规范。所有与智能体交互的外部系统都应遵循此规范进行通信。WebSocket端点优先处理JSON格式的消息，所有消息都应该是JSON对象，包含 `type`、`content`、`context` 等字段。

## 消息格式

### 基本格式
```json
{
  "type": "message_type",
  "content": "message_content",
  "context": {}
}
```

## 支持的消息类型

### 1. 查询消息 (query)
```json
{
  "type": "query",
  "content": "请查询今天的产量数据",
  "context": {
    "department": "production",
    "date": "2025-08-04"
  }
}
```

### 2. 退出消息 (exit)
```json
{
  "type": "exit"
}
```

### 3. 查看历史 (history)
```json
{
  "type": "history"
}
```

### 4. 清空历史 (clear_history)
```json
{
  "type": "clear_history"
}
```

### 5. 心跳消息 (ping)
```json
{
  "type": "ping"
}
```

## 响应格式

### 成功响应
```json
{
  "type": "stream_start|stream|stream_end|info|history",
  "agent": "decision",
  "message": "响应消息",
  "content": "具体内容",
  "is_finished": false
}
```

### 错误响应
```json
{
  "type": "error",
  "agent": "decision",
  "message": "错误描述"
}
```

## 注意事项

- 所有消息必须是有效的JSON格式
- 如果发送非JSON格式的消息，将收到格式错误的响应
- `content` 字段是必需的（对于query类型消息）
- `context` 字段是可选的，用于提供额外的上下文信息

## 示例客户端代码

```javascript
// 发送查询消息
const queryMessage = {
  type: "query",
  content: "请分析最近一周的生产效率",
  context: {
    timeRange: "7days",
    metrics: ["efficiency", "output"]
  }
};
websocket.send(JSON.stringify(queryMessage));

// 发送心跳
const pingMessage = {
  type: "ping"
};
websocket.send(JSON.stringify(pingMessage));
```
