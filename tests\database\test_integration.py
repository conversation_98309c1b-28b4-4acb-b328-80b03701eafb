"""数据库模块集成测试"""
import pytest
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
import json

from database import (
    get_db_uri,
    create_engine,
    get_engine,
    get_db_session,
    test_connection as db_test_connection,  # 重命名以避免pytest警告
    get_table_info,
    get_default_table_info
)


class TestDatabaseIntegration:
    """测试数据库模块的集成功能"""
    
    def test_full_database_workflow(self, mock_settings, mock_engine, mock_inspector):
        """测试完整的数据库工作流程"""
        with patch("database.connection.settings", mock_settings):
            with patch("database.schema.settings", mock_settings):
                with patch("sqlalchemy.create_engine") as mock_create_engine:
                    mock_create_engine.return_value = mock_engine
                    
                    with patch("sqlalchemy.inspect") as mock_inspect:
                        mock_inspect.return_value = mock_inspector
                        
                        # 1. 创建数据库连接
                        uri = get_db_uri()
                        assert "mysql+mysqlconnector://" in uri
                        
                        # 2. 获取引擎
                        engine = get_engine()
                        assert engine is not None
                        
                        # 3. 测试连接
                        is_connected = db_test_connection()
                        assert is_connected is True
                        
                        # 4. 获取表结构信息
                        table_info = get_table_info(mock_settings.TARGET_TABLES)
                        assert "steelmaking_indicator_info" in table_info
                        
                        # 5. 获取会话
                        session = get_db_session()
                        assert session is not None
    
    def test_database_fallback_mechanism(self, mock_settings):
        """测试数据库连接失败时的降级机制"""
        with patch("database.connection.settings", mock_settings):
            with patch("database.schema.settings", mock_settings):
                # 模拟get_engine失败，这样get_table_info会捕获异常并返回默认信息
                with patch("database.schema.get_engine") as mock_get_engine:
                    mock_get_engine.side_effect = Exception("Connection refused")
                    
                    # 获取表信息应该返回默认信息
                    table_info = get_table_info()
                    default_info = get_default_table_info()
                    
                    # 验证返回的是默认表信息（至少包含预期的表）
                    assert "steelmaking_indicator_info" in table_info
                    assert "炼钢指标信息表" in table_info
                    
                    # 测试连接应该返回False
                    with patch("database.connection.get_engine") as mock_conn_get_engine:
                        mock_conn_get_engine.side_effect = Exception("No engine available")
                        is_connected = db_test_connection()
                        assert is_connected is False
    
    def test_sql_execution_with_real_engine(self, mock_settings, mock_engine):
        """测试使用真实引擎执行SQL（模拟）"""
        with patch("database.connection.settings", mock_settings):
            with patch("database.connection.get_engine") as mock_get_engine:
                mock_get_engine.return_value = mock_engine
                
                # 模拟pandas read_sql
                with patch("pandas.read_sql") as mock_read_sql:
                    # 模拟查询结果
                    mock_df = pd.DataFrame({
                        "idx_code": ["STEEL001", "STEEL002"],
                        "idx_name": ["吨钢毛利", "钢坯成本"],
                        "idx_value": [280.5, 2650.0]
                    })
                    mock_read_sql.return_value = mock_df
                    
                    # 执行查询
                    sql = "SELECT * FROM steelmaking_indicator_info"
                    df = pd.read_sql(sql, mock_engine)
                    
                    assert len(df) == 2
                    assert "idx_code" in df.columns
                    assert df.iloc[0]["idx_name"] == "吨钢毛利"
    
    def test_connection_pool_configuration(self, mock_settings):
        """测试连接池配置"""
        with patch("database.connection.settings", mock_settings):
            with patch("sqlalchemy.create_engine") as mock_create_engine:
                mock_engine = Mock()
                mock_create_engine.return_value = mock_engine
                
                # 重置全局引擎
                import database.connection
                database.connection._engine = None
                
                engine = create_engine()
                
                # 验证连接池参数
                call_kwargs = mock_create_engine.call_args[1]
                assert call_kwargs["pool_size"] == 10
                assert call_kwargs["max_overflow"] == 20
                assert call_kwargs["pool_pre_ping"] is True
                assert call_kwargs["pool_recycle"] == 3600
                
                # 清理
                database.connection._engine = None
    
    def test_session_transaction_handling(self, mock_settings, mock_engine):
        """测试会话事务处理"""
        with patch("database.connection.settings", mock_settings):
            with patch("database.connection.get_engine") as mock_get_engine:
                mock_get_engine.return_value = mock_engine
                
                with patch("database.connection.sessionmaker") as mock_sessionmaker:
                    mock_session_class = Mock()
                    mock_session = Mock()
                    mock_session_class.return_value = mock_session
                    mock_sessionmaker.return_value = mock_session_class
                    
                    session = get_db_session()
                    
                    # 验证会话配置
                    mock_sessionmaker.assert_called_with(
                        autocommit=False,  # 不自动提交
                        autoflush=False,   # 不自动刷新
                        bind=mock_engine
                    )
                    
                    # 模拟事务操作
                    session.begin()
                    session.commit()
                    session.rollback()
                    session.close()
                    
                    # 验证方法可以被调用
                    session.begin.assert_called_once()
                    session.commit.assert_called_once()
                    session.rollback.assert_called_once()
                    session.close.assert_called_once()


class TestErrorHandling:
    """测试错误处理"""
    
    def test_invalid_database_config(self):
        """测试无效的数据库配置"""
        invalid_settings = Mock()
        invalid_settings.DB_DIALECT = None
        invalid_settings.DB_DRIVER = None
        invalid_settings.DB_USERNAME = ""
        invalid_settings.DB_PASSWORD = ""
        invalid_settings.DB_HOST = ""
        invalid_settings.DB_PORT = ""
        invalid_settings.DB_NAME = ""
        
        with patch("database.connection.settings", invalid_settings):
            uri = get_db_uri()
            # 即使配置无效，也应该生成URI（可能是无效的）
            assert uri == "None+None://:@:/"
    
    def test_network_timeout_handling(self, mock_settings):
        """测试网络超时处理"""
        with patch("database.connection.settings", mock_settings):
            with patch("sqlalchemy.create_engine") as mock_create_engine:
                from sqlalchemy.exc import TimeoutError
                mock_create_engine.side_effect = TimeoutError()
                
                with pytest.raises(Exception) as exc_info:
                    create_engine()
                
                assert "数据库连接失败" in str(exc_info.value)
    
    def test_authentication_failure(self, mock_settings):
        """测试认证失败"""
        with patch("database.connection.settings", mock_settings):
            with patch("sqlalchemy.create_engine") as mock_create_engine:
                from sqlalchemy.exc import OperationalError
                mock_create_engine.side_effect = OperationalError(
                    "Access denied for user", None, None
                )
                
                with pytest.raises(Exception) as exc_info:
                    create_engine()
                
                assert "数据库连接失败" in str(exc_info.value)
# Override to avoid pytest warning
test_connection = None
