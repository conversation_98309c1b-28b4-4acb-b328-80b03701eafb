import json
import asyncio
from fastapi import WebSocket, WebSocketDisconnect
from src.backend.services.speech_recognition import <PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON><PERSON><PERSON>, CHUNK_SIZE
from src.config.logging import logger


async def speech_websocket_endpoint(websocket: WebSocket):
    """语音识别WebSocket端点"""
    await websocket.accept()
    logger.info("语音识别客户端已连接")
    
    handler = SpeechRecognitionHandler()
    client_connected = True
    
    try:
        while client_connected:
            try:
                # 接收客户端消息
                message = await asyncio.wait_for(websocket.receive(), timeout=0.1)
                
                if "bytes" in message:
                    # 接收音频数据
                    audio_data = message["bytes"]
                    handler.audio_buffer.extend(audio_data)
                    
                    # 当缓冲区有足够数据时发送
                    while len(handler.audio_buffer) >= CHUNK_SIZE and handler.is_connected:
                        chunk = bytes(handler.audio_buffer[:CHUNK_SIZE])
                        handler.audio_buffer = handler.audio_buffer[CHUNK_SIZE:]
                        
                        success = await handler.send_audio_chunk(chunk)
                        if not success:
                            break
                            
                        # 发送当前识别结果
                        result = handler.get_recognition_result()
                        if result:
                            await websocket.send_json({
                                "type": "recognition",
                                "data": result
                            })
                    
                elif "text" in message:
                    data = json.loads(message["text"])
                    action = data.get("action")
                    
                    if action == "start":
                        logger.info("收到开始录音命令")
                        
                        # 断开之前的连接
                        if handler.is_connected:
                            await handler.disconnect_from_xfyun()
                        
                        # 建立新连接
                        connected = await handler.connect_to_xfyun()
                        if connected:
                            handler.audio_buffer = bytearray()
                            await websocket.send_json({
                                "type": "status",
                                "message": "已开始录音"
                            })
                        else:
                            await websocket.send_json({
                                "type": "error",
                                "message": "无法连接到语音识别服务"
                            })
                    
                    elif action == "stop":
                        logger.info("收到停止录音命令")
                        
                        if handler.is_connected:
                            # 发送剩余的音频数据
                            if len(handler.audio_buffer) > 0:
                                # 填充到完整的帧
                                while len(handler.audio_buffer) < CHUNK_SIZE:
                                    handler.audio_buffer.append(0)
                                chunk = bytes(handler.audio_buffer[:CHUNK_SIZE])
                                await handler.send_audio_chunk(chunk)
                            
                            # 发送结束帧
                            await handler.send_end_frame()
                            
                            # 等待最后的识别结果
                            await asyncio.sleep(2)
                            
                            # 发送最终结果
                            final_text = handler.get_final_text()
                            if final_text:
                                await websocket.send_json({
                                    "type": "final_result",
                                    "text": final_text
                                })
                        
                        # 断开连接
                        await handler.disconnect_from_xfyun()
                        
                        await websocket.send_json({
                            "type": "status",
                            "message": "已停止录音"
                        })
                    
                    elif action == "ping":
                        await websocket.send_json({"type": "pong"})
                        
            except asyncio.TimeoutError:
                # 检查是否有未发送的识别结果
                if handler.is_connected:
                    result = handler.get_recognition_result()
                    if result:
                        await websocket.send_json({
                            "type": "recognition",
                            "data": result
                        })
                continue
                
            except WebSocketDisconnect:
                logger.info("语音识别客户端主动断开连接")
                client_connected = False
                break
                
            except Exception as e:
                if "disconnect" in str(e).lower():
                    logger.info("检测到语音识别客户端断开连接")
                    client_connected = False
                    break
                else:
                    logger.error(f"处理语音识别消息时出错: {e}")
                    
    except Exception as e:
        logger.error(f"语音识别WebSocket错误: {e}")
        
    finally:
        client_connected = False
        logger.info("开始清理语音识别资源")
        await handler.disconnect_from_xfyun()
        logger.info("语音识别资源清理完成")