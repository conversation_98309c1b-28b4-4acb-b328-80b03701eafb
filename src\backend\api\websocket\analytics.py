from fastapi import WebSocket, WebSocketDisconnect
import json
import uuid
from config.logging import logger
from backend.services import ConnectionManager
from agents import AnalyticsAgent


async def analytics_websocket_endpoint(websocket: WebSocket):
    """数据分析智能体专用WebSocket端点"""
    client_id = str(uuid.uuid4())
    
    connection_manager: ConnectionManager = websocket.app.state.connection_manager
    
    await connection_manager.connect(websocket, client_id)
    
    try:
        analytics_agent = AnalyticsAgent()
        
        await connection_manager.send_json(client_id, {
            "type": "connected",
            "client_id": client_id,
            "agent": "analytics",
            "message": "欢迎使用数据分析智能体！我可以帮助您进行生产数据分析、质量预测和异常检测。"
        })
        
        while True:
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                message_type = message.get("type", "query")
                
                if message_type == "analyze":
                    analysis_type = message.get("analysis_type", "general")
                    data_params = message.get("params", {})
                    
                    await connection_manager.send_json(client_id, {
                        "type": "processing",
                        "agent": "analytics",
                        "message": f"正在进行{analysis_type}分析..."
                    })
                    
                    if analysis_type == "quality_prediction":
                        result = await analytics_agent.predict_quality(data_params)
                    elif analysis_type == "anomaly_detection":
                        result = await analytics_agent.detect_anomalies(data_params)
                    elif analysis_type == "correlation":
                        result = await analytics_agent.analyze_correlation(data_params)
                    elif analysis_type == "trend":
                        result = await analytics_agent.analyze_trend(data_params)
                    else:
                        result = await analytics_agent.general_analysis(data_params)
                    
                    await connection_manager.send_json(client_id, {
                        "type": "analysis_result",
                        "agent": "analytics",
                        "analysis_type": analysis_type,
                        "result": result,
                        "status": "completed"
                    })
                    
                elif message_type == "report":
                    report_type = message.get("report_type", "daily")
                    report_params = message.get("params", {})
                    
                    await connection_manager.send_json(client_id, {
                        "type": "processing",
                        "agent": "analytics",
                        "message": f"正在生成{report_type}报告..."
                    })
                    
                    report = await analytics_agent.generate_report(report_type, report_params)
                    
                    await connection_manager.send_json(client_id, {
                        "type": "report_result",
                        "agent": "analytics",
                        "report_type": report_type,
                        "report": report,
                        "status": "completed"
                    })
                    
                elif message_type == "realtime_monitor":
                    metrics = message.get("metrics", [])
                    interval = message.get("interval", 5)
                    
                    await connection_manager.send_json(client_id, {
                        "type": "monitor_started",
                        "agent": "analytics",
                        "metrics": metrics,
                        "interval": interval
                    })
                    
                    async for metrics_data in analytics_agent.monitor_metrics(metrics, interval):
                        await connection_manager.send_json(client_id, {
                            "type": "metrics_update",
                            "agent": "analytics",
                            "data": metrics_data
                        })
                        
                elif message_type == "ping":
                    await connection_manager.send_json(client_id, {"type": "pong", "agent": "analytics"})
                    
            except json.JSONDecodeError:
                await connection_manager.send_json(client_id, {
                    "type": "error",
                    "agent": "analytics",
                    "message": "无效的消息格式"
                })
                    
    except WebSocketDisconnect:
        logger.info(f"Analytics agent client {client_id} disconnected")
        connection_manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"Analytics WebSocket error for client {client_id}: {e}")
        connection_manager.disconnect(client_id)
        await websocket.close(code=1011, reason="Server error")