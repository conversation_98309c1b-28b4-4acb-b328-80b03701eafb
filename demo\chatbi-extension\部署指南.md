# ChatBI 部署指南

## 目录结构
```
chatbi-extension/
├── extension/          # Chrome 扩展程序
│   ├── manifest.json
│   ├── background.js
│   ├── content.js
│   ├── styles.css
│   └── icons/         # 图标文件夹
├── backend/           # 后端服务器
│   ├── package.json
│   └── server.js
├── API文档.md
└── 部署指南.md
```

## 部署步骤

### 1. 后端部署

#### 本地运行
```bash
# 进入后端目录
cd chatbi-extension/backend

# 安装依赖
npm install

# 启动服务器
npm start
```

服务器将在 `http://localhost:8080` 启动，WebSocket 端点为 `ws://localhost:8080/ws`。

#### 生产环境部署
可以使用 PM2、Docker 或云服务：

**使用 PM2：**
```bash
npm install -g pm2
pm2 start server.js --name chatbi-backend
pm2 save
pm2 startup
```

**使用 Docker：**
```dockerfile
FROM node:18
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 8080
CMD ["node", "server.js"]
```

### 2. Chrome 扩展安装

#### 开发者模式安装（推荐）

1. **生成图标文件**
   - 打开 `extension/icons/icon-generator.html` 在浏览器中
   - 打开控制台查看生成的 base64 图标数据
   - 使用在线工具将 base64 转换为 PNG 文件
   - 保存为 `icon-16.png`、`icon-48.png`、`icon-128.png`

2. **加载扩展**
   - 打开 Chrome 浏览器
   - 访问 `chrome://extensions/`
   - 开启右上角的"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `chatbi-extension/extension` 文件夹

3. **验证安装**
   - 扩展图标应出现在浏览器工具栏
   - 访问任意网页，右下角应显示悬浮图标

#### 打包发布（可选）

1. 在 `chrome://extensions/` 页面点击"打包扩展程序"
2. 选择扩展根目录
3. 生成 `.crx` 文件用于分发

### 3. 配置说明

#### 修改 WebSocket 地址
如果后端部署在其他地址，修改 `content.js` 中的：
```javascript
ws = new WebSocket('ws://your-server-address:8080/ws');
```

#### 自定义样式
编辑 `styles.css` 文件中的 CSS 变量：
```css
:root {
  --chatbi-primary: #5c6bc0;     /* 主题色 */
  --chatbi-bg: #ffffff;          /* 背景色 */
  /* ... 其他变量 */
}
```

### 4. 替代方案

如果觉得浏览器扩展部署麻烦，可以考虑：

#### 方案一：Web 应用
将前端代码改造为独立的 Web 应用：
1. 创建 `index.html` 文件
2. 将 `content.js` 和 `styles.css` 引入
3. 去除 Chrome 扩展相关代码
4. 部署到任意 Web 服务器

#### 方案二：书签小工具
创建一个书签小工具（Bookmarklet）：
```javascript
javascript:(function(){
  const script = document.createElement('script');
  script.src = 'https://your-server.com/chatbi.js';
  document.body.appendChild(script);
})();
```

#### 方案三：用户脚本
使用 Tampermonkey 等用户脚本管理器：
1. 安装 Tampermonkey 扩展
2. 创建新脚本，复制 `content.js` 内容
3. 添加用户脚本头部信息

### 5. 常见问题

**Q: 连接 WebSocket 失败**
- 检查后端服务是否运行
- 确认防火墙允许 8080 端口
- 检查 WebSocket 地址是否正确

**Q: 语音输入不工作**
- 确保使用 HTTPS 或 localhost
- 检查浏览器麦克风权限
- 某些浏览器可能不支持 Web Speech API

**Q: 扩展图标不显示**
- 确保图标文件存在且路径正确
- 重新加载扩展
- 检查控制台错误信息

### 6. 安全建议

1. **生产环境使用 WSS**
   ```javascript
   ws = new WebSocket('wss://your-domain.com/ws');
   ```

2. **添加身份验证**
   在 WebSocket 连接时传递 token：
   ```javascript
   ws = new WebSocket('ws://localhost:8080/ws?token=your-auth-token');
   ```

3. **限制 CORS**
   在后端配置具体的允许域名：
   ```javascript
   app.use(cors({
     origin: 'https://your-domain.com'
   }));
   ```

### 7. 性能优化

1. **启用消息压缩**
   WebSocket 支持 permessage-deflate 扩展

2. **限制消息频率**
   实现消息节流和防抖

3. **优化重连策略**
   使用指数退避算法

## 快速开始命令汇总

```bash
# 克隆或下载项目后
cd chatbi-extension

# 启动后端
cd backend
npm install
npm start

# 新开终端，生成图标（可选）
# 打开 extension/icons/icon-generator.html 手动生成

# 在 Chrome 中加载扩展
# 1. 打开 chrome://extensions/
# 2. 开启开发者模式
# 3. 加载 extension 文件夹
```

完成以上步骤后，访问任意网页即可看到右下角的 ChatBI 悬浮图标！