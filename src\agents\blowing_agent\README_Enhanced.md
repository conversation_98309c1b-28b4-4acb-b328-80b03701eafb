# 吹炼智能体增强功能说明

## 概述

吹炼智能体已经按照您的需求进行了精简和优化，专注于两个核心功能：

1. **实时监测功能**：每 30 秒查询数据库，分析炉况并给出建议
2. **阶段播报功能**：接收阶段通知，生成专业播报内容（第一阶段播报初始数据，后续阶段播报参数和建议）

## 新增功能详解

### 1. 实时监测功能

#### 功能描述

- 每 30 秒自动查询 `llmproduct1.blowing_information` 数据表
- 获取当前炉次号（通过最新记录的 ID 字段）
- 分析关键参数变化趋势
- 检查加料计划并提醒
- 调用大模型生成专业分析和建议

#### 监测参数

- **氧气流量** (o2_flowrate): Nm³/min
- **枪位高度** (gun_height): mm
- **累计氧量** (o2_accum): Nm³
- **吹炼压力** (blow_pres): MPa
- **喷溅信息** (pj_time, pj_level, pj_dura): 时间、等级、持续时间
- **火焰颜色** (fire_color): 炉况重要指标
- **炉渣泡沫高度** (lzpm_height): m
- **炉底气体** (ld_gas_type, ld_gas_flow, ld_gas_pres): 类型、流量、压力

#### 加料计划管理

- 自动解析 `rjzzl_batch` 字段中的 JSON 格式加料计划
- 根据 `blowtime` 字段判断当前吹炼进度
- 在接近加料时间时自动提醒操作人员
- 支持多批次、多材料的复杂加料计划

### 2. 阶段播报功能

#### 功能描述

- 接收外部系统发送的阶段开始信号
- 获取当前炉况数据
- 调用大模型生成专业播报内容
- 包含时间点、阶段信息、参数状态、操作建议

#### 播报内容

**第一阶段播报**（吹炼开始）：

- 当前时间点和炉次信息
- 完整的初始状态数据（铁水温度、重量、化学成分、废钢信息等）
- 计划加料内容和时间安排
- 不包含操作建议，仅播报现状和计划

**后续阶段播报**：

- 当前时间点和吹炼进度
- 阶段开始的正式播报
- 关键参数状态总结
- 该阶段的重点关注事项
- 具体操作建议和注意事项

## 使用方法

### WebSocket 接口

#### 启动实时监测

```json
{
  "type": "start_monitoring"
}
```

#### 停止实时监测

```json
{
  "type": "stop_monitoring"
}
```

#### 发送阶段通知

```json
{
  "type": "stage_notification",
  "stage_info": {
    "stage_number": 2,
    "stage_name": "脱碳阶段"
  }
}
```

### 编程接口

#### 创建和初始化智能体

```python
from agents.blowing_agent import BlowingAgent

# 创建智能体实例
agent = BlowingAgent()

# 初始化（包含数据库连接）
await agent.initialize()
```

#### 启动实时监测

```python
# 启动监测
await agent.start_real_time_monitoring()

# 停止监测
await agent.stop_real_time_monitoring()
```

#### 处理阶段通知

```python
stage_info = {
    "stage_number": 2,
    "stage_name": "脱碳阶段"
}

result = await agent.handle_stage_notification(stage_info)
```

#### 清理资源

```python
# 清理数据库连接等资源
await agent.cleanup()
```

## 数据库配置

确保 `src/config/settings.py` 中的数据库配置正确：

```python
DB_HOST: str = "***************"
DB_PORT: str = "3416"
DB_USERNAME: str = "root"
DB_PASSWORD: str = "jishu_2023"
DB_NAME: str = "llmproduct1"
```

## 测试方法

运行测试脚本：

```bash
cd src/agents/blowing_agent
python test_enhanced_agent.py
```

## 日志监控

所有关键操作都会记录详细日志：

- 数据库连接状态
- 监测循环执行情况
- 参数分析结果
- 大模型调用记录
- 错误和异常信息

查看日志以监控系统运行状态。

## 注意事项

1. **数据库连接**：确保数据库服务正常，网络连接稳定
2. **大模型服务**：确保 LLM 服务可用，避免调用失败
3. **资源管理**：使用完毕后调用 `cleanup()` 方法清理资源
4. **异常处理**：系统具备自动重试和错误恢复机制
5. **性能考虑**：监测频率为 10 秒，可根据需要调整

## 扩展功能

系统设计具有良好的扩展性，可以轻松添加：

- 更多监测参数
- 自定义报警规则
- 历史数据分析
- 预测模型集成
- 多炉次并行监测
