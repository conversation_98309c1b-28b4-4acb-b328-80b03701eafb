# 语音识别功能说明

## 概述
本项目已将语音识别功能从 Chrome 的 webkitSpeechRecognition API（需要翻墙）替换为科大讯飞的语音识别服务（不需要翻墙）。

## 使用方法
1. 点击聊天窗口中的麦克风按钮 🎤 开始录音
2. 说话完毕后，再次点击按钮停止录音
3. 系统会自动将语音转换为文字并填入输入框
4. 最长录音时间为 60 秒，超时会自动停止

## 技术实现
- **前端**：使用 Web Audio API 录制 16kHz 16bit PCM 格式音频
- **后端**：通过 WebSocket 接收音频数据，调用科大讯飞 API 进行识别
- **通信**：使用 WebSocket 进行实时通信

## 注意事项
1. 首次使用需要允许浏览器访问麦克风
2. 确保后端服务已启动（默认端口 8080）
3. 科大讯飞 API 密钥已配置在 server.py 中
4. 录音时请保持环境相对安静以获得更好的识别效果

## 故障排除
- 如果提示"无法访问麦克风"，请检查浏览器权限设置
- 如果提示"语音识别连接错误"，请确保后端服务正在运行
- 如果识别结果不准确，请尝试：
  - 说话时靠近麦克风
  - 减少背景噪音
  - 使用标准普通话