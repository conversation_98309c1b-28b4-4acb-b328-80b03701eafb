from fastapi import APIRouter, status
from pydantic import BaseModel
from datetime import datetime
import psutil


router = APIRouter()


class HealthStatus(BaseModel):
    status: str
    timestamp: datetime
    version: str = "1.0.0"
    
    
class SystemInfo(BaseModel):
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    gpu_available: bool
    gpu_memory_used: float = 0.0
    gpu_memory_total: float = 0.0


@router.get("/", response_model=HealthStatus)
async def health_check():
    """健康检查"""
    return HealthStatus(
        status="healthy",
        timestamp=datetime.utcnow()
    )


@router.get("/ready")
async def readiness_check():
    """就绪检查"""
    # 检查模型是否加载
    # 检查数据库连接
    # 检查其他依赖服务
    return {"ready": True}


@router.get("/system", response_model=SystemInfo)
async def system_info():
    """系统信息"""
    info = SystemInfo(
        cpu_percent=psutil.cpu_percent(interval=1),
        memory_percent=psutil.virtual_memory().percent,
        disk_percent=psutil.disk_usage('/').percent
    )
    
    return info