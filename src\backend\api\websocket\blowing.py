from fastapi import WebSocket, WebSocketDisconnect
import json
import uuid
import asyncio
import re
from config.logging import logger
from backend.services import ConnectionManager, WebSocketStreamingCallbackHandler
from agents import BlowingAgent


def format_content_for_display(content: str) -> str:
    """格式化内容，将 Markdown 格式转换为适合前端显示的格式"""
    if not content:
        return content

    # 处理表格（检测竖杠分隔的表格）
    original_content = content
    content = format_tables(content)

    # 调试日志
    if '<table' in content and '<table' not in original_content:
        logger.debug(f"Table detected and converted in content")
    elif '|' in original_content and '<table' not in content:
        logger.debug(f"Pipe characters found but no table created")

    # 处理标题
    content = re.sub(r'^### (.*?)$',
                     r'<h3>\1</h3>',
                     content,
                     flags=re.MULTILINE)
    content = re.sub(r'^## (.*?)$',
                     r'<h2>\1</h2>',
                     content,
                     flags=re.MULTILINE)
    content = re.sub(r'^# (.*?)$', r'<h1>\1</h1>', content, flags=re.MULTILINE)

    # 处理加粗
    content = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', content)

    # 处理分隔线
    content = re.sub(r'^---+$', r'<hr>', content, flags=re.MULTILINE)

    # 处理列表项
    content = re.sub(r'^- (.*?)$', r'<li>\1</li>', content, flags=re.MULTILINE)

    # 包装连续的列表项
    lines = content.split('\n')
    formatted_lines = []
    in_list = False

    for line in lines:
        if line.strip().startswith('<li>'):
            if not in_list:
                formatted_lines.append('<ul>')
                in_list = True
            formatted_lines.append(line)
        else:
            if in_list:
                formatted_lines.append('</ul>')
                in_list = False
            formatted_lines.append(line)

    if in_list:
        formatted_lines.append('</ul>')

    content = '\n'.join(formatted_lines)

    # 处理段落间距
    content = re.sub(r'\n\n+', '<div class="paragraph-break"></div>', content)
    content = re.sub(r'\n', '<br>', content)

    return content


def format_tables(content: str) -> str:
    """格式化表格内容"""
    lines = content.split('\n')
    formatted_lines = []
    i = 0

    while i < len(lines):
        line = lines[i].strip()

        # 更严格的表格检测：必须有至少3个竖杠，且不能是单独的竖杠
        if ('|' in line and line.count('|') >= 3
                and not line.replace('|', '').replace(' ', '') == ''
                and len([cell
                         for cell in line.split('|') if cell.strip()]) >= 2):

            # 开始处理表格
            table_lines = []

            # 收集表格行
            while (i < len(lines) and '|' in lines[i]
                   and lines[i].count('|') >= 3 and len([
                       cell for cell in lines[i].split('|') if cell.strip()
                   ]) >= 2):
                table_lines.append(lines[i].strip())
                i += 1

            # 只有当收集到多行时才转换为表格
            if len(table_lines) >= 2:
                html_table = convert_to_html_table(table_lines)
                formatted_lines.append(html_table)
            else:
                # 如果只有一行，当作普通文本处理
                for table_line in table_lines:
                    formatted_lines.append(table_line)

            i -= 1  # 回退一行，因为外层循环会自增
        else:
            formatted_lines.append(lines[i])

        i += 1

    return '\n'.join(formatted_lines)


def convert_to_html_table(table_lines: list) -> str:
    """将表格行转换为HTML表格"""
    if not table_lines or len(table_lines) < 2:
        return ""

    # 验证所有行都有相同数量的列
    first_row_cols = len(
        [cell.strip() for cell in table_lines[0].split('|') if cell.strip()])
    if first_row_cols < 2:
        return ""

    # 检查其他行是否有相似的列数
    valid_table = True
    for line in table_lines[1:]:
        cols = len([cell.strip() for cell in line.split('|') if cell.strip()])
        if abs(cols - first_row_cols) > 1:  # 允许1列的差异
            valid_table = False
            break

    if not valid_table:
        return ""

    html = '<table class="data-table">'

    for i, line in enumerate(table_lines):
        # 清理行并分割
        cells = [cell.strip() for cell in line.split('|') if cell.strip()]

        if not cells:
            continue

        # 第一行作为表头
        if i == 0:
            html += '<thead><tr>'
            for cell in cells:
                html += f'<th>{cell}</th>'
            html += '</tr></thead><tbody>'
        else:
            html += '<tr>'
            for cell in cells:
                html += f'<td>{cell}</td>'
            html += '</tr>'

    html += '</tbody></table>'
    return html


async def format_integrated_monitoring_report(monitoring_report: dict) -> str:
    """格式化新的集成监控报告"""
    try:
        # 获取报告各部分数据
        current_stage = monitoring_report.get("current_stage", {})
        parameters_analysis = monitoring_report.get("parameters_analysis", {})
        risk_and_suggestions = monitoring_report.get("risk_and_suggestions",
                                                     {})

        # 构建报告内容
        report_lines = []

        # 1. 当前阶段播报
        stage_name = current_stage.get("stage_name", "未知阶段")
        stage_description = current_stage.get("stage_description", "")
        stage_changed = current_stage.get("stage_changed", False)

        report_lines.append("## 🔥 吹炼监控报告")
        report_lines.append(
            f"**时间**: {monitoring_report.get('timestamp', '')}")
        report_lines.append(f"**炉次号**: {monitoring_report.get('heat_id', '')}")
        report_lines.append(
            f"**吹炼时间**: 第{monitoring_report.get('blow_time', '0')}分钟")
        report_lines.append("")

        # 阶段播报
        if stage_changed:
            report_lines.append("### 📢 阶段播报")
            report_lines.append(f"**{stage_description}**")
        else:
            report_lines.append("### 📍 当前阶段")
            report_lines.append(f"**{stage_name}**: {stage_description}")
        report_lines.append("")

        # 2. 参数状态与趋势分析
        report_lines.append("### 📊 参数状态与趋势分析")
        parameters_table = parameters_analysis.get("parameters_table", {})
        analysis_type = parameters_analysis.get("analysis_type", "")

        if parameters_table:
            if analysis_type == "preparation_data":
                # 准备阶段数据表格
                report_lines.append("| 参数名称 | 当前值 |")
                report_lines.append("|---------|--------|")
                for param_name, param_data in parameters_table.items():
                    current_value = param_data.get("current_value", "-")
                    report_lines.append(f"| {param_name} | {current_value} |")
            else:
                # 时序数据表格
                report_lines.append("| 参数名称 | 当前值 | 变化值 | 状态评估 |")
                report_lines.append("|---------|--------|--------|----------|")
                for param_name, param_data in parameters_table.items():
                    current_value = param_data.get("current_value", "-")
                    change_value = param_data.get("change_value", "-")
                    status = param_data.get("status", "-")
                    status_emoji = "✅" if status == "正常" else "⚠️"
                    # 确保表格行格式正确，避免内容过长导致格式问题
                    table_row = f"| {param_name} | {current_value} | {change_value} | {status_emoji} {status} |"
                    report_lines.append(table_row)
        else:
            report_lines.append("暂无参数数据")
        report_lines.append("")

        # 3. 风险提示与操作建议
        report_lines.append("### ⚠️ 风险提示与操作建议")

        status = risk_and_suggestions.get("status", "normal")
        message = risk_and_suggestions.get("message", "")
        risks = risk_and_suggestions.get("risks", [])
        suggestions = risk_and_suggestions.get("suggestions", [])
        material_reminders = risk_and_suggestions.get("material_reminders", [])

        if status == "normal":
            report_lines.append(f"✅ **{message}**")
        else:
            report_lines.append(f"⚠️ **{message}**")

        if risks:
            report_lines.append("")
            report_lines.append("**风险提示**:")
            for risk in risks:
                report_lines.append(f"• ⚠️ {risk}")

        if suggestions:
            report_lines.append("")
            report_lines.append("**操作建议**:")
            for suggestion in suggestions:
                report_lines.append(f"• 💡 {suggestion}")

        if material_reminders:
            report_lines.append("")
            report_lines.append("**加料提醒**:")
            for reminder in material_reminders:
                report_lines.append(f"• 🔔 {reminder}")

        report_lines.append("")
        report_lines.append("---")

        return "\n".join(report_lines)

    except Exception as e:
        logger.error(f"Error formatting integrated monitoring report: {e}")
        return f"监控报告格式化失败: {str(e)}"


async def send_monitoring_report_stream(connection_manager, client_id: str,
                                        monitoring_report: dict):
    """流式发送新格式的监控报告到前端"""
    try:
        # 发送监控开始信号
        await connection_manager.send_json(
            client_id, {
                "type": "monitoring_stream_start",
                "agent": "blowing",
                "timestamp": monitoring_report.get("timestamp"),
                "heat_id": monitoring_report.get("heat_id"),
                "blow_time": monitoring_report.get("blow_time")
            })

        # 构建完整的监控报告内容
        report_content = await format_integrated_monitoring_report(
            monitoring_report)

        if report_content:
            # 格式化内容
            formatted_content = format_content_for_display(report_content)

            # 分段发送内容（模拟流式输出）
            chunks = split_content_into_chunks(formatted_content,
                                               chunk_size=100)

            for i, chunk in enumerate(chunks):
                try:
                    await connection_manager.send_json(
                        client_id, {
                            "type": "monitoring_stream",
                            "agent": "blowing",
                            "content": chunk,
                            "chunk_index": i,
                            "total_chunks": len(chunks),
                            "is_final": i == len(chunks) - 1
                        })
                    # 添加延迟实现真正的流式输出
                    await asyncio.sleep(0.3)
                except Exception as chunk_error:
                    logger.error(f"Error sending chunk {i}: {chunk_error}")
                    # 如果某个块发送失败，尝试发送剩余内容
                    remaining_content = ''.join(chunks[i:])
                    await connection_manager.send_json(
                        client_id, {
                            "type": "monitoring_stream",
                            "agent": "blowing",
                            "content": remaining_content,
                            "chunk_index": i,
                            "total_chunks": len(chunks),
                            "is_final": True
                        })
                    break

        # 发送监控结束信号
        await connection_manager.send_json(
            client_id, {
                "type": "monitoring_stream_end",
                "agent": "blowing",
                "report": monitoring_report
            })

    except Exception as e:
        logger.error(f"Error in send_monitoring_report_stream: {e}")
        await connection_manager.send_json(client_id, {
            "type": "error",
            "agent": "blowing",
            "message": f"监控报告发送失败: {str(e)}"
        })


def split_content_into_chunks(content: str, chunk_size: int = 80) -> list:
    """将内容分割成小块用于流式输出"""
    if not content:
        return []

    # 按HTML标签和句子分割，保持完整性
    # 先按HTML标签分割
    html_parts = re.split(r'(<[^>]+>)', content)
    chunks = []
    current_chunk = ""

    for part in html_parts:
        if part.startswith('<') and part.endswith('>'):
            # HTML标签直接添加
            current_chunk += part
        else:
            # 文本内容按句子分割
            if not part.strip():
                current_chunk += part
                continue

            sentences = re.split(r'(?<=[。！？；：])\s*', part)
            for sentence in sentences:
                if len(current_chunk + sentence) <= chunk_size:
                    current_chunk += sentence
                else:
                    if current_chunk.strip():
                        chunks.append(current_chunk)
                    current_chunk = sentence

    if current_chunk.strip():
        chunks.append(current_chunk)

    return chunks if chunks else [content]


async def blowing_websocket_endpoint(websocket: WebSocket):
    """吹炼控制智能体专用WebSocket端点"""
    client_id = str(uuid.uuid4())

    connection_manager: ConnectionManager = websocket.app.state.connection_manager

    await connection_manager.connect(websocket, client_id)

    # 状态管理
    monitoring_active = False
    current_stage = None

    try:
        # 创建并初始化吹炼智能体
        blowing_agent = BlowingAgent()
        await blowing_agent.initialize()

        # 设置监控报告回调函数
        async def monitoring_callback(monitoring_report):
            """监控报告回调函数，将监控结果发送到前端"""
            try:
                logger.info(
                    f"Sending monitoring report to client {client_id}, type: {monitoring_report.get('report_type', 'unknown')}"
                )
                # 发送监控报告到前端
                await connection_manager.send_json(
                    client_id,
                    {
                        "type": "monitoring_report",
                        **monitoring_report  # 展开所有报告内容
                    })
                logger.info(
                    f"Monitoring report sent successfully to client {client_id}"
                )
            except Exception as e:
                logger.error(f"Error sending monitoring report: {e}")

        blowing_agent.set_monitoring_callback(monitoring_callback)

        await connection_manager.send_json(
            client_id, {
                "type": "connected",
                "client_id": client_id,
                "agent": "blowing",
                "message": "欢迎使用吹炼智能体！支持实时监控和阶段自动判断功能。"
            })

        while True:
            data = await websocket.receive_text()

            try:
                message = json.loads(data)
                message_type = message.get("type", "query")

                if message_type == "exit":
                    await connection_manager.send_json(
                        client_id, {
                            "type": "info",
                            "agent": "blowing",
                            "message": "感谢使用吹炼智能体，再见！"
                        })
                    break

                elif message_type == "start_process":
                    # 开始吹炼过程：启动阶段播报和实时监控
                    start_time = message.get("start_time")  # 可选的开始时间

                    logger.info(
                        f"Starting blowing process for client {client_id}")

                    # 启动实时监控
                    monitor_result = await blowing_agent.real_time_monitoring_function(
                        action="start", start_time=start_time)

                    if monitor_result.get("success"):
                        monitoring_active = True
                        await connection_manager.send_json(
                            client_id, {
                                "type":
                                "process_started",
                                "agent":
                                "blowing",
                                "message":
                                "吹炼过程已启动，实时监控开始运行",
                                "monitoring_start_time":
                                monitor_result.get("start_time"),
                                "monitoring_interval":
                                "60秒"
                            })
                    else:
                        await connection_manager.send_json(
                            client_id, {
                                "type":
                                "error",
                                "agent":
                                "blowing",
                                "message":
                                f"启动吹炼过程失败: {monitor_result.get('error')}"
                            })

                elif message_type == "stop_process":
                    # 停止吹炼过程：停止阶段播报和实时监控
                    logger.info(
                        f"Stopping blowing process for client {client_id}")

                    # 停止实时监控
                    stop_result = await blowing_agent.real_time_monitoring_function(
                        action="stop")

                    if stop_result.get("success"):
                        monitoring_active = False
                        current_stage = None
                        await connection_manager.send_json(
                            client_id, {
                                "type": "process_stopped",
                                "agent": "blowing",
                                "message": "吹炼过程已停止，实时监控已结束"
                            })
                    else:
                        await connection_manager.send_json(
                            client_id, {
                                "type": "error",
                                "agent": "blowing",
                                "message":
                                f"停止吹炼过程失败: {stop_result.get('error')}"
                            })

                elif message_type == "status":
                    # 查询当前状态
                    await connection_manager.send_json(
                        client_id, {
                            "type": "status_response",
                            "agent": "blowing",
                            "monitoring_active": monitoring_active,
                            "current_stage": current_stage
                        })

                elif message_type == "ping":
                    # 响应心跳
                    await connection_manager.send_json(client_id, {
                        "type": "pong",
                        "agent": "blowing"
                    })

                else:
                    # 未知消息类型
                    await connection_manager.send_json(
                        client_id, {
                            "type": "error",
                            "agent": "blowing",
                            "message": f"未知的消息类型: {message_type}"
                        })

            except json.JSONDecodeError:
                # 如果不是有效的JSON格式，返回错误
                await connection_manager.send_json(
                    client_id, {
                        "type": "error",
                        "agent": "blowing",
                        "message": "消息格式错误，请发送有效的JSON格式消息"
                    })

    except WebSocketDisconnect:
        logger.info(f"Blowing agent client {client_id} disconnected")
        # 清理智能体资源
        if 'blowing_agent' in locals():
            try:
                # 停止监控
                if monitoring_active:
                    await blowing_agent.real_time_monitoring_function(
                        action="stop")
                # 清理资源
                await blowing_agent.cleanup()
            except Exception as cleanup_error:
                logger.error(f"Error during cleanup: {cleanup_error}")
        connection_manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"Blowing WebSocket error for client {client_id}: {e}")
        # 清理智能体资源
        if 'blowing_agent' in locals():
            try:
                # 停止监控
                if monitoring_active:
                    await blowing_agent.real_time_monitoring_function(
                        action="stop")
                # 清理资源
                await blowing_agent.cleanup()
            except Exception as cleanup_error:
                logger.error(f"Error during cleanup: {cleanup_error}")
        connection_manager.disconnect(client_id)
        await websocket.close(code=1011, reason="Server error")
