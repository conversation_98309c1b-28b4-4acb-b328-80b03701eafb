document.addEventListener('DOMContentLoaded', function() {
  const toggleButton = document.getElementById('toggle-chat');
  const statusElement = document.getElementById('connection-status');
  
  // 检查服务器连接状态
  checkServerConnection();
  
  // 绑定按钮事件
  toggleButton.addEventListener('click', function() {
    // 向当前活动标签页发送消息
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {action: 'toggleChat'});
      window.close(); // 关闭弹出窗口
    });
  });
  
  // 检查服务器连接状态
  function checkServerConnection() {
    const ws = new WebSocket('ws://localhost:9001/ws/agent/blowing');
    
    const timeout = setTimeout(() => {
      updateStatus('disconnected', '❌ 服务器未连接');
      ws.close();
    }, 3000);
    
    ws.onopen = function() {
      clearTimeout(timeout);
      updateStatus('connected', '✅ 服务器已连接');
      ws.close();
    };
    
    ws.onerror = function() {
      clearTimeout(timeout);
      updateStatus('disconnected', '❌ 连接失败');
    };
    
    ws.onclose = function() {
      // 连接已关闭，不需要额外处理
    };
  }
  
  function updateStatus(status, message) {
    statusElement.className = `status ${status}`;
    statusElement.textContent = message;
  }
});
