let chatWindow = null;
let floatingIcon = null;
let ws = null;
let messages = [];
let currentStreamingMessage = null;
let currentStreamingElement = null;
let isStreaming = false;
let streamingTimeout = null;
let monitoringActive = false;
let currentStage = null;

function createFloatingIcon() {
  floatingIcon = document.createElement("div");
  floatingIcon.id = "chatbi-floating-icon";
  floatingIcon.innerHTML = `
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z" fill="currentColor"/>
      <path d="M12 6C8.69 6 6 8.69 6 12C6 15.31 8.69 18 12 18C15.31 18 18 15.31 18 12C18 8.69 15.31 6 12 6ZM12 16.5C9.52 16.5 7.5 14.48 7.5 12C7.5 9.52 9.52 7.5 12 7.5C14.48 7.5 16.5 9.52 16.5 12C16.5 14.48 14.48 16.5 12 16.5Z" fill="currentColor"/>
      <circle cx="12" cy="12" r="1.5" fill="currentColor"/>
    </svg>
  `;
  floatingIcon.addEventListener("click", toggleChatWindow);
  document.body.appendChild(floatingIcon);
}

function createChatWindow() {
  chatWindow = document.createElement("div");
  chatWindow.id = "chatbi-window";
  chatWindow.className = "chatbi-hidden";
  chatWindow.innerHTML = `
    <div class="chatbi-header">
      <h3>🔥 吹炼智能体</h3>
      <div class="chatbi-status">
        <span id="monitoring-status" class="status-indicator ${
          monitoringActive ? "active" : ""
        }">
          ${monitoringActive ? "🟢 监控中" : "🔴 已停止"}
        </span>
      </div>
      <button class="chatbi-close" aria-label="Close">&times;</button>
    </div>
    <div class="chatbi-messages" id="chatbi-messages"></div>
    <div class="chatbi-control-panel">
      <div class="chatbi-process-controls">
        <button id="start-process" class="chatbi-control-btn primary" ${
          monitoringActive ? "disabled" : ""
        }>
          🚀 启动吹炼过程
        </button>
        <button id="stop-process" class="chatbi-control-btn danger" ${
          !monitoringActive ? "disabled" : ""
        }>
          🛑 停止吹炼过程
        </button>
      </div>
      <div class="chatbi-time-input">
        <h4>监控时间设置</h4>
        <label for="monitoring-start-time">监控开始时间 (可选):</label>
        <input type="datetime-local" id="monitoring-start-time" placeholder="留空使用当前时间">
        <small>如不设置，将从当前时间开始监控</small>
      </div>
    </div>
  `;
  document.body.appendChild(chatWindow);

  // 绑定事件监听器
  const closeBtn = chatWindow.querySelector(".chatbi-close");
  const startBtn = chatWindow.querySelector("#start-process");
  const stopBtn = chatWindow.querySelector("#stop-process");

  closeBtn.addEventListener("click", toggleChatWindow);
  startBtn.addEventListener("click", startBlowingProcess);
  stopBtn.addEventListener("click", stopBlowingProcess);

  connectWebSocket();
}

function toggleChatWindow() {
  if (!chatWindow) {
    createChatWindow();
  }
  chatWindow.classList.toggle("chatbi-hidden");

  if (!chatWindow.classList.contains("chatbi-hidden")) {
    floatingIcon.style.display = "none";
    // 查询当前状态
    queryStatus();
  } else {
    floatingIcon.style.display = "flex";
  }
}

function connectWebSocket() {
  ws = new WebSocket("ws://localhost:9001/ws/agent/blowing");

  ws.onopen = () => {
    console.log("WebSocket connected to blowing agent");
    addMessage("system", "✅ 已连接到吹炼智能体");
  };

  ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    handleWebSocketMessage(data);
  };

  ws.onerror = (error) => {
    console.error("WebSocket error:", error);
    addMessage("system", "❌ 连接错误，请稍后重试");
  };

  ws.onclose = () => {
    console.log("WebSocket disconnected");
    addMessage("system", "🔌 连接已断开，正在重连...");
    setTimeout(connectWebSocket, 3000);
  };
}

function handleWebSocketMessage(data) {
  switch (data.type) {
    case "connected":
      addMessage("system", `🎉 ${data.message}`);
      break;

    case "process_started":
      monitoringActive = true;
      updateUI();
      addMessage("system", `🚀 ${data.message}`);
      addMessage("system", `⏰ 监控开始时间: ${data.monitoring_start_time}`);
      addMessage("system", `🔄 监控间隔: ${data.monitoring_interval}`);
      break;

    case "process_stopped":
      monitoringActive = false;
      currentStage = null;
      updateUI();
      addMessage("system", `🛑 ${data.message}`);
      break;

    case "monitoring_stream_start":
      // 监控报告流式输出开始
      currentStreamingMessage = {
        role: "monitoring",
        content: `📊 实时监控报告 (第${data.blow_time}分钟)\n\n`,
        timestamp: new Date().toISOString(),
        heat_id: data.heat_id,
        blow_time: data.blow_time,
      };
      messages.push(currentStreamingMessage);
      isStreaming = true;
      startStreamingTimeout();
      renderMessages(true);
      break;

    case "monitoring_stream":
      // 监控报告流式内容
      if (currentStreamingMessage && currentStreamingElement) {
        currentStreamingMessage.content += data.content;
        const contentElement = currentStreamingElement.querySelector(
          ".chatbi-message-content"
        );
        contentElement.innerHTML = formatMessageContent(
          currentStreamingMessage.content
        );

        // 滚动到底部
        const messagesContainer = document.getElementById("chatbi-messages");
        if (messagesContainer) {
          messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 如果是最后一块，确保内容完整
        if (data.is_final) {
          console.log("Monitoring stream completed");
        }
      }
      break;

    case "monitoring_stream_end":
      // 监控报告流式输出结束
      currentStreamingMessage = null;
      currentStreamingElement = null;
      isStreaming = false;
      clearStreamingTimeout();
      break;

    case "status_response":
      monitoringActive = data.monitoring_active;
      currentStage = data.current_stage;
      updateUI();
      break;

    case "error":
      addMessage("error", `❌ 错误: ${data.message}`);
      break;

    case "monitoring_report":
      // 处理监控报告
      handleMonitoringReport(data);
      break;

    case "pong":
      console.log("Received pong from blowing agent");
      break;

    default:
      console.log("Unknown message type:", data.type);
  }
}

function startBlowingProcess() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    addMessage("error", "❌ WebSocket 未连接");
    return;
  }

  // 获取用户设置的监控开始时间
  const monitoringStartTimeInput = document.getElementById(
    "monitoring-start-time"
  );
  const startTime = monitoringStartTimeInput
    ? monitoringStartTimeInput.value
    : null;

  const message = {
    type: "start_process",
  };

  // 如果用户设置了时间，则使用用户设置的时间，否则不传入时间参数（使用当前时间）
  if (startTime) {
    message.start_time = startTime.replace("T", " ") + ":00";
    addMessage("user", `🚀 启动吹炼过程（开始时间: ${message.start_time}）...`);
  } else {
    addMessage("user", "🚀 启动吹炼过程（使用当前时间）...");
  }

  ws.send(JSON.stringify(message));
}

function stopBlowingProcess() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    addMessage("error", "❌ WebSocket 未连接");
    return;
  }

  const message = {
    type: "stop_process",
  };

  ws.send(JSON.stringify(message));
  addMessage("user", "🛑 停止吹炼过程...");
}

function queryStatus() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    return;
  }

  const message = {
    type: "status",
  };

  ws.send(JSON.stringify(message));
}

function updateUI() {
  // 更新状态指示器
  const statusIndicator = document.getElementById("monitoring-status");
  if (statusIndicator) {
    statusIndicator.className = `status-indicator ${
      monitoringActive ? "active" : ""
    }`;
    statusIndicator.textContent = monitoringActive ? "🟢 监控中" : "🔴 已停止";
  }

  // 更新按钮状态
  const startBtn = document.getElementById("start-process");
  const stopBtn = document.getElementById("stop-process");

  if (startBtn) {
    startBtn.disabled = monitoringActive;
  }

  if (stopBtn) {
    stopBtn.disabled = !monitoringActive;
  }

  // 更新阶段按钮状态
  const stageButtons = document.querySelectorAll(".chatbi-stage-btn");
  stageButtons.forEach((btn) => {
    btn.classList.remove("current");
    if (
      currentStage &&
      parseInt(btn.dataset.stage) === currentStage.stage_number
    ) {
      btn.classList.add("current");
    }
  });
}

function addMessage(role, content) {
  messages.push({
    role,
    content,
    timestamp: new Date().toISOString(),
  });
  renderMessages();
}

function renderMessages(isStreaming = false) {
  const messagesContainer = document.getElementById("chatbi-messages");
  if (!messagesContainer) return;

  if (isStreaming && currentStreamingMessage) {
    // 如果是流式输出，只添加新的消息容器
    const msgElement = document.createElement("div");
    msgElement.className = `chatbi-message chatbi-${currentStreamingMessage.role}`;
    msgElement.innerHTML = `
      <div class="chatbi-message-content">${formatMessageContent(
        currentStreamingMessage.content
      )}</div>
      <div class="chatbi-message-time">${new Date(
        currentStreamingMessage.timestamp
      ).toLocaleTimeString()}</div>
    `;
    messagesContainer.appendChild(msgElement);
    currentStreamingElement = msgElement;
  } else if (!isStreaming) {
    // 完整重新渲染
    messagesContainer.innerHTML = messages
      .map(
        (msg) => `
      <div class="chatbi-message chatbi-${msg.role}">
        <div class="chatbi-message-content">${formatMessageContent(
          msg.content
        )}</div>
        <div class="chatbi-message-time">${new Date(
          msg.timestamp
        ).toLocaleTimeString()}</div>
      </div>
    `
      )
      .join("");
  }

  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function handleMonitoringReport(data) {
  console.log("收到监控报告:", data);
  const reportType = data.report_type;
  const priority = data.priority;

  console.log("报告类型:", reportType, "优先级:", priority);

  if (reportType === "emergency_monitoring") {
    // 紧急监控：特殊显示
    console.log("处理紧急监控报告");
    handleEmergencyMonitoringReport(data);
  } else if (reportType === "scheduled_monitoring") {
    // 定时监控：正常显示
    console.log("处理定时监控报告");
    handleScheduledMonitoringReport(data);
  } else {
    console.log("未知的报告类型:", reportType);
  }
}

function handleEmergencyMonitoringReport(data) {
  const eventType = data.event_type;
  const detectionTime = data.event_detection_time;

  // 构建完整的紧急监控报告内容
  let reportContent = `🚨 紧急监控 - ${eventType}事件\n`;
  reportContent += `⏰ 检测时间: ${detectionTime}\n\n`;

  // 显示阶段信息
  if (data.current_stage) {
    reportContent += `📍 当前阶段: ${data.current_stage.stage_name}\n`;
    reportContent += `📢 ${data.current_stage.stage_description}\n\n`;
  }

  // 显示参数分析
  if (data.parameters_analysis) {
    reportContent += "📊 参数状态分析:\n";

    // 处理参数表格数据
    if (data.parameters_analysis.parameters_table) {
      const parametersTable = data.parameters_analysis.parameters_table;
      const analysisType = data.parameters_analysis.analysis_type;

      if (analysisType === "preparation_data") {
        // 准备阶段数据表格
        reportContent += "| 参数名称 | 当前值 |\n";
        reportContent += "|---------|--------|\n";
        for (const [paramName, paramData] of Object.entries(parametersTable)) {
          const currentValue = paramData.current_value || "-";
          reportContent += `| ${paramName} | ${currentValue} |\n`;
        }
      } else {
        // 时序数据表格
        reportContent += "| 参数名称 | 当前值 | 变化值 | 状态评估 |\n";
        reportContent += "|---------|--------|--------|----------|\n";
        for (const [paramName, paramData] of Object.entries(parametersTable)) {
          const currentValue = paramData.current_value || "-";
          const changeValue = paramData.change_value || "-";
          const status = paramData.status || "-";
          const statusEmoji =
            status === "正常" ? "✅" : status === "警告" ? "⚠️" : "❓";
          reportContent += `| ${paramName} | ${currentValue} | ${changeValue} | ${statusEmoji} ${status} |\n`;
        }
      }
      reportContent += "\n";
    } else if (data.parameters_analysis.analysis_result) {
      reportContent += `${data.parameters_analysis.analysis_result}\n\n`;
    } else {
      reportContent += "参数分析数据处理中...\n\n";
    }
  }

  // 显示风险分析
  if (data.risk_and_suggestions) {
    reportContent += "⚠️ 风险分析:\n";
    reportContent += `状态: ${data.risk_and_suggestions.status}\n`;
    reportContent += `说明: ${data.risk_and_suggestions.message}\n`;

    if (
      data.risk_and_suggestions.risks &&
      data.risk_and_suggestions.risks.length > 0
    ) {
      reportContent += "\n🔴 风险点:\n";
      data.risk_and_suggestions.risks.forEach((risk) => {
        reportContent += `  • ${risk}\n`;
      });
    }

    if (
      data.risk_and_suggestions.suggestions &&
      data.risk_and_suggestions.suggestions.length > 0
    ) {
      reportContent += "\n💡 操作建议:\n";
      data.risk_and_suggestions.suggestions.forEach((suggestion) => {
        reportContent += `  • ${suggestion}\n`;
      });
    }
  }

  // 显示加料提醒
  if (
    data.risk_and_suggestions &&
    data.risk_and_suggestions.material_reminders &&
    data.risk_and_suggestions.material_reminders.length > 0
  ) {
    reportContent += "\n📦 加料提醒:\n";
    data.risk_and_suggestions.material_reminders.forEach((reminder) => {
      reportContent += `${reminder}\n`;
    });
  }

  // 将完整的紧急报告作为一条消息发送
  if (reportContent.trim()) {
    addMessage("emergency", reportContent.trim());
  }
}

function handleScheduledMonitoringReport(data) {
  // 正常的定时监控报告处理
  console.log("开始处理定时监控报告，数据:", data);

  // 构建完整的监控报告内容
  let reportContent = "";

  // 显示阶段信息
  if (data.current_stage) {
    console.log("显示阶段信息:", data.current_stage);
    reportContent += `📍 当前阶段: ${data.current_stage.stage_name}\n`;
    reportContent += `📢 ${data.current_stage.stage_description}\n\n`;
  } else {
    console.log("没有阶段信息");
  }

  // 显示参数分析
  if (data.parameters_analysis) {
    console.log("参数分析数据:", data.parameters_analysis);
    reportContent += "📊 参数状态分析:\n";

    // 处理参数表格数据
    if (data.parameters_analysis.parameters_table) {
      const parametersTable = data.parameters_analysis.parameters_table;
      const analysisType = data.parameters_analysis.analysis_type;

      if (analysisType === "preparation_data") {
        // 准备阶段数据表格
        reportContent += "| 参数名称 | 当前值 |\n";
        reportContent += "|---------|--------|\n";
        for (const [paramName, paramData] of Object.entries(parametersTable)) {
          const currentValue = paramData.current_value || "-";
          reportContent += `| ${paramName} | ${currentValue} |\n`;
        }
      } else {
        // 时序数据表格
        reportContent += "| 参数名称 | 当前值 | 变化值 | 状态评估 |\n";
        reportContent += "|---------|--------|--------|----------|\n";
        for (const [paramName, paramData] of Object.entries(parametersTable)) {
          const currentValue = paramData.current_value || "-";
          const changeValue = paramData.change_value || "-";
          const status = paramData.status || "-";
          const statusEmoji =
            status === "正常" ? "✅" : status === "警告" ? "⚠️" : "❓";
          reportContent += `| ${paramName} | ${currentValue} | ${changeValue} | ${statusEmoji} ${status} |\n`;
        }
      }
      reportContent += "\n";
    } else if (data.parameters_analysis.analysis_result) {
      reportContent += `${data.parameters_analysis.analysis_result}\n\n`;
    } else {
      console.log("参数分析结果为空");
      reportContent += "参数分析数据处理中...\n\n";
    }
  }

  // 显示风险分析
  if (data.risk_and_suggestions) {
    reportContent += "⚠️ 风险分析:\n";
    reportContent += `状态: ${data.risk_and_suggestions.status}\n`;
    reportContent += `说明: ${data.risk_and_suggestions.message}\n`;

    if (
      data.risk_and_suggestions.risks &&
      data.risk_and_suggestions.risks.length > 0
    ) {
      reportContent += "\n🔴 风险点:\n";
      data.risk_and_suggestions.risks.forEach((risk) => {
        reportContent += `  • ${risk}\n`;
      });
    }

    if (
      data.risk_and_suggestions.suggestions &&
      data.risk_and_suggestions.suggestions.length > 0
    ) {
      reportContent += "\n💡 操作建议:\n";
      data.risk_and_suggestions.suggestions.forEach((suggestion) => {
        reportContent += `  • ${suggestion}\n`;
      });
    }
  }

  // 显示加料提醒
  if (
    data.risk_and_suggestions &&
    data.risk_and_suggestions.material_reminders &&
    data.risk_and_suggestions.material_reminders.length > 0
  ) {
    reportContent += "\n📦 加料提醒:\n";
    data.risk_and_suggestions.material_reminders.forEach((reminder) => {
      reportContent += `${reminder}\n`;
    });
  }

  // 将完整的报告作为一条消息发送
  if (reportContent.trim()) {
    addMessage("assistant", reportContent.trim());
  }
}

function formatMessageContent(content) {
  if (!content) return "";

  // 如果内容已经包含HTML标签，进行安全检查
  if (content.includes("<")) {
    // 检查是否有未闭合的表格标签
    const openTableTags = (content.match(/<table/g) || []).length;
    const closeTableTags = (content.match(/<\/table>/g) || []).length;

    // 如果表格标签不匹配，进行修复
    if (openTableTags > closeTableTags) {
      content += "</tbody></table>".repeat(openTableTags - closeTableTags);
    }

    // 检查其他可能未闭合的标签
    const openUlTags = (content.match(/<ul>/g) || []).length;
    const closeUlTags = (content.match(/<\/ul>/g) || []).length;
    if (openUlTags > closeUlTags) {
      content += "</ul>".repeat(openUlTags - closeUlTags);
    }

    return content;
  }

  // 将Markdown格式转换为HTML
  return convertMarkdownToHtml(content);
}

function convertMarkdownToHtml(markdown) {
  if (!markdown) return "";

  let html = markdown;

  // 处理表格
  html = html.replace(
    /\|(.+)\|\n\|[-\s|:]+\|\n((?:\|.+\|\n?)*)/g,
    (match, header, rows) => {
      const headerCells = header
        .split("|")
        .map((cell) => cell.trim())
        .filter((cell) => cell);
      const rowLines = rows.trim().split("\n");

      let tableHtml = '<table class="data-table">';

      // 表头
      tableHtml += "<thead><tr>";
      headerCells.forEach((cell) => {
        tableHtml += `<th>${escapeHtml(cell)}</th>`;
      });
      tableHtml += "</tr></thead>";

      // 表格内容
      tableHtml += "<tbody>";
      rowLines.forEach((row) => {
        if (row.trim()) {
          const cells = row
            .split("|")
            .map((cell) => cell.trim())
            .filter((cell) => cell);
          tableHtml += "<tr>";
          cells.forEach((cell) => {
            tableHtml += `<td>${escapeHtml(cell)}</td>`;
          });
          tableHtml += "</tr>";
        }
      });
      tableHtml += "</tbody></table>";

      return tableHtml;
    }
  );

  // 处理标题 - 使用更好的样式
  html = html.replace(
    /^### (.+)$/gm,
    '<h3 style="margin: 15px 0 8px 0; color: #2c3e50; font-size: 16px; font-weight: 600; border-bottom: 1px solid #ecf0f1; padding-bottom: 4px;">$1</h3>'
  );
  html = html.replace(
    /^## (.+)$/gm,
    '<h2 style="margin: 20px 0 10px 0; color: #2c3e50; font-size: 18px; font-weight: 600; border-bottom: 2px solid #3498db; padding-bottom: 6px;">$1</h2>'
  );
  html = html.replace(
    /^# (.+)$/gm,
    '<h1 style="margin: 25px 0 15px 0; color: #2c3e50; font-size: 20px; font-weight: 700; border-bottom: 3px solid #e74c3c; padding-bottom: 8px;">$1</h1>'
  );

  // 处理粗体
  html = html.replace(
    /\*\*(.+?)\*\*/g,
    '<strong style="font-weight: 600; color: #2c3e50;">$1</strong>'
  );

  // 处理列表项 - 支持多种列表符号
  html = html.replace(
    /^[•·▪▫‣⁃] (.+)$/gm,
    '<li style="margin: 4px 0; line-height: 1.4;">$1</li>'
  );

  // 将连续的li包装在ul中
  html = html.replace(/(<li[^>]*>.*?<\/li>\s*)+/g, (match) => {
    return `<ul style="margin: 8px 0; padding-left: 20px; list-style-type: disc;">${match}</ul>`;
  });

  // 处理分隔线
  html = html.replace(
    /^---+$/gm,
    '<hr style="margin: 15px 0; border: none; border-top: 1px solid #bdc3c7;">'
  );

  // 处理段落分隔 - 保持适当的间距
  html = html.replace(/\n\n+/g, '<div style="margin: 12px 0;"></div>');
  html = html.replace(/\n/g, "<br>");

  return html;
}

function escapeHtml(text) {
  const div = document.createElement("div");
  div.textContent = text;
  return div.innerHTML;
}

function startStreamingTimeout() {
  // 清除之前的超时
  if (streamingTimeout) {
    clearTimeout(streamingTimeout);
  }

  // 设置30秒超时
  streamingTimeout = setTimeout(() => {
    if (isStreaming && currentStreamingMessage) {
      console.warn("Streaming timeout, forcing completion");
      // 强制结束流式输出
      currentStreamingMessage = null;
      currentStreamingElement = null;
      isStreaming = false;
      addMessage("system", "⚠️ 输出超时，可能存在网络问题");
    }
  }, 30000);
}

function clearStreamingTimeout() {
  if (streamingTimeout) {
    clearTimeout(streamingTimeout);
    streamingTimeout = null;
  }
}

// Chrome extension message listener
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "toggleChat") {
    toggleChatWindow();
  }
});

// Initialize when DOM is ready
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", createFloatingIcon);
} else {
  createFloatingIcon();
}
