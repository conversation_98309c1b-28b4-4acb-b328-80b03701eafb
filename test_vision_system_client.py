#!/usr/bin/env python3
"""
观火系统客户端测试脚本
用于测试向吹炼智能体发送紧急监控请求
"""

import asyncio
import websockets
import json
from datetime import datetime


async def test_vision_system_client():
    """测试观火系统客户端"""
    uri = "ws://localhost:8005"

    try:
        print(f"正在连接到观火系统WebSocket服务器: {uri}")

        async with websockets.connect(uri) as websocket:
            # 等待连接确认
            response = await websocket.recv()
            print(f"连接确认: {response}")

            while True:
                print("\n" + "=" * 50)
                print("观火系统测试客户端")
                print("=" * 50)
                print("1. 发送喷溅事件")
                print("2. 发送火焰亮度异常事件")
                print("3. 发送温度异常事件")
                print("4. 发送自定义事件")
                print("5. 退出")
                print("-" * 50)

                choice = input("请选择操作 (1-5): ").strip()

                if choice == "1":
                    await send_event(websocket, "喷溅")
                elif choice == "2":
                    await send_event(websocket, "火焰亮度异常")
                elif choice == "3":
                    await send_event(websocket, "温度异常")
                elif choice == "4":
                    task = input("请输入事件类型: ").strip()
                    if task:
                        await send_event(websocket, task)
                    else:
                        print("事件类型不能为空")
                elif choice == "5":
                    print("退出测试客户端")
                    break
                else:
                    print("无效选择，请重新输入")

    except ConnectionRefusedError:
        print(f"❌ 无法连接到服务器 {uri}")
        print("请确保吹炼智能体服务已启动")
    except Exception as e:
        print(f"❌ 连接错误: {e}")


async def send_event(websocket, task_type):
    """发送事件通知"""
    try:
        # 使用当前时间或让用户输入时间
        use_current_time = input("使用当前时间? (y/n, 默认y): ").strip().lower()

        if use_current_time in ['', 'y', 'yes']:
            event_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        else:
            event_time = input("请输入时间 (格式: YYYY-MM-DD HH:MM:SS): ").strip()
            if not event_time:
                event_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 构造消息 (观火系统的序列化字符串格式)
        message = f'{{task: "{task_type}", time: "{event_time}"}}'

        print(f"发送消息: {message}")

        # 发送消息
        await websocket.send(message)

        # 接收响应
        response = await websocket.recv()
        response_data = json.loads(response)

        print(f"✅ 服务器响应:")
        print(f"   状态: {response_data.get('status')}")
        print(f"   消息: {response_data.get('message')}")
        print(f"   任务: {response_data.get('task')}")
        print(f"   处理时间: {response_data.get('processed_time')}")

    except Exception as e:
        print(f"❌ 发送事件失败: {e}")


def main():
    """主函数"""
    print("观火系统客户端测试工具")
    print("用于测试向吹炼智能体发送紧急监控请求")
    print("确保吹炼智能体服务已启动并监听端口8002")
    print()

    try:
        asyncio.run(test_vision_system_client())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")


if __name__ == "__main__":
    main()
