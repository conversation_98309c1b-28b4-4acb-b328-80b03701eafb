<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>智能炼钢决策系统 - WebSocket测试</title>
    <style>
        body { font-family: sans-serif; margin: 0; padding: 0; display: flex; flex-direction: column; height: 100vh; }
        #chatContainer {
            flex: 1;
            display: flex;
            flex-direction: column;
            border: 1px solid #ccc;
            margin: 10px;
            padding: 10px;
            overflow-y: auto;
            background-color: #f9f9f9;
        }
        .message {
            display: flex;
            margin: 5px 0;
        }
        .message.user {
            justify-content: flex-end;
        }
        .message.agent {
            justify-content: flex-start;
        }
        .message span {
            max-width: 70%;
            padding: 10px;
            border-radius: 10px;
        }
        .message.user span {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        .message.agent span {
            background-color: #f8d7da;
            color: #842029;
        }
        #inputContainer {
            display: flex;
            padding: 10px;
            border-top: 1px solid #ccc;
            background-color: #fff;
        }
        #questionInput {
            flex: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        #sendButton {
            margin-left: 10px;
            padding: 10px;
            border: none;
            background-color: #007bff;
            color: white;
            border-radius: 5px;
            cursor: pointer;
        }
        #sendButton:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        #status.connected { color: green; }
        #status.disconnected { color: red; }
    </style>
</head>
<body>
    <h1>智能炼钢决策系统 - WebSocket测试</h1>
    <p>状态: <b id="status" class="disconnected">未连接</b></p>
    <p><small>提示：可以输入问题查询生产数据，或使用下方的命令按钮</small></p>
    <div id="chatContainer"></div>
    <div id="inputContainer">
        <input type="text" id="questionInput" placeholder="在这里输入问题..." />
        <button id="sendButton" disabled>发送</button>
    </div>

    <script>
        const statusElem = document.getElementById('status');
        const questionInput = document.getElementById('questionInput');
        const sendButton = document.getElementById('sendButton');
        const chatContainer = document.getElementById('chatContainer');

        // 确保您的 FastAPI 服务器正在运行
        // 如果您的服务器地址或端口不同，请修改下面的 URL
        const socket = new WebSocket("ws://localhost:9000/ws/agent/decision");

        socket.onopen = (event) => {
            console.log("WebSocket connection opened.");
            statusElem.textContent = "已连接";
            statusElem.className = "connected";
            sendButton.disabled = false;
        };

        let currentStreamMessage = null;

        socket.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                let contentElem;

                if (message.type === "connected") {
                    const messageElem = document.createElement('div');
                    messageElem.className = 'message agent';
                    contentElem = document.createElement('span');
                    contentElem.textContent = `系统消息: ${message.message}`;
                    messageElem.appendChild(contentElem);
                    chatContainer.appendChild(messageElem);
                } else if (message.type === "processing") {
                    const messageElem = document.createElement('div');
                    messageElem.className = 'message agent';
                    contentElem = document.createElement('span');
                    contentElem.textContent = `${message.message}`;
                    messageElem.appendChild(contentElem);
                    chatContainer.appendChild(messageElem);
                } else if (message.type === "stream_start") {
                    // 创建新的流式消息容器
                    const messageElem = document.createElement('div');
                    messageElem.className = 'message agent';
                    contentElem = document.createElement('span');
                    contentElem.textContent = '';
                    messageElem.appendChild(contentElem);
                    chatContainer.appendChild(messageElem);
                    currentStreamMessage = contentElem;
                } else if (message.type === "stream") {
                    // 追加流式内容
                    if (currentStreamMessage) {
                        currentStreamMessage.textContent += message.content;
                    }
                } else if (message.type === "stream_end") {
                    // 流式响应结束
                    if (message.total_content && currentStreamMessage) {
                        currentStreamMessage.textContent = message.total_content;
                    }
                    currentStreamMessage = null;
                } else if (message.type === "info") {
                    const messageElem = document.createElement('div');
                    messageElem.className = 'message agent';
                    contentElem = document.createElement('span');
                    contentElem.textContent = `信息: ${message.message}`;
                    messageElem.appendChild(contentElem);
                    chatContainer.appendChild(messageElem);
                } else if (message.type === "history") {
                    const messageElem = document.createElement('div');
                    messageElem.className = 'message agent';
                    contentElem = document.createElement('span');
                    contentElem.textContent = `历史记录: ${JSON.stringify(message.content, null, 2)}`;
                    messageElem.appendChild(contentElem);
                    chatContainer.appendChild(messageElem);
                } else if (message.type === "error") {
                    const messageElem = document.createElement('div');
                    messageElem.className = 'message agent';
                    contentElem = document.createElement('span');
                    contentElem.style.backgroundColor = '#f8d7da';
                    contentElem.style.color = '#842029';
                    contentElem.textContent = `错误: ${message.message}`;
                    messageElem.appendChild(contentElem);
                    chatContainer.appendChild(messageElem);
                } else if (message.type === "pong") {
                    console.log("收到心跳响应");
                }

                chatContainer.scrollTop = chatContainer.scrollHeight;
            } catch (error) {
                console.error("Failed to parse message:", event.data);
            }
        };

        socket.onclose = (event) => {
            console.log("WebSocket connection closed.");
            statusElem.textContent = "已断开";
            statusElem.className = "disconnected";
            sendButton.disabled = true;
        };

        socket.onerror = (error) => {
            console.error("WebSocket Error: ", error);
            statusElem.textContent = "连接错误";
            statusElem.className = "disconnected";
        };

        sendButton.onclick = () => {
            const question = questionInput.value.trim();
            if (question && socket.readyState === WebSocket.OPEN) {
                // 显示用户消息
                const messageElem = document.createElement('div');
                messageElem.className = 'message user';
                const contentElem = document.createElement('span');
                contentElem.textContent = question;
                messageElem.appendChild(contentElem);
                chatContainer.appendChild(messageElem);
                chatContainer.scrollTop = chatContainer.scrollHeight;

                // 发送JSON格式的查询消息
                const queryMessage = {
                    type: "query",
                    content: question,
                    context: {}
                };
                socket.send(JSON.stringify(queryMessage));
                questionInput.value = '';
            }
        };
        
        // 添加特殊命令按钮
        const createCommandButton = (text, command) => {
            const button = document.createElement('button');
            button.textContent = text;
            button.style.marginLeft = '5px';
            button.style.padding = '5px 10px';
            button.style.border = 'none';
            button.style.backgroundColor = '#6c757d';
            button.style.color = 'white';
            button.style.borderRadius = '3px';
            button.style.cursor = 'pointer';
            
            button.onclick = () => {
                if (socket.readyState === WebSocket.OPEN) {
                    socket.send(JSON.stringify(command));
                }
            };
            
            return button;
        };
        
        // 添加命令按钮到输入容器
        const inputContainer = document.getElementById('inputContainer');
        inputContainer.appendChild(createCommandButton('查看历史', { type: 'history' }));
        inputContainer.appendChild(createCommandButton('清空历史', { type: 'clear_history' }));
        inputContainer.appendChild(createCommandButton('心跳测试', { type: 'ping' }));
        inputContainer.appendChild(createCommandButton('退出', { type: 'exit' }));
        
        // 允许按回车键发送
        questionInput.addEventListener("keyup", function(event) {
            if (event.key === "Enter") {
                event.preventDefault();
                sendButton.click();
            }
        });
    </script>
</body>
</html>