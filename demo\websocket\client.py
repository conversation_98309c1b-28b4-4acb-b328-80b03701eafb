"""
WebSocket客户端演示脚本
用于测试智能体交互功能
"""

import asyncio
import json
import websockets
from datetime import datetime
from typing import Optional, Any


class SteelAgentClient:
    """钢铁大模型WebSocket客户端"""
    
    def __init__(self, url: str = "ws://localhost:8001/ws/agent"):
        self.url = url
        self.websocket: Optional[Any] = None  # websockets connection object
        self.client_id: Optional[str] = None
        self.running = False
        
    async def connect(self):
        """连接到服务器"""
        print(f"正在连接到 {self.url}...")
        self.websocket = await websockets.connect(self.url)
        self.running = True
        print("连接成功！")
        # 等待接收欢迎消息
        self.connected_event = asyncio.Event()
        
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            print("已断开连接")
            
    async def send_query(self, content: str):
        """发送查询"""
        if not self.websocket:
            raise RuntimeError("WebSocket未连接")
        message = {
            "type": "query",
            "content": content,
            "context": {}
        }
        await self.websocket.send(json.dumps(message))
        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 发送: {content}")
        
    async def send_action(self, action: str, data: dict):
        """发送动作"""
        if not self.websocket:
            raise RuntimeError("WebSocket未连接")
        message = {
            "type": "action",
            "action": action,
            "data": data
        }
        await self.websocket.send(json.dumps(message))
        
    async def receive_messages(self):
        """接收消息"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await self.handle_message(data)
        except websockets.exceptions.ConnectionClosed:
            print("连接已关闭")
            self.running = False
            
    async def handle_message(self, data: dict):
        """处理接收到的消息"""
        msg_type = data.get("type")
        # print(f"[DEBUG] 收到消息类型: {msg_type}, 数据: {data}")  # 调试信息（已注释）
        
        if msg_type == "connected":
            self.client_id = data.get("client_id")
            print(f"\n{data.get('message')}")
            print(f"客户端ID: {self.client_id}")
            # 设置连接事件，表示已收到欢迎消息
            if hasattr(self, 'connected_event'):
                self.connected_event.set()
            
        elif msg_type == "status":
            print(f"[状态] {data.get('content')}")
            
        elif msg_type == "intent":
            print(f"[意图识别] {data.get('description')} (置信度: {data.get('confidence'):.2f})")
            
        elif msg_type == "content":
            print(data.get('content'), end='', flush=True)
            
        elif msg_type == "alert":
            print(f"\n{'='*50}")
            print(f"[预警] {data.get('title')}")
            print(f"严重程度: {data.get('severity')}")
            print(f"内容: {data.get('content')}")
            print(f"时间: {data.get('timestamp')}")
            
            # 显示可用操作
            if data.get('actions'):
                print("\n可用操作:")
                for i, action in enumerate(data['actions']):
                    print(f"  {i+1}. {action['label']}")
                    
            print(f"{'='*50}\n")
            
        elif msg_type == "interactive":
            print("\n[交互选项]")
            for option in data.get('options', []):
                print(f"- {option.get('text')}")
                
        elif msg_type == "done":
            print(f"\n[完成] 会话ID: {data.get('session_id')}")
            print("-" * 50)
            print("\n您: ", end='', flush=True)  # 显示输入提示符
            
        elif msg_type == "error":
            print(f"\n[错误] {data.get('content')}")
            
        elif msg_type == "ping":
            # 自动回复pong
            if self.websocket:
                await self.websocket.send(json.dumps({"type": "pong"}))
            
    async def interactive_loop(self):
        """交互式循环"""
        # 等待收到欢迎消息
        if hasattr(self, 'connected_event'):
            await self.connected_event.wait()
        
        print("\n" + "="*50)
        print("钢铁大模型智能助手 - Demo客户端")
        print("="*50)
        print("\n输入 'quit' 退出，'help' 查看帮助")
        print("-"*50)
        
        # 使用 asyncio 的 stdin reader
        import sys
        if sys.platform == 'win32':
            # Windows 需要特殊处理
            import threading
            import queue
            
            input_queue = queue.Queue()
            
            def input_thread():
                while self.running:
                    try:
                        line = input("\n您: ")
                        input_queue.put(line)
                    except EOFError:
                        break
            
            # 启动输入线程
            thread = threading.Thread(target=input_thread, daemon=True)
            thread.start()
            
            while self.running:
                try:
                    # 非阻塞地检查输入
                    try:
                        user_input = input_queue.get_nowait()
                    except queue.Empty:
                        await asyncio.sleep(0.1)
                        continue
                    
                    if user_input.lower() == 'quit':
                        break
                        
                    elif user_input.lower() == 'help':
                        self.show_help()
                        
                    elif user_input.lower() == 'alert':
                        # 模拟触发一个预警
                        await self.trigger_mock_alert()
                        
                    else:
                        # 发送查询
                        await self.send_query(user_input)
                        
                except KeyboardInterrupt:
                    print("\n正在退出...")
                    break
        else:
            # Unix/Linux 系统
            reader = asyncio.StreamReader()
            protocol = asyncio.StreamReaderProtocol(reader)
            await asyncio.get_running_loop().connect_read_pipe(lambda: protocol, sys.stdin)
            
            while self.running:
                try:
                    print("\n您: ", end='', flush=True)
                    user_input = await reader.readline()
                    user_input = user_input.decode().strip()
                    
                    if user_input.lower() == 'quit':
                        break
                        
                    elif user_input.lower() == 'help':
                        self.show_help()
                        
                    elif user_input.lower() == 'alert':
                        # 模拟触发一个预警
                        await self.trigger_mock_alert()
                        
                    else:
                        # 发送查询
                        await self.send_query(user_input)
                        
                except KeyboardInterrupt:
                    print("\n正在退出...")
                    break
                
    def show_help(self):
        """显示帮助信息"""
        print("\n" + "="*50)
        print("帮助信息:")
        print("="*50)
        print("1. 经营决策相关查询示例:")
        print("   - 分析本月吨钢毛利下降的原因")
        print("   - 查看最近一周的成本构成变化")
        print("   - 哪些产品的利润率最高？")
        print("   - 分析HRB400E的市场价格趋势")
        print("\n2. 生产控制相关查询示例:")
        print("   - 当前炉况如何？")
        print("   - 氧枪位置应该调整到多少？")
        print("   - 如何预防喷溅？")
        print("\n3. 质量分析相关查询示例:")
        print("   - 最近的质量合格率如何？")
        print("   - 分析质量不合格的原因")
        print("\n4. 特殊命令:")
        print("   - help: 显示此帮助信息")
        print("   - alert: 模拟触发一个KPI预警")
        print("   - quit: 退出程序")
        print("="*50)
        
    async def trigger_mock_alert(self):
        """触发模拟预警"""
        print("\n正在模拟KPI异常...")
        # 实际应用中，这应该由服务器端的监控系统触发
        # 这里只是为了演示效果
        
    async def run(self):
        """运行客户端"""
        try:
            await self.connect()
            
            # 创建两个任务：接收消息和用户交互
            receive_task = asyncio.create_task(self.receive_messages())
            interact_task = asyncio.create_task(self.interactive_loop())
            
            # 等待任一任务完成
            done, pending = await asyncio.wait(
                [receive_task, interact_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # 取消未完成的任务
            for task in pending:
                task.cancel()
                
        finally:
            await self.disconnect()


async def main():
    """主函数"""
    client = SteelAgentClient()
    await client.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序已退出")