# 钢铁智能体系统数据库设计文档

## 一、概述

钢铁智能体系统需要处理大量的结构化和非结构化数据，包括实时生产数据、历史数据、文档资料、图像视频等。本文档详细设计了多种数据库的协同使用方案，以满足不同类型数据的存储和查询需求。

## 二、数据库选型

### 2.1 数据库体系架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        数据存储层架构                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  结构化数据                     半结构化数据           非结构化数据    │
│  ┌──────────────┐             ┌──────────────┐    ┌──────────┐ │
│  │  PostgreSQL  │             │   MongoDB    │    │  MinIO   │ │
│  │  + TimescaleDB│             │  (文档数据)   │    │ (对象存储) │ │
│  │  (关系+时序)   │             └──────────────┘    └──────────┘ │
│  └──────────────┘                                              │
│                                                                 │
│  实时数据缓存                   向量数据              图数据       │
│  ┌──────────────┐             ┌──────────────┐    ┌──────────┐ │
│  │    Redis     │             │   Milvus     │    │  Neo4j   │ │
│  │  (内存缓存)   │             │  (向量数据库)  │    │ (知识图谱) │ │
│  └──────────────┘             └──────────────┘    └──────────┘ │
│                                                                 │
│  时序数据                      搜索引擎                          │
│  ┌──────────────┐             ┌──────────────┐                │
│  │  InfluxDB    │             │Elasticsearch │                │
│  │ (边缘时序数据) │             │  (全文检索)   │                │
│  └──────────────┘             └──────────────┘                │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 数据库选型理由

| 数据库 | 用途 | 选型理由 |
|--------|------|----------|
| PostgreSQL + TimescaleDB | 业务数据、历史数据 | 强一致性、SQL支持、时序扩展 |
| Redis | 实时缓存、会话管理 | 高性能、低延迟、丰富数据结构 |
| InfluxDB | 边缘实时数据 | 专为时序数据优化、高写入性能 |
| MongoDB | 半结构化数据 | 文档存储、灵活schema |
| MinIO | 文件、视频、模型 | S3兼容、分布式、高可用 |
| Milvus | 向量embedding | 向量相似度搜索、GPU加速 |
| Neo4j | 知识图谱 | 图查询、因果关系分析 |
| Elasticsearch | 全文搜索 | 分布式搜索、日志分析 |

## 三、结构化数据设计（PostgreSQL + TimescaleDB）

### 3.1 数据库架构

```sql
-- 创建数据库
CREATE DATABASE steel_production;

-- 启用TimescaleDB扩展
CREATE EXTENSION IF NOT EXISTS timescaledb;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

### 3.2 核心业务表设计

#### 3.2.1 转炉设备表

```sql
-- 转炉设备表
CREATE TABLE furnaces (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    furnace_no VARCHAR(20) UNIQUE NOT NULL,
    furnace_type VARCHAR(50),
    capacity DECIMAL(10,2),  -- 吨
    manufacturer VARCHAR(100),
    commission_date DATE,
    total_heats INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 炉衬记录表
CREATE TABLE furnace_linings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    furnace_id UUID REFERENCES furnaces(id),
    lining_no INTEGER NOT NULL,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE,
    total_heats INTEGER DEFAULT 0,
    max_heats INTEGER,
    status VARCHAR(20) DEFAULT 'active',
    UNIQUE(furnace_id, lining_no)
);
```

#### 3.2.2 炉次主表

```sql
-- 炉次主表
CREATE TABLE heats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    heat_no VARCHAR(50) UNIQUE NOT NULL,
    furnace_id UUID REFERENCES furnaces(id),
    steel_grade VARCHAR(50) NOT NULL,
    shift_id VARCHAR(20),
    crew_id VARCHAR(20),
    
    -- 时间信息
    charge_time TIMESTAMP WITH TIME ZONE,
    tap_time TIMESTAMP WITH TIME ZONE,
    total_duration INTEGER,  -- 秒
    
    -- 生产结果
    steel_weight DECIMAL(10,2),  -- 吨
    yield_rate DECIMAL(5,2),     -- %
    quality_score DECIMAL(3,2),
    
    -- 状态
    status VARCHAR(20) DEFAULT 'planned',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_heat_no (heat_no),
    INDEX idx_charge_time (charge_time),
    INDEX idx_steel_grade (steel_grade)
);

-- 转换为时序表
SELECT create_hypertable('heats', 'charge_time', 
    chunk_time_interval => INTERVAL '1 month');
```

#### 3.2.3 原料数据表

```sql
-- 铁水数据
CREATE TABLE hot_metal_charges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    heat_id UUID REFERENCES heats(id),
    weight DECIMAL(10,2) NOT NULL,  -- 吨
    temperature DECIMAL(6,1),       -- ℃
    
    -- 成分 (%)
    c_content DECIMAL(5,3),
    si_content DECIMAL(5,3),
    mn_content DECIMAL(5,3),
    p_content DECIMAL(5,3),
    s_content DECIMAL(5,3),
    
    ladle_no VARCHAR(20),
    charge_time TIMESTAMP WITH TIME ZONE NOT NULL,
    
    INDEX idx_heat_charge (heat_id, charge_time)
);

-- 废钢数据
CREATE TABLE scrap_charges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    heat_id UUID REFERENCES heats(id),
    scrap_type VARCHAR(50),
    weight DECIMAL(10,2) NOT NULL,  -- 吨
    moisture_content DECIMAL(5,2),  -- %
    charge_time TIMESTAMP WITH TIME ZONE NOT NULL,
    charge_sequence INTEGER,
    
    INDEX idx_heat_scrap (heat_id, charge_time)
);

-- 辅料数据
CREATE TABLE flux_additions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    heat_id UUID REFERENCES heats(id),
    material_type VARCHAR(50) NOT NULL,
    weight DECIMAL(10,3) NOT NULL,  -- 千克
    addition_time TIMESTAMP WITH TIME ZONE NOT NULL,
    addition_method VARCHAR(20),
    
    INDEX idx_heat_flux (heat_id, addition_time)
);
```

#### 3.2.4 过程数据表（时序数据）

```sql
-- 吹炼过程数据（高频时序数据）
CREATE TABLE blowing_process (
    heat_id UUID NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- 氧枪数据
    oxygen_flow DECIMAL(10,2),      -- Nm³/h
    lance_position DECIMAL(6,1),    -- mm
    lance_pattern VARCHAR(20),
    
    -- 供氧数据
    oxygen_pressure DECIMAL(6,2),   -- MPa
    total_oxygen_volume DECIMAL(10,2), -- Nm³
    
    -- 温度数据
    bath_temperature DECIMAL(6,1),  -- ℃
    
    -- 其他
    bath_level DECIMAL(6,1),        -- mm
    
    PRIMARY KEY (heat_id, timestamp)
);

-- 创建时序表
SELECT create_hypertable('blowing_process', 'timestamp',
    chunk_time_interval => INTERVAL '1 day');

-- 烟气分析数据
CREATE TABLE offgas_analysis (
    heat_id UUID NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- 成分 (%)
    co_content DECIMAL(5,2),
    co2_content DECIMAL(5,2),
    o2_content DECIMAL(5,2),
    
    -- 流量温度
    flow_rate DECIMAL(10,2),        -- Nm³/h
    temperature DECIMAL(6,1),       -- ℃
    
    PRIMARY KEY (heat_id, timestamp)
);

SELECT create_hypertable('offgas_analysis', 'timestamp',
    chunk_time_interval => INTERVAL '1 day');
```

#### 3.2.5 质量数据表

```sql
-- 终点成分表
CREATE TABLE endpoint_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    heat_id UUID REFERENCES heats(id),
    sample_time TIMESTAMP WITH TIME ZONE NOT NULL,
    sample_type VARCHAR(20),  -- 'sublance', 'ladle'
    
    -- 成分 (%)
    c_content DECIMAL(5,3),
    si_content DECIMAL(5,3),
    mn_content DECIMAL(5,3),
    p_content DECIMAL(5,3),
    s_content DECIMAL(5,3),
    
    -- 温度
    temperature DECIMAL(6,1),  -- ℃
    
    -- 其他元素（JSONB存储）
    other_elements JSONB,
    
    INDEX idx_heat_endpoint (heat_id, sample_time)
);

-- 质量判定表
CREATE TABLE quality_judgments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    heat_id UUID REFERENCES heats(id),
    judgment_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 判定结果
    carbon_qualified BOOLEAN,
    temperature_qualified BOOLEAN,
    composition_qualified BOOLEAN,
    overall_qualified BOOLEAN,
    
    -- 偏差
    carbon_deviation DECIMAL(5,3),
    temp_deviation DECIMAL(6,1),
    
    remarks TEXT
);
```

### 3.3 数据分区策略

```sql
-- 按月分区示例
CREATE TABLE blowing_process_2024_01 PARTITION OF blowing_process
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

-- 自动创建分区
SELECT add_retention_policy('blowing_process', INTERVAL '2 years');
SELECT add_compression_policy('blowing_process', INTERVAL '7 days');
```

## 四、实时缓存设计（Redis）

### 4.1 数据结构设计

```python
# Redis数据结构设计

# 1. 实时炉况数据（Hash）
"""
Key: furnace:state:{furnace_id}
Fields:
  - heat_no: 当前炉次号
  - temperature: 当前温度
  - carbon_content: 碳含量
  - oxygen_flow: 氧流量
  - lance_position: 枪位
  - update_time: 更新时间
TTL: 24小时
"""

# 2. 实时预警队列（List）
"""
Key: alerts:queue:{severity}  # severity: high/medium/low
Value: JSON格式的预警信息
{
    "heat_no": "************",
    "type": "splash_risk",
    "probability": 0.85,
    "timestamp": "2024-01-01T10:30:00Z",
    "message": "喷溅风险预警"
}
"""

# 3. 模型推理缓存（String）
"""
Key: inference:cache:{model}:{hash(input)}
Value: 推理结果JSON
TTL: 5分钟
"""

# 4. 会话状态（Hash）
"""
Key: session:{session_id}
Fields:
  - user_id: 用户ID
  - context: 对话上下文
  - last_access: 最后访问时间
TTL: 30分钟
"""

# 5. 实时指标（Sorted Set）
"""
Key: metrics:realtime:{metric_name}
Member: {furnace_id}:{timestamp}
Score: metric_value
"""

# 6. 操作建议缓存（List）
"""
Key: suggestions:{heat_no}
Value: JSON格式的操作建议
TTL: 吹炼结束后1小时
"""
```

### 4.2 Redis集群配置

```yaml
# Redis集群配置
redis:
  cluster:
    nodes:
      - host: redis-1
        port: 6379
      - host: redis-2
        port: 6379
      - host: redis-3
        port: 6379
    
  sentinel:
    master: steel-master
    sentinels:
      - host: sentinel-1
        port: 26379
      - host: sentinel-2
        port: 26379
      - host: sentinel-3
        port: 26379
        
  config:
    maxmemory: 8gb
    maxmemory-policy: allkeys-lru
    save: ""  # 禁用持久化以提高性能
```

## 五、时序数据设计（InfluxDB）

### 5.1 数据模型

```influxql
-- 测量(Measurement)设计

-- 1. 实时过程数据
-- Measurement: process_data
-- Tags: furnace_id, heat_no
-- Fields: oxygen_flow, lance_position, temperature, carbon_content
-- Time: 纳秒精度时间戳

-- 2. 设备监控数据
-- Measurement: equipment_status
-- Tags: equipment_id, equipment_type
-- Fields: status, temperature, pressure, vibration
-- Time: 纳秒精度时间戳

-- 3. 能源消耗数据
-- Measurement: energy_consumption
-- Tags: furnace_id, energy_type
-- Fields: consumption_rate, total_consumption
-- Time: 纳秒精度时间戳
```

### 5.2 保留策略

```influxql
-- 创建数据库
CREATE DATABASE steel_edge;

-- 创建保留策略
-- 高精度数据：保留7天
CREATE RETENTION POLICY "high_precision" ON "steel_edge" 
  DURATION 7d REPLICATION 1 DEFAULT;

-- 降采样数据：保留30天
CREATE RETENTION POLICY "downsampled" ON "steel_edge" 
  DURATION 30d REPLICATION 1;

-- 聚合数据：保留1年
CREATE RETENTION POLICY "aggregated" ON "steel_edge" 
  DURATION 365d REPLICATION 1;
```

### 5.3 连续查询

```influxql
-- 1分钟平均值
CREATE CONTINUOUS QUERY "cq_1m_avg" ON "steel_edge"
BEGIN
  SELECT mean(*) INTO "downsampled"."process_data_1m"
  FROM "process_data"
  GROUP BY time(1m), *
END;

-- 1小时聚合
CREATE CONTINUOUS QUERY "cq_1h_agg" ON "steel_edge"
BEGIN
  SELECT 
    mean(*) AS mean,
    max(*) AS max,
    min(*) AS min,
    stddev(*) AS stddev
  INTO "aggregated"."process_data_1h"
  FROM "process_data"
  GROUP BY time(1h), *
END;
```

## 六、文档数据设计（MongoDB）

### 6.1 集合设计

```javascript
// 1. 操作日志集合
db.operation_logs = {
  _id: ObjectId(),
  heat_no: "************",
  timestamp: ISODate("2024-01-01T10:30:00Z"),
  operator: {
    id: "OP001",
    name: "张三",
    role: "炉长"
  },
  operation: {
    type: "lance_adjustment",
    previous_position: 1500,
    new_position: 1600,
    reason: "温度偏低，提高脱碳速率"
  },
  context: {
    temperature: 1650,
    carbon_content: 0.15,
    oxygen_flow: 18000
  },
  tags: ["manual_operation", "temperature_control"]
}

// 2. 专家知识库集合
db.expert_knowledge = {
  _id: ObjectId(),
  category: "splash_prevention",
  title: "喷溅预防操作规程",
  content: {
    symptoms: [
      "火焰发白发亮",
      "炉口冒出褐色烟气",
      "炉渣泡沫化指数>2.5"
    ],
    actions: [
      {
        step: 1,
        action: "立即提枪至2000mm",
        duration: "30秒"
      },
      {
        step: 2,
        action: "降低氧气流量至15000Nm³/h",
        condition: "如喷溅未缓解"
      }
    ],
    theory: "喷溅主要由于...",
    cases: [
      {
        heat_no: "20231201-005",
        description: "成功处理喷溅案例",
        video_url: "minio://videos/splash_case_001.mp4"
      }
    ]
  },
  metadata: {
    author: "北科大冶金专家组",
    created_at: ISODate("2023-12-01T00:00:00Z"),
    version: "1.2",
    review_status: "approved"
  }
}

// 3. 模型训练数据集
db.training_datasets = {
  _id: ObjectId(),
  dataset_name: "blowing_cot_v1",
  dataset_type: "chain_of_thought",
  samples: [
    {
      input: {
        furnace_state: {
          temperature: 1680,
          carbon: 0.12,
          // ...
        },
        target: {
          carbon: 0.045,
          temperature: 1650
        }
      },
      reasoning: [
        "当前碳含量0.12%，目标0.045%，需脱碳0.075%",
        "当前温度1680℃，略高于目标，需适度控制",
        "根据脱碳速率模型，预计需要3-4分钟",
        "建议采用高-低-高的供氧模式"
      ],
      output: {
        strategy: "三段式吹炼",
        actions: [
          // ...
        ]
      }
    }
  ],
  statistics: {
    total_samples: 10000,
    quality_score: 0.92,
    last_updated: ISODate("2024-01-01T00:00:00Z")
  }
}

// 4. 异常事件记录
db.abnormal_events = {
  _id: ObjectId(),
  event_id: "EVT-************",
  heat_no: "************",
  event_type: "splash",
  severity: "high",
  timestamp: ISODate("2024-01-01T10:35:00Z"),
  
  timeline: [
    {
      time: "10:33:00",
      observation: "火焰颜色变白",
      data_snapshot: {
        foam_index: 2.8,
        co_co2_ratio: 0.85
      }
    },
    {
      time: "10:34:00",
      action: "操作员提枪",
      result: "喷溅未缓解"
    }
  ],
  
  root_cause: {
    primary: "炉渣过氧化",
    contributing: ["加料时机不当", "氧枪模式选择不当"],
    analysis: "详细RCA分析..."
  },
  
  attachments: [
    {
      type: "video",
      url: "minio://videos/splash_20240101_001.mp4",
      duration: 120
    },
    {
      type: "report",
      url: "minio://reports/RCA_20240101_001.pdf"
    }
  ]
}
```

### 6.2 索引策略

```javascript
// 创建索引
db.operation_logs.createIndex({ heat_no: 1, timestamp: -1 });
db.operation_logs.createIndex({ "operator.id": 1 });
db.operation_logs.createIndex({ tags: 1 });

db.expert_knowledge.createIndex({ category: 1 });
db.expert_knowledge.createIndex({ "$**": "text" }); // 全文索引

db.abnormal_events.createIndex({ heat_no: 1 });
db.abnormal_events.createIndex({ event_type: 1, severity: 1 });
db.abnormal_events.createIndex({ timestamp: -1 });

// TTL索引（自动过期）
db.operation_logs.createIndex(
  { timestamp: 1 },
  { expireAfterSeconds: 365 * 24 * 3600 } // 1年后过期
);
```

## 七、对象存储设计（MinIO）

### 7.1 存储桶设计

```yaml
buckets:
  # 1. 视频数据桶
  - name: furnace-videos
    policy: private
    lifecycle:
      - rule: compress_old_videos
        days: 30
        action: transition
        storage_class: GLACIER
    versioning: enabled
    
  # 2. 模型文件桶
  - name: model-artifacts
    policy: private
    versioning: enabled
    replication:
      target: backup-cluster
      
  # 3. 文档资料桶
  - name: documents
    policy: read-only
    versioning: enabled
    
  # 4. 数据集桶
  - name: datasets
    policy: private
    lifecycle:
      - rule: delete_temp_datasets
        prefix: temp/
        days: 7
        action: delete
        
  # 5. 报告输出桶
  - name: reports
    policy: read-only
    versioning: enabled
```

### 7.2 文件组织结构

```
minio://
├── furnace-videos/
│   ├── {year}/{month}/{day}/
│   │   ├── {heat_no}/
│   │   │   ├── full_process.mp4
│   │   │   ├── highlights/
│   │   │   │   ├── splash_event_001.mp4
│   │   │   │   └── endpoint_sampling.mp4
│   │   │   └── metadata.json
│   └── index/
│       └── video_index_{date}.json
│
├── model-artifacts/
│   ├── steel-llm/
│   │   ├── v1.0/
│   │   │   ├── model.bin
│   │   │   ├── config.json
│   │   │   └── tokenizer/
│   │   └── checkpoints/
│   └── edge-models/
│       ├── carbon-predictor/
│       └── flame-analyzer/
│
├── documents/
│   ├── standards/
│   │   ├── GB/
│   │   └── enterprise/
│   ├── manuals/
│   │   ├── operation/
│   │   └── maintenance/
│   └── papers/
│
├── datasets/
│   ├── raw/
│   │   ├── public/
│   │   ├── industry/
│   │   └── enterprise/
│   ├── processed/
│   │   ├── sft/
│   │   ├── rlhf/
│   │   └── cot/
│   └── temp/
│
└── reports/
    ├── daily/
    ├── analysis/
    └── quality/
```

### 7.3 元数据管理

```json
// 视频元数据示例
{
  "object_name": "furnace-videos/2024/01/01/************/full_process.mp4",
  "metadata": {
    "heat_no": "************",
    "furnace_id": "BOF-01",
    "duration": 1200,
    "fps": 25,
    "resolution": "1920x1080",
    "start_time": "2024-01-01T10:00:00Z",
    "end_time": "2024-01-01T10:20:00Z",
    "size_bytes": 524288000,
    "checksum": "md5:1234567890abcdef",
    "tags": ["full_process", "normal_heat"],
    "events": [
      {
        "type": "charging",
        "timestamp": "00:00:30"
      },
      {
        "type": "blowing_start",
        "timestamp": "00:02:00"
      }
    ]
  }
}
```

## 八、向量数据库设计（Milvus）

### 8.1 集合设计

```python
# 1. 文档向量集合
document_embeddings = {
    "collection_name": "steel_documents",
    "schema": {
        "fields": [
            {"name": "doc_id", "type": "VARCHAR", "max_length": 128, "is_primary": True},
            {"name": "doc_type", "type": "VARCHAR", "max_length": 50},
            {"name": "title", "type": "VARCHAR", "max_length": 256},
            {"name": "content", "type": "VARCHAR", "max_length": 65535},
            {"name": "embedding", "type": "FLOAT_VECTOR", "dim": 768},
            {"name": "metadata", "type": "JSON"}
        ]
    },
    "index": {
        "field": "embedding",
        "index_type": "IVF_FLAT",
        "metric_type": "IP",  # Inner Product
        "params": {"nlist": 1024}
    }
}

# 2. 炉况特征向量集合
furnace_state_embeddings = {
    "collection_name": "furnace_states",
    "schema": {
        "fields": [
            {"name": "state_id", "type": "VARCHAR", "max_length": 128, "is_primary": True},
            {"name": "heat_no", "type": "VARCHAR", "max_length": 50},
            {"name": "timestamp", "type": "INT64"},
            {"name": "state_vector", "type": "FLOAT_VECTOR", "dim": 128},
            {"name": "outcome", "type": "JSON"}  # 终点结果
        ]
    },
    "index": {
        "field": "state_vector",
        "index_type": "HNSW",
        "metric_type": "L2",
        "params": {"M": 16, "efConstruction": 200}
    }
}

# 3. 异常模式向量集合
anomaly_patterns = {
    "collection_name": "anomaly_patterns",
    "schema": {
        "fields": [
            {"name": "pattern_id", "type": "VARCHAR", "max_length": 128, "is_primary": True},
            {"name": "anomaly_type", "type": "VARCHAR", "max_length": 50},
            {"name": "pattern_vector", "type": "FLOAT_VECTOR", "dim": 256},
            {"name": "severity", "type": "INT32"},
            {"name": "description", "type": "VARCHAR", "max_length": 1024}
        ]
    }
}
```

### 8.2 向量生成策略

```python
# 炉况特征向量生成
def generate_furnace_state_vector(state_data):
    """
    将炉况数据转换为特征向量
    包含：温度、成分、氧枪参数、烟气数据等
    """
    features = []
    
    # 标准化数值特征
    features.extend([
        normalize(state_data['temperature'], 1400, 1800),
        normalize(state_data['carbon_content'], 0, 5),
        normalize(state_data['oxygen_flow'], 0, 30000),
        normalize(state_data['lance_position'], 0, 3000),
        # ... 更多特征
    ])
    
    # 时序特征编码
    time_features = encode_time_series(state_data['history'])
    features.extend(time_features)
    
    # 类别特征编码
    categorical_features = encode_categorical(state_data['steel_grade'])
    features.extend(categorical_features)
    
    return np.array(features, dtype=np.float32)
```

## 九、知识图谱设计（Neo4j）

### 9.1 节点设计

```cypher
// 1. 钢种节点
CREATE (s:SteelGrade {
  name: 'HRB400E',
  category: '螺纹钢',
  standard: 'GB/T 1499.2-2018',
  carbon_range: [0.22, 0.25],
  strength: 400,
  properties: {
    yield_strength: 400,
    tensile_strength: 540,
    elongation: 16
  }
})

// 2. 工艺参数节点
CREATE (p:ProcessParameter {
  name: 'endpoint_carbon',
  type: 'target',
  unit: '%',
  typical_range: [0.03, 0.06],
  affects: ['strength', 'hardness']
})

// 3. 设备节点
CREATE (e:Equipment {
  id: 'BOF-01',
  type: 'converter',
  capacity: 120,
  manufacturer: 'CISDI'
})

// 4. 操作节点
CREATE (o:Operation {
  name: 'lance_pattern_change',
  type: 'control',
  description: '改变氧枪模式'
})

// 5. 异常节点
CREATE (a:Anomaly {
  type: 'splash',
  severity: 'high',
  frequency: 'common'
})

// 6. 原因节点
CREATE (c:Cause {
  name: 'over_oxidized_slag',
  category: 'chemical',
  description: '炉渣过氧化'
})
```

### 9.2 关系设计

```cypher
// 1. 钢种需要的工艺参数
MATCH (s:SteelGrade {name: 'HRB400E'})
MATCH (p:ProcessParameter {name: 'endpoint_carbon'})
CREATE (s)-[:REQUIRES {
  target: 0.045,
  tolerance: 0.005,
  priority: 'high'
}]->(p)

// 2. 操作影响参数
MATCH (o:Operation {name: 'increase_oxygen_flow'})
MATCH (p:ProcessParameter {name: 'decarburization_rate'})
CREATE (o)-[:AFFECTS {
  effect: 'increase',
  magnitude: 'high',
  delay: 30  // 秒
}]->(p)

// 3. 异常的因果关系
MATCH (c:Cause {name: 'over_oxidized_slag'})
MATCH (a:Anomaly {type: 'splash'})
CREATE (c)-[:CAUSES {
  probability: 0.75,
  conditions: ['high_oxygen_flow', 'low_carbon']
}]->(a)

// 4. 异常的处理方法
MATCH (a:Anomaly {type: 'splash'})
MATCH (o:Operation {name: 'raise_lance'})
CREATE (a)-[:HANDLED_BY {
  effectiveness: 0.85,
  response_time: 'immediate'
}]->(o)

// 5. 设备与工艺的关系
MATCH (e:Equipment {id: 'BOF-01'})
MATCH (p:ProcessParameter)
CREATE (e)-[:CONTROLS {
  min_value: 0,
  max_value: 3000,
  precision: 10
}]->(p)
```

### 9.3 查询示例

```cypher
// 1. 查询导致喷溅的所有原因链
MATCH path = (c:Cause)-[:CAUSES*1..3]->(a:Anomaly {type: 'splash'})
RETURN path

// 2. 查询处理特定异常的操作序列
MATCH (a:Anomaly {type: 'splash'})
MATCH path = (a)-[:HANDLED_BY]->(o:Operation)
RETURN o.name, o.description
ORDER BY o.priority DESC

// 3. 查询影响终点碳的所有因素
MATCH (n)-[r:AFFECTS]->(p:ProcessParameter {name: 'endpoint_carbon'})
RETURN n, r.effect, r.magnitude

// 4. 推荐相似炉次的操作策略
MATCH (h1:Heat {heat_no: $current_heat})
MATCH (h2:Heat)-[:SIMILAR_TO]->(h1)
MATCH (h2)-[:USED_STRATEGY]->(s:Strategy)
WHERE s.quality_score > 0.9
RETURN s
ORDER BY s.quality_score DESC
LIMIT 5
```

## 十、全文搜索设计（Elasticsearch）

### 10.1 索引设计

```json
// 1. 操作日志索引
PUT /operation_logs
{
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "steel_analyzer": {
          "type": "custom",
          "tokenizer": "ik_max_word",
          "filter": ["lowercase", "steel_synonym"]
        }
      },
      "filter": {
        "steel_synonym": {
          "type": "synonym",
          "synonyms": [
            "拉碳,终点控制",
            "喷溅,喷料",
            "返干,炉渣返干"
          ]
        }
      }
    }
  },
  "mappings": {
    "properties": {
      "heat_no": {"type": "keyword"},
      "timestamp": {"type": "date"},
      "operator": {
        "properties": {
          "id": {"type": "keyword"},
          "name": {"type": "keyword"}
        }
      },
      "description": {
        "type": "text",
        "analyzer": "steel_analyzer"
      },
      "tags": {"type": "keyword"},
      "metrics": {
        "properties": {
          "temperature": {"type": "float"},
          "carbon": {"type": "float"}
        }
      }
    }
  }
}

// 2. 知识库索引
PUT /knowledge_base
{
  "mappings": {
    "properties": {
      "title": {
        "type": "text",
        "analyzer": "ik_max_word",
        "fields": {
          "keyword": {"type": "keyword"}
        }
      },
      "content": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "category": {"type": "keyword"},
      "tags": {"type": "keyword"},
      "author": {"type": "keyword"},
      "created_date": {"type": "date"},
      "attachments": {
        "type": "nested",
        "properties": {
          "filename": {"type": "keyword"},
          "content": {"type": "text"}
        }
      }
    }
  }
}

// 3. 异常事件索引
PUT /abnormal_events
{
  "mappings": {
    "properties": {
      "event_id": {"type": "keyword"},
      "heat_no": {"type": "keyword"},
      "event_type": {"type": "keyword"},
      "severity": {"type": "keyword"},
      "timestamp": {"type": "date"},
      "description": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "root_cause": {
        "type": "text",
        "analyzer": "ik_max_word"
      },
      "timeline": {
        "type": "nested",
        "properties": {
          "time": {"type": "date"},
          "action": {"type": "text"},
          "result": {"type": "text"}
        }
      }
    }
  }
}
```

### 10.2 搜索模板

```json
// 1. 相似问题搜索模板
POST _scripts/similar_issue_search
{
  "script": {
    "lang": "mustache",
    "source": {
      "query": {
        "bool": {
          "must": [
            {
              "more_like_this": {
                "fields": ["description", "root_cause"],
                "like": "{{issue_description}}",
                "min_term_freq": 1,
                "min_doc_freq": 1
              }
            }
          ],
          "filter": [
            {
              "range": {
                "timestamp": {
                  "gte": "{{start_date}}",
                  "lte": "{{end_date}}"
                }
              }
            }
          ]
        }
      },
      "aggs": {
        "by_type": {
          "terms": {
            "field": "event_type"
          }
        }
      }
    }
  }
}

// 2. 专家知识搜索模板
POST _scripts/expert_knowledge_search
{
  "script": {
    "lang": "mustache",
    "source": {
      "query": {
        "bool": {
          "should": [
            {
              "match": {
                "title": {
                  "query": "{{keyword}}",
                  "boost": 2
                }
              }
            },
            {
              "match": {
                "content": "{{keyword}}"
              }
            }
          ],
          "filter": [
            {
              "term": {
                "category": "{{category}}"
              }
            }
          ]
        }
      },
      "highlight": {
        "fields": {
          "content": {
            "fragment_size": 150,
            "number_of_fragments": 3
          }
        }
      }
    }
  }
}
```

## 十一、数据同步与集成

### 11.1 数据流转架构

```yaml
data_pipelines:
  # 1. 实时数据流
  realtime_pipeline:
    source: PLC/MES/Video
    processing:
      - step: data_collection
        component: Edge Gateway
        output: Raw Data Stream
      
      - step: data_cleaning
        component: Stream Processor
        rules:
          - remove_outliers
          - interpolate_missing
          - normalize_values
      
      - step: data_routing
        routes:
          - condition: high_frequency
            destination: InfluxDB
          - condition: business_data
            destination: PostgreSQL
          - condition: logs
            destination: MongoDB
          
  # 2. 批处理数据流
  batch_pipeline:
    schedule: "0 2 * * *"  # 每天凌晨2点
    source: PostgreSQL
    processing:
      - step: data_aggregation
        component: Spark Job
      - step: feature_extraction
        component: ML Pipeline
      - step: vector_generation
        component: Embedding Service
    destination: Milvus
    
  # 3. 文档处理流
  document_pipeline:
    source: MinIO
    processing:
      - step: text_extraction
        component: Document Parser
      - step: indexing
        component: Elasticsearch Indexer
      - step: embedding
        component: Text Encoder
    destinations:
      - Elasticsearch
      - Milvus
```

### 11.2 数据同步策略

```python
# CDC (Change Data Capture) 配置
cdc_config = {
    "postgres_to_elasticsearch": {
        "source": {
            "type": "postgres",
            "connection": "postgresql://...",
            "tables": ["heats", "quality_judgments"],
            "slot_name": "elastic_sync"
        },
        "transform": {
            "heats": {
                "index": "production_heats",
                "id_field": "heat_no",
                "fields_mapping": {
                    "charge_time": {"type": "date"},
                    "steel_grade": {"type": "keyword"},
                    "quality_score": {"type": "float"}
                }
            }
        },
        "sink": {
            "type": "elasticsearch",
            "connection": "http://elasticsearch:9200"
        }
    },
    
    "influxdb_to_postgres": {
        "source": {
            "type": "influxdb",
            "database": "steel_edge",
            "measurement": "process_data"
        },
        "aggregation": {
            "interval": "5m",
            "functions": ["mean", "max", "min", "stddev"]
        },
        "sink": {
            "type": "postgres",
            "table": "process_summary",
            "batch_size": 1000
        }
    }
}
```

## 十二、数据安全与备份

### 12.1 数据分级策略

```yaml
data_classification:
  # 核心生产数据 - 最高级别保护
  level_1:
    description: "核心生产工艺数据"
    examples:
      - 实时过程数据
      - 质量检测数据
      - 工艺配方
    security_measures:
      - encryption_at_rest: AES-256
      - encryption_in_transit: TLS 1.3
      - access_control: RBAC + MFA
      - audit_logging: comprehensive
      - backup: real-time replication
      
  # 业务运营数据
  level_2:
    description: "业务运营相关数据"
    examples:
      - 生产计划
      - 设备维护记录
      - 操作日志
    security_measures:
      - encryption_at_rest: AES-128
      - encryption_in_transit: TLS 1.2+
      - access_control: RBAC
      - audit_logging: standard
      - backup: daily
      
  # 公开资料
  level_3:
    description: "可公开的技术资料"
    examples:
      - 国标文档
      - 公开论文
      - 培训材料
    security_measures:
      - access_control: read-only
      - backup: weekly
```

### 12.2 备份策略

```yaml
backup_strategy:
  # PostgreSQL备份
  postgres:
    full_backup:
      frequency: daily
      time: "02:00"
      retention: 30d
      location: "s3://backup/postgres/full/"
      
    incremental:
      frequency: hourly
      method: WAL archiving
      retention: 7d
      location: "s3://backup/postgres/wal/"
      
  # MongoDB备份
  mongodb:
    full_backup:
      frequency: daily
      time: "03:00"
      method: mongodump
      compression: gzip
      retention: 30d
      
    oplog_backup:
      continuous: true
      retention: 24h
      
  # MinIO备份
  minio:
    replication:
      type: cross-region
      target: backup-datacenter
      mode: async
      
  # 灾难恢复
  disaster_recovery:
    rpo: 1h  # Recovery Point Objective
    rto: 4h  # Recovery Time Objective
    test_frequency: quarterly
```

## 十三、性能优化建议

### 13.1 查询优化

```sql
-- PostgreSQL查询优化示例

-- 1. 创建覆盖索引
CREATE INDEX idx_heats_search ON heats(
    steel_grade, 
    charge_time DESC
) INCLUDE (heat_no, quality_score, steel_weight);

-- 2. 分区表查询优化
CREATE OR REPLACE FUNCTION get_recent_heats(
    p_steel_grade VARCHAR,
    p_days INTEGER DEFAULT 7
) RETURNS TABLE(...) AS $$
BEGIN
    RETURN QUERY
    SELECT /*+ IndexScan(h idx_heats_search) */
        h.heat_no,
        h.charge_time,
        h.quality_score
    FROM heats h
    WHERE h.steel_grade = p_steel_grade
      AND h.charge_time >= CURRENT_DATE - INTERVAL '1 day' * p_days
      AND h.charge_time < CURRENT_DATE + INTERVAL '1 day'
    ORDER BY h.charge_time DESC;
END;
$$ LANGUAGE plpgsql;

-- 3. 物化视图
CREATE MATERIALIZED VIEW mv_daily_production AS
SELECT 
    DATE(charge_time) as production_date,
    steel_grade,
    COUNT(*) as heat_count,
    AVG(quality_score) as avg_quality,
    SUM(steel_weight) as total_weight
FROM heats
WHERE charge_time >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY DATE(charge_time), steel_grade
WITH DATA;

-- 自动刷新
CREATE OR REPLACE FUNCTION refresh_daily_production()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY mv_daily_production;
END;
$$ LANGUAGE plpgsql;
```

### 13.2 缓存策略

```python
# Redis缓存策略实现

class CacheStrategy:
    def __init__(self, redis_client):
        self.redis = redis_client
        
    def cache_with_loader(self, key, loader_func, ttl=300):
        """带加载器的缓存策略"""
        # 尝试从缓存获取
        cached = self.redis.get(key)
        if cached:
            return json.loads(cached)
            
        # 使用分布式锁防止缓存击穿
        lock_key = f"lock:{key}"
        if self.redis.set(lock_key, "1", nx=True, ex=10):
            try:
                # 加载数据
                data = loader_func()
                # 设置缓存
                self.redis.setex(
                    key, 
                    ttl, 
                    json.dumps(data)
                )
                return data
            finally:
                self.redis.delete(lock_key)
        else:
            # 等待其他进程加载
            time.sleep(0.1)
            return self.cache_with_loader(key, loader_func, ttl)
            
    def cache_pattern_invalidation(self, pattern):
        """基于模式的缓存失效"""
        cursor = 0
        while True:
            cursor, keys = self.redis.scan(
                cursor, 
                match=pattern, 
                count=100
            )
            if keys:
                self.redis.delete(*keys)
            if cursor == 0:
                break
```

## 十四、监控与维护

### 14.1 监控指标

```yaml
monitoring_metrics:
  # 数据库性能指标
  database:
    postgres:
      - connections_active
      - queries_per_second
      - average_query_time
      - replication_lag
      - disk_usage_percent
      
    mongodb:
      - operations_per_second
      - connection_pool_size
      - replication_lag
      - document_count
      
    influxdb:
      - writes_per_second
      - query_duration
      - series_cardinality
      - disk_usage
      
  # 数据质量指标
  data_quality:
    - missing_data_rate
    - anomaly_detection_rate
    - data_freshness
    - schema_validation_errors
    
  # 业务指标
  business:
    - daily_heat_count
    - average_quality_score
    - system_availability
    - api_response_time
```

### 14.2 维护计划

```yaml
maintenance_schedule:
  daily:
    - vacuum_analyze_postgres
    - backup_verification
    - log_rotation
    - cache_cleanup
    
  weekly:
    - index_optimization
    - statistics_update
    - performance_report
    - storage_cleanup
    
  monthly:
    - data_archival
    - security_audit
    - capacity_planning
    - schema_review
    
  quarterly:
    - disaster_recovery_drill
    - performance_tuning
    - data_retention_review
    - upgrade_planning
```

## 十五、数据治理

### 15.1 数据标准

```yaml
data_standards:
  naming_conventions:
    tables:
      format: "{domain}_{entity}[_{qualifier}]"
      examples:
        - production_heats
        - quality_inspections
        - equipment_maintenance
        
    columns:
      format: "{attribute}[_{unit}]"
      examples:
        - temperature_celsius
        - weight_tons
        - created_at
        
  data_types:
    temperature: DECIMAL(6,1)  # -999.9 to 9999.9
    percentage: DECIMAL(5,2)   # 0.00 to 999.99
    weight: DECIMAL(10,2)      # tons
    timestamp: TIMESTAMP WITH TIME ZONE
    
  value_constraints:
    temperature:
      min: 0
      max: 2000
    carbon_content:
      min: 0
      max: 5
    quality_score:
      min: 0
      max: 100
```

### 15.2 数据血缘

```mermaid
graph LR
    A[PLC数据] --> B[InfluxDB]
    B --> C[PostgreSQL汇总表]
    C --> D[数据仓库]
    D --> E[BI报表]
    
    F[MES数据] --> G[PostgreSQL业务表]
    G --> D
    
    H[视频数据] --> I[MinIO]
    I --> J[特征提取]
    J --> K[Milvus向量库]
    
    G --> L[Elasticsearch]
    C --> L
    
    D --> M[机器学习训练]
    K --> M
    M --> N[模型部署]
```

这份数据库设计文档详细规划了钢铁智能体系统的数据存储架构，涵盖了：

1. **多种数据库协同**：针对不同数据类型选择合适的存储方案
2. **结构化数据**：使用PostgreSQL+TimescaleDB处理业务和时序数据
3. **非结构化数据**：使用MinIO存储视频、文档等大文件
4. **半结构化数据**：使用MongoDB存储灵活的文档数据
5. **特殊用途数据库**：向量数据库、图数据库、搜索引擎等
6. **数据同步与集成**：完整的数据流转和同步策略
7. **安全与备份**：分级安全策略和完善的备份方案
8. **性能优化**：查询优化、缓存策略等
9. **监控与维护**：全面的监控指标和维护计划

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"id": "1", "content": "\u8bbe\u8ba1\u6574\u4f53\u9879\u76ee\u7ed3\u6784\u548c\u76ee\u5f55\u67b6\u6784", "status": "completed", "priority": "high"}, {"id": "2", "content": "\u8bbe\u8ba1\u4e91\u7aef\u94a2\u94c1\u5927\u6a21\u578b\u670d\u52a1\u67b6\u6784", "status": "completed", "priority": "high"}, {"id": "3", "content": "\u8bbe\u8ba1\u8fb9\u7f18\u5c42\u5b9e\u65f6\u6a21\u578b\u670d\u52a1\u67b6\u6784", "status": "pending", "priority": "high"}, {"id": "4", "content": "\u8bbe\u8ba1\u7aef\u4fa7\u6570\u636e\u91c7\u96c6\u4e0e\u4ea4\u4e92\u67b6\u6784", "status": "pending", "priority": "high"}, {"id": "5", "content": "\u8bbe\u8ba1\u4e09\u5927\u667a\u80fd\u4f53\u7684\u8be6\u7ec6\u67b6\u6784", "status": "pending", "priority": "high"}, {"id": "6", "content": "\u8bbe\u8ba1\u6570\u636e\u96c6\u6784\u5efa\u4e0e\u7ba1\u7406\u67b6\u6784", "status": "pending", "priority": "medium"}, {"id": "7", "content": "\u521b\u5efa\u9879\u76ee\u57fa\u7840\u76ee\u5f55\u7ed3\u6784", "status": "completed", "priority": "medium"}, {"id": "8", "content": "\u8bbe\u8ba1\u6570\u636e\u5e93\u67b6\u6784\u6587\u6863", "status": "completed", "priority": "high"}]