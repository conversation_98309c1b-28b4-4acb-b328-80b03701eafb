"""
WebSocketStreamingCallbackHandler 测试文件
测试WebSocket流式回调处理器的各种功能
"""

import asyncio
import json
from unittest.mock import AsyncMock, MagicMock, patch
from langchain.schema import LLMResult, Generation
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from src.backend.api.websocket.decision import WebSocketStreamingCallbackHandler


class TestWebSocketStreamingCallbackHandler:
    """WebSocketStreamingCallbackHandler测试类"""
    
    def create_mock_connection_manager(self):
        """创建mock的ConnectionManager"""
        manager = AsyncMock()
        manager.send_json = AsyncMock()
        return manager
    
    def create_callback_handler(self, mock_connection_manager):
        """创建回调处理器实例"""
        client_id = "test_client_123"
        return WebSocketStreaming<PERSON>allbackHandler(mock_connection_manager, client_id)
    
    async def test_initialization(self, callback_handler):
        """测试回调处理器初始化"""
        assert callback_handler.client_id == "test_client_123"
        assert callback_handler.stream_buffer == ""
        assert callback_handler.connection_manager is not None
    
    async def test_on_llm_start(self, callback_handler, mock_connection_manager):
        """测试LLM开始时的回调"""
        serialized = {"test": "data"}
        prompts = ["测试提示词"]
        
        await callback_handler.on_llm_start(serialized, prompts)
        
        # 验证发送了正确的消息
        mock_connection_manager.send_json.assert_called_once_with(
            "test_client_123",
            {
                "type": "stream_start",
                "agent": "decision",
                "message": "开始生成响应..."
            }
        )
    
    async def test_on_llm_new_token(self, callback_handler, mock_connection_manager):
        """测试接收新token时的回调"""
        token = "测试"
        
        await callback_handler.on_llm_new_token(token)
        
        # 验证缓冲区更新
        assert callback_handler.stream_buffer == "测试"
        
        # 验证发送了正确的消息
        mock_connection_manager.send_json.assert_called_once_with(
            "test_client_123",
            {
                "type": "stream",
                "agent": "decision",
                "content": "测试",
                "is_finished": False
            }
        )
    
    async def test_multiple_tokens(self, callback_handler, mock_connection_manager):
        """测试接收多个token的情况"""
        tokens = ["你好", "，", "世界", "！"]
        
        for token in tokens:
            await callback_handler.on_llm_new_token(token)
        
        # 验证缓冲区包含所有token
        assert callback_handler.stream_buffer == "你好，世界！"
        
        # 验证每个token都被发送
        assert mock_connection_manager.send_json.call_count == 4
        
        # 验证最后一次调用的参数
        last_call = mock_connection_manager.send_json.call_args_list[-1]
        assert last_call[0][1]["content"] == "！"
    
    async def test_on_llm_end(self, callback_handler, mock_connection_manager):
        """测试LLM结束时的回调"""
        # 先添加一些内容到缓冲区
        callback_handler.stream_buffer = "完整的响应内容"
        
        # 创建mock的LLMResult
        generation = Generation(text="完整的响应内容")
        llm_result = LLMResult(generations=[[generation]])
        
        await callback_handler.on_llm_end(llm_result)
        
        # 验证发送了正确的结束消息
        mock_connection_manager.send_json.assert_called_once_with(
            "test_client_123",
            {
                "type": "stream_end",
                "agent": "decision",
                "content": "",
                "is_finished": True,
                "total_content": "完整的响应内容"
            }
        )
        
        # 验证缓冲区被清空
        assert callback_handler.stream_buffer == ""
    
    async def test_on_llm_error(self, callback_handler, mock_connection_manager):
        """测试LLM出错时的回调"""
        error = Exception("测试错误")
        
        with patch('src.backend.api.websocket.decision.logger') as mock_logger:
            await callback_handler.on_llm_error(error)
            
            # 验证错误被记录
            mock_logger.error.assert_called_once_with("LLM error: 测试错误")
        
        # 验证发送了错误消息
        mock_connection_manager.send_json.assert_called_once_with(
            "test_client_123",
            {
                "type": "error",
                "agent": "decision",
                "message": "生成响应时出错: 测试错误"
            }
        )
    
    async def test_complete_workflow(self, callback_handler, mock_connection_manager):
        """测试完整的工作流程"""
        # 1. LLM开始
        await callback_handler.on_llm_start({}, ["测试提示"])
        
        # 2. 接收多个token
        tokens = ["智能", "炼钢", "系统", "正在", "为您", "分析", "数据"]
        for token in tokens:
            await callback_handler.on_llm_new_token(token)
        
        # 3. LLM结束
        generation = Generation(text="智能炼钢系统正在为您分析数据")
        llm_result = LLMResult(generations=[[generation]])
        await callback_handler.on_llm_end(llm_result)
        
        # 验证调用次数：1次开始 + 7次token + 1次结束 = 9次
        assert mock_connection_manager.send_json.call_count == 9
        
        # 验证第一次调用（开始）
        first_call = mock_connection_manager.send_json.call_args_list[0]
        assert first_call[0][1]["type"] == "stream_start"
        
        # 验证中间调用（token）
        token_calls = mock_connection_manager.send_json.call_args_list[1:8]
        for i, call in enumerate(token_calls):
            assert call[0][1]["type"] == "stream"
            assert call[0][1]["content"] == tokens[i]
            assert call[0][1]["is_finished"] is False
        
        # 验证最后一次调用（结束）
        last_call = mock_connection_manager.send_json.call_args_list[-1]
        assert last_call[0][1]["type"] == "stream_end"
        assert last_call[0][1]["is_finished"] is True
        assert last_call[0][1]["total_content"] == "智能炼钢系统正在为您分析数据"
    
    async def test_connection_manager_failure(self, mock_connection_manager):
        """测试连接管理器发送失败的情况"""
        # 设置连接管理器发送失败
        mock_connection_manager.send_json.side_effect = Exception("连接失败")
        
        callback_handler = WebSocketStreamingCallbackHandler(mock_connection_manager, "test_client")
        
        # 测试是否能正确处理异常（不应该崩溃）
        try:
            await callback_handler.on_llm_start({}, ["测试"])
        except Exception as e:
            # 如果有异常，应该是连接失败的异常
            assert "连接失败" in str(e)
    
    async def test_empty_token(self, callback_handler, mock_connection_manager):
        """测试空token的处理"""
        await callback_handler.on_llm_new_token("")
        
        # 即使是空token也应该被发送
        mock_connection_manager.send_json.assert_called_once_with(
            "test_client_123",
            {
                "type": "stream",
                "agent": "decision",
                "content": "",
                "is_finished": False
            }
        )
        
        # 缓冲区应该还是空的
        assert callback_handler.stream_buffer == ""
    
    async def test_unicode_tokens(self, callback_handler, mock_connection_manager):
        """测试Unicode字符token的处理"""
        unicode_tokens = ["🔥", "钢铁", "🏭", "智能", "💡"]
        
        for token in unicode_tokens:
            await callback_handler.on_llm_new_token(token)
        
        # 验证Unicode字符正确处理
        assert callback_handler.stream_buffer == "🔥钢铁🏭智能💡"
        assert mock_connection_manager.send_json.call_count == 5


def test_sync_runner():
    """同步测试运行器，用于手动运行测试"""
    async def run_all_tests():
        import importlib
        
        # 重新导入模块以确保最新代码
        if 'src.backend.api.websocket.decision' in sys.modules:
            importlib.reload(sys.modules['src.backend.api.websocket.decision'])
        
        test_instance = TestWebSocketStreamingCallbackHandler()
        
        print("🧪 开始测试 WebSocketStreamingCallbackHandler...")
        
        # 创建mock和callback handler
        mock_manager = test_instance.create_mock_connection_manager()
        callback_handler = test_instance.create_callback_handler(mock_manager)
        
        # 测试初始化
        print("✅ 测试初始化...")
        await test_instance.test_initialization(callback_handler)
        
        # 测试LLM开始
        print("✅ 测试LLM开始回调...")
        await test_instance.test_on_llm_start(callback_handler, mock_manager)
        mock_manager.send_json.reset_mock()
        
        # 测试新token
        print("✅ 测试新token回调...")
        callback_handler.stream_buffer = ""  # 重置缓冲区
        await test_instance.test_on_llm_new_token(callback_handler, mock_manager)
        mock_manager.send_json.reset_mock()
        
        # 测试多个token
        print("✅ 测试多个token...")
        callback_handler.stream_buffer = ""  # 重置缓冲区
        await test_instance.test_multiple_tokens(callback_handler, mock_manager)
        mock_manager.send_json.reset_mock()
        
        # 测试LLM结束
        print("✅ 测试LLM结束回调...")
        await test_instance.test_on_llm_end(callback_handler, mock_manager)
        mock_manager.send_json.reset_mock()
        
        # 测试错误处理
        print("✅ 测试错误处理...")
        await test_instance.test_on_llm_error(callback_handler, mock_manager)
        mock_manager.send_json.reset_mock()
        
        # 测试完整工作流程
        print("✅ 测试完整工作流程...")
        callback_handler.stream_buffer = ""  # 重置缓冲区
        await test_instance.test_complete_workflow(callback_handler, mock_manager)
        mock_manager.send_json.reset_mock()
        
        # 测试连接失败
        print("✅ 测试连接管理器失败...")
        mock_manager_fail = test_instance.create_mock_connection_manager()
        await test_instance.test_connection_manager_failure(mock_manager_fail)
        
        # 测试空token
        print("✅ 测试空token...")
        callback_handler.stream_buffer = ""  # 重置缓冲区
        await test_instance.test_empty_token(callback_handler, mock_manager)
        mock_manager.send_json.reset_mock()
        
        # 测试Unicode字符
        print("✅ 测试Unicode字符...")
        callback_handler.stream_buffer = ""  # 重置缓冲区
        await test_instance.test_unicode_tokens(callback_handler, mock_manager)
        
        print("🎉 所有测试通过！WebSocketStreamingCallbackHandler 工作正常。")
        print("📋 测试摘要：")
        print("   - 初始化测试：✅")
        print("   - LLM开始回调：✅")
        print("   - Token流式处理：✅")
        print("   - 多token累积：✅")
        print("   - LLM结束回调：✅")
        print("   - 错误处理：✅")
        print("   - 完整工作流程：✅")
        print("   - 连接失败处理：✅")
        print("   - 空token处理：✅")
        print("   - Unicode字符处理：✅")
    
    # 运行异步测试
    asyncio.run(run_all_tests())


if __name__ == "__main__":
    print("运行 WebSocketStreamingCallbackHandler 测试...")
    test_sync_runner()
