# Demo 示例

本目录包含各种演示示例，展示系统的不同功能。

## 目录结构

- `websocket/` - WebSocket交互演示
  - `client.py` - WebSocket客户端
  - `monitoring.py` - KPI监控测试工具
  - `run_demo.sh` - Linux/Mac启动脚本
  - `run_demo.bat` - Windows启动脚本

## 快速开始

### WebSocket交互演示

展示智能体的双向交互功能：

```bash
cd websocket
./run_demo.sh  # Linux/Mac
# 或
run_demo.bat   # Windows
```

详细说明请参见 `websocket/README.md`

## 添加新的Demo

在此目录下创建新的子目录，并包含：
- 演示代码
- README.md 说明文档
- 必要的启动脚本