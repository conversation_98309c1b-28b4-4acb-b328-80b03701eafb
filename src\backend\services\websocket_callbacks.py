from typing import Dict, Any, List, Optional
from langchain.callbacks.base import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain.schema import <PERSON><PERSON><PERSON>ult
from config.logging import logger
from .websocket_manager import ConnectionManager


class WebSocketStreamingCallbackHandler(AsyncCallbackHandler):
    """通用WebSocket流式回调处理器，用于实时发送LLM响应"""
    
    def __init__(
        self, 
        connection_manager: ConnectionManager, 
        client_id: str,
        agent_type: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        初始化回调处理器
        
        Args:
            connection_manager: WebSocket连接管理器
            client_id: 客户端ID
            agent_type: 智能体类型（如 decision, vision, blowing, analytics）
            metadata: 可选的元数据，用于扩展消息内容
        """
        self.connection_manager = connection_manager
        self.client_id = client_id
        self.agent_type = agent_type
        self.metadata = metadata or {}
        self.stream_buffer = ""
    
    async def on_llm_start(
        self, serialized: Dict[str, Any], prompts: List[str], **kwargs: Any
    ) -> None:
        """LLM开始时的回调"""
        message = {
            "type": "stream_start",
            "agent": self.agent_type,
            "message": "开始生成响应...",
            **self.metadata
        }
        await self.connection_manager.send_json(self.client_id, message)
    
    async def on_llm_new_token(self, token: str, **kwargs: Any) -> None:
        """接收到新token时的回调"""
        self.stream_buffer += token
        # 实时发送每个token
        message = {
            "type": "stream",
            "agent": self.agent_type,
            "content": token,
            "is_finished": False,
            **self.metadata
        }
        await self.connection_manager.send_json(self.client_id, message)
    
    async def on_llm_end(self, response: LLMResult, **kwargs: Any) -> None:
        """LLM结束时的回调"""
        message = {
            "type": "stream_end",
            "agent": self.agent_type,
            "content": "",
            "is_finished": True,
            "total_content": self.stream_buffer,
            **self.metadata
        }
        await self.connection_manager.send_json(self.client_id, message)
        self.stream_buffer = ""  # 清空缓冲区
    
    async def on_llm_error(
        self, error: BaseException, **kwargs: Any
    ) -> None:
        """LLM出错时的回调"""
        logger.error(f"LLM error in {self.agent_type}: {error}")
        message = {
            "type": "error",
            "agent": self.agent_type,
            "message": f"生成响应时出错: {str(error)}",
            **self.metadata
        }
        await self.connection_manager.send_json(self.client_id, message)
    
    async def on_tool_start(
        self, serialized: Dict[str, Any], input_str: str, **kwargs: Any
    ) -> None:
        """工具开始执行时的回调（可选实现）"""
        tool_name = serialized.get("name", "unknown")
        message = {
            "type": "tool_start",
            "agent": self.agent_type,
            "tool": tool_name,
            "message": f"正在执行工具: {tool_name}",
            **self.metadata
        }
        await self.connection_manager.send_json(self.client_id, message)
    
    async def on_tool_end(self, output: str, **kwargs: Any) -> None:
        """工具执行结束时的回调（可选实现）"""
        message = {
            "type": "tool_end",
            "agent": self.agent_type,
            "output": output,
            **self.metadata
        }
        await self.connection_manager.send_json(self.client_id, message)
    
    async def on_tool_error(
        self, error: BaseException, **kwargs: Any
    ) -> None:
        """工具执行出错时的回调（可选实现）"""
        logger.error(f"Tool error in {self.agent_type}: {error}")
        message = {
            "type": "tool_error",
            "agent": self.agent_type,
            "message": f"工具执行出错: {str(error)}",
            **self.metadata
        }
        await self.connection_manager.send_json(self.client_id, message)