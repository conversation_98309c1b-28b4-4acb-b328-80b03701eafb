import asyncio
import json
import logging
import os
import pandas as pd
from datetime import datetime, date
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from sqlalchemy import create_engine
from database.schema import DAILY_STEEL_MAKING_SQL
from database.connection import get_engine
from .sql_tools import DataAnalysisTool
from .chains import DecisionChains

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("daily_indicator_analyzer")

# 分析结果保存目录
ANALYSIS_RESULT_DIR = "src/agents/decision_agent/daily_analysis_results"
DATA_RESULT_DIR = os.path.join(ANALYSIS_RESULT_DIR, "raw_data")
os.makedirs(ANALYSIS_RESULT_DIR, exist_ok=True)
os.makedirs(DATA_RESULT_DIR, exist_ok=True)


# 自定义JSON编码器，处理日期类型
class DateTimeEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, (datetime, date)):
            return o.isoformat()
        return super().default(o)


class DailyIndicatorAnalyzer:
    def __init__(self):
        self.engine = None
        self.analysis_tool = DataAnalysisTool()
        self.chains = DecisionChains()
        self.initialized = False

    async def initialize(self):
        """初始化数据库连接"""
        try:
            self.engine = get_engine()
            if self.engine is None:
                raise Exception("数据库引擎获取失败")
            self.initialized = True
            logger.info("数据库连接初始化成功")
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {str(e)}")
            self.initialized = False

    async def execute_fixed_sql(self) -> str:
        """执行固定SQL并返回结果"""
        if not self.initialized:
            raise Exception("Analyzer未初始化或初始化失败")
        
        if self.engine is None:
            logger.error("数据库连接为空")
            return json.dumps({
                "success": False,
                "error": "数据库连接不可用"
            }, ensure_ascii=False)

        try:
            df = pd.read_sql(DAILY_STEEL_MAKING_SQL, self.engine)
            if df.empty:
                logger.warning("SQL查询结果为空")
                return json.dumps({"success": True, "data": [], "message": "无数据"})

            # 转换日期列，确保它们是字符串类型
            for col in df.columns:
                if pd.api.types.is_datetime64_any_dtype(df[col]):
                    df[col] = df[col].dt.strftime('%Y-%m-%d')

            result = {
                "success": True,
                "row_count": len(df),
                "columns": df.columns.tolist(),
                "data": df.to_dict("records")
            }
            # 使用自定义编码器处理任何剩余的日期类型
            return json.dumps(result, ensure_ascii=False, cls=DateTimeEncoder)
        except Exception as e:
            logger.error(f"SQL执行失败: {str(e)}")
            return json.dumps({
                "success": False,
                "error": str(e)
            }, ensure_ascii=False)

    async def analyze_results(self, data: str) -> str:
        """使用大模型分析查询结果"""
        try:
            analysis = await self.analysis_tool._arun(
                query="分析今日生产指标表现，包括达标情况、异常点及可能原因",
                data=data,
                analysis_type="general"
            )
            return analysis
        except Exception as e:
            logger.error(f"结果分析失败: {str(e)}")
            return f"分析过程出错: {str(e)}"

    async def save_analysis_result(self, analysis: str, data: str):
        """保存分析结果和原始数据到文件"""
        today = date.today().strftime("%Y%m%d")
        analysis_file_path = os.path.join(ANALYSIS_RESULT_DIR, f"{today}_indicator_analysis.txt")
        data_file_path = os.path.join(DATA_RESULT_DIR, f"{today}_raw_data.json")

        try:
            # 保存分析结果
            with open(analysis_file_path, "w", encoding="utf-8") as f:
                f.write(f"分析日期: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("分析结果:\n")
                f.write(analysis)
            logger.info(f"分析结果已保存至: {analysis_file_path}")

            # 保存原始数据
            with open(data_file_path, "w", encoding="utf-8") as f:
                # 解析数据字符串为JSON对象，以便格式化输出
                data_json = json.loads(data)
                # 格式化JSON，方便阅读
                json.dump(data_json, f, ensure_ascii=False, indent=2)
            logger.info(f"原始数据已保存至: {data_file_path}")

        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")

    async def run_daily_analysis(self):
        """执行每日分析流程"""
        logger.info("开始每日指标自动分析任务")
        try:
            # 1. 执行固定SQL
            data = await self.execute_fixed_sql()
            data_json = json.loads(data)
            if not data_json.get("success", False):
                logger.error("SQL执行失败，终止分析流程")
                return

            # 2. 分析结果
            analysis = await self.analyze_results(data)

            # 3. 保存结果（包括原始数据）
            await self.save_analysis_result(analysis, data)

            logger.info("每日指标自动分析任务完成")
        except Exception as e:
            logger.error(f"每日分析任务执行失败: {str(e)}")


async def main():
    # 初始化分析器
    analyzer = DailyIndicatorAnalyzer()
    await analyzer.initialize()

    if not analyzer.initialized:
        logger.error("初始化失败，无法启动定时任务")
        return

    # 创建定时任务调度器
    scheduler = AsyncIOScheduler(timezone='Asia/Shanghai')

    # 添加每日23点执行的任务
    scheduler.add_job(
        analyzer.run_daily_analysis,
        'cron',
        hour=23,
        minute=0,
        second=0
    )

    logger.info("每日指标分析定时任务已启动，将在每日23:00执行")
    scheduler.start()
    # 保持程序运行
    try:
        while True:
            await asyncio.sleep(3600)
    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown()
        logger.info("程序已退出")


if __name__ == "__main__":
    asyncio.run(main())
