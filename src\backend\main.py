from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn
import asyncio
from src.config.logging import logger

from .api import health, management
from .api.websocket import router as websocket_router
from src.config import settings
from .services import ConnectionManager
from workflows import AgentOrchestrator


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting up application...")

    # 初始化服务
    connection_manager = ConnectionManager()
    agent_orchestrator = AgentOrchestrator(connection_manager)
    await agent_orchestrator.initialize()

    # 存储到app状态
    app.state.connection_manager = connection_manager
    app.state.agent_orchestrator = agent_orchestrator

    # 启动心跳检测
    heartbeat_task = asyncio.create_task(connection_manager.heartbeat())

    yield

    # Shutdown
    logger.info("Shutting down application...")
    heartbeat_task.cancel()
    await agent_orchestrator.cleanup()


app = FastAPI(title="钢铁大模型服务",
              description="Steel LLM Service API",
              version="1.0.0",
              lifespan=lifespan)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket路由已移至 api/websocket/router.py

# 注册路由
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(management.router)
app.include_router(websocket_router)

if __name__ == "__main__":
    uvicorn.run("backend.main:app",
                host=settings.HOST,
                port=settings.PORT,
                reload=settings.DEBUG)
