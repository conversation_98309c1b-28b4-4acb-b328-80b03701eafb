"""
经营决策智能体测试脚本
测试经营决策智能问数智能体的各种功能
"""

import asyncio
import json
import websockets
from datetime import datetime
from typing import Optional, Dict, Any, List
import aioconsole
from colorama import init, Fore, Style

# 初始化colorama以支持Windows终端颜色
init()


class DecisionAgentTestClient:
    """经营决策智能体测试客户端"""
    
    def __init__(self, url: str = "ws://localhost:8001/ws/agent"):
        self.url = url
        self.websocket: Optional[Any] = None
        self.client_id: Optional[str] = None
        self.running = False
        self.current_test_case = None
        
    async def connect(self):
        """连接到服务器"""
        print(f"{Fore.CYAN}正在连接到 {self.url}...{Style.RESET_ALL}")
        self.websocket = await websockets.connect(self.url)
        self.running = True
        print(f"{Fore.GREEN}✓ 连接成功！{Style.RESET_ALL}")
        
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            print(f"{Fore.YELLOW}已断开连接{Style.RESET_ALL}")
            
    async def send_query(self, content: str, context: Dict[str, Any] = None):
        """发送查询"""
        if not self.websocket:
            raise RuntimeError("WebSocket未连接")
        
        message = {
            "type": "query",
            "content": content,
            "context": context or {}
        }
        
        await self.websocket.send(json.dumps(message))
        print(f"\n{Fore.BLUE}[{datetime.now().strftime('%H:%M:%S')}] → 发送查询:{Style.RESET_ALL}")
        print(f"  {content}")
        
    async def send_alert(self, kpi_info: Dict[str, Any]):
        """发送KPI预警信息"""
        if not self.websocket:
            raise RuntimeError("WebSocket未连接")
            
        message = {
            "type": "action",
            "action": "analyze_now",
            "data": kpi_info
        }
        
        await self.websocket.send(json.dumps(message))
        print(f"\n{Fore.RED}[{datetime.now().strftime('%H:%M:%S')}] ⚠ 发送预警:{Style.RESET_ALL}")
        print(f"  KPI: {kpi_info.get('kpi')} | 当前值: {kpi_info.get('current_value')} | 目标值: {kpi_info.get('target_value')}")
        
    async def receive_messages(self):
        """接收消息的协程"""
        try:
            while self.running and self.websocket:
                try:
                    message = await asyncio.wait_for(self.websocket.recv(), timeout=0.1)
                    data = json.loads(message)
                    self._handle_message(data)
                except asyncio.TimeoutError:
                    continue
                except websockets.exceptions.ConnectionClosed:
                    print(f"{Fore.RED}服务器断开连接{Style.RESET_ALL}")
                    self.running = False
                    break
        except Exception as e:
            print(f"{Fore.RED}接收消息时出错: {e}{Style.RESET_ALL}")
            self.running = False
            
    def _handle_message(self, data: Dict[str, Any]):
        """处理接收到的消息"""
        msg_type = data.get("type", "unknown")
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        if msg_type == "connected":
            self.client_id = data.get("client_id")
            print(f"\n{Fore.GREEN}[{timestamp}] ✓ 已连接{Style.RESET_ALL}")
            print(f"  客户端ID: {self.client_id}")
            print(f"  {data.get('message', '')}")
            
        elif msg_type == "analysis":
            print(f"\n{Fore.MAGENTA}[{timestamp}] 📊 分析结果:{Style.RESET_ALL}")
            content = data.get("content", "")
            # 格式化输出分析结果
            self._format_analysis_output(content)
            
        elif msg_type == "error":
            print(f"\n{Fore.RED}[{timestamp}] ❌ 错误:{Style.RESET_ALL}")
            print(f"  {data.get('message', '未知错误')}")
            
        else:
            # 其他类型的消息
            print(f"\n{Fore.YELLOW}[{timestamp}] 收到消息:{Style.RESET_ALL}")
            if "content" in data:
                print(f"  {data['content']}")
            else:
                print(json.dumps(data, ensure_ascii=False, indent=2))
                
    def _format_analysis_output(self, content: str):
        """格式化分析输出"""
        lines = content.split('\n')
        for line in lines:
            if '异常' in line and '指标' in line:
                print(f"  {Fore.RED}{line}{Style.RESET_ALL}")
            elif '建议' in line:
                print(f"  {Fore.GREEN}{line}{Style.RESET_ALL}")
            elif '原因' in line:
                print(f"  {Fore.YELLOW}{line}{Style.RESET_ALL}")
            else:
                print(f"  {line}")
                
    async def run_test_cases(self):
        """运行预定义的测试用例"""
        test_cases = [
            {
                "name": "基本经营指标查询",
                "queries": [
                    "请查询昨天的吨钢毛利情况",
                    "分析最近一周的钢坯成本变化趋势",
                    "本月的成材率是多少？",
                    "查看上个月的炼钢工序加工成本"
                ]
            },
            {
                "name": "异常指标分析",
                "queries": [
                    "分析昨天钢坯成本异常的原因",
                    "为什么最近吨钢毛利下降了？",
                    "铁水温度合格率偏低的根本原因是什么？"
                ]
            },
            {
                "name": "对比分析",
                "queries": [
                    "对比本月和上月的生产成本",
                    "比较1号高炉和2号高炉的工序加工成本",
                    "分析不同班组的成材率差异"
                ]
            },
            {
                "name": "预测与建议",
                "queries": [
                    "预测下周的原料成本走势",
                    "如何降低钢坯成本？",
                    "提升吨钢毛利的建议措施有哪些？"
                ]
            }
        ]
        
        print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}开始运行测试用例{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{Fore.YELLOW}[测试组 {i}] {test_case['name']}{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}{'-'*40}{Style.RESET_ALL}")
            
            for query in test_case['queries']:
                await self.send_query(query)
                await asyncio.sleep(3)  # 等待响应
                
            print(f"\n{Fore.GREEN}✓ 测试组 {i} 完成{Style.RESET_ALL}")
            await asyncio.sleep(2)
            
    async def run_alert_test(self):
        """运行KPI预警测试"""
        print(f"\n{Fore.RED}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.RED}开始KPI预警测试{Style.RESET_ALL}")
        print(f"{Fore.RED}{'='*60}{Style.RESET_ALL}")
        
        # 模拟KPI异常
        alerts = [
            {
                "kpi": "吨钢毛利",
                "current_value": 265.0,
                "target_value": 300.0,
                "unit": "元/吨",
                "time": "2024-01-15"
            },
            {
                "kpi": "钢坯成本",
                "current_value": 2680.0,
                "target_value": 2500.0,
                "unit": "元/吨",
                "time": "2024-01-15"
            }
        ]
        
        for alert in alerts:
            await self.send_alert(alert)
            await asyncio.sleep(4)  # 等待分析结果
            
    async def interactive_mode(self):
        """交互模式"""
        print(f"\n{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}进入交互模式{Style.RESET_ALL}")
        print(f"{Fore.GREEN}输入 'help' 查看可用命令{Style.RESET_ALL}")
        print(f"{Fore.GREEN}输入 'exit' 退出程序{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
        
        while self.running:
            try:
                # 使用aioconsole获取异步输入
                user_input = await aioconsole.ainput(f"\n{Fore.CYAN}请输入查询 > {Style.RESET_ALL}")
                
                if user_input.lower() == 'exit':
                    break
                elif user_input.lower() == 'help':
                    self._show_help()
                elif user_input.lower() == 'test':
                    await self.run_test_cases()
                elif user_input.lower() == 'alert':
                    await self.run_alert_test()
                elif user_input.strip():
                    await self.send_query(user_input)
                    
            except Exception as e:
                print(f"{Fore.RED}输入处理错误: {e}{Style.RESET_ALL}")
                
    def _show_help(self):
        """显示帮助信息"""
        help_text = f"""
{Fore.YELLOW}可用命令:{Style.RESET_ALL}
  help   - 显示此帮助信息
  test   - 运行预定义测试用例
  alert  - 运行KPI预警测试
  exit   - 退出程序

{Fore.YELLOW}查询示例:{Style.RESET_ALL}
  • 查询昨天的吨钢毛利情况
  • 分析最近一周的成本变化
  • 为什么钢坯成本上升了？
  • 如何提高成材率？
  • 预测下个月的原料成本趋势
        """
        print(help_text)
        
    async def run(self):
        """运行客户端"""
        try:
            await self.connect()
            
            # 创建并发任务
            receive_task = asyncio.create_task(self.receive_messages())
            
            # 等待欢迎消息
            await asyncio.sleep(1)
            
            # 进入交互模式
            await self.interactive_mode()
            
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}程序被用户中断{Style.RESET_ALL}")
        except Exception as e:
            print(f"{Fore.RED}运行错误: {e}{Style.RESET_ALL}")
        finally:
            await self.disconnect()


async def main():
    """主函数"""
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}经营决策智能体测试客户端{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    client = DecisionAgentTestClient()
    await client.run()


if __name__ == "__main__":
    # 运行客户端
    asyncio.run(main())