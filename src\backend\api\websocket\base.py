from fastapi import WebSocket, WebSocketDisconnect
import json
import uuid
from config.logging import logger
from backend.services import ConnectionManager
from workflows import AgentOrchestrator


async def websocket_endpoint(websocket: WebSocket):
    """通用智能体WebSocket端点，支持双向交互
    
    注意：当前使用AgentOrchestrator仅作展示，实际项目中可以简化处理
    """
    client_id = str(uuid.uuid4())
    
    connection_manager: ConnectionManager = websocket.app.state.connection_manager
    agent_orchestrator: AgentOrchestrator = websocket.app.state.agent_orchestrator
    
    await connection_manager.connect(websocket, client_id)
    
    try:
        await connection_manager.send_json(client_id, {
            "type": "connected",
            "client_id": client_id,
            "message": "欢迎使用钢铁大模型智能助手！我可以帮助您进行经营决策分析、生产控制和质量分析。"
        })
        
        while True:
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                message_type = message.get("type", "query")
                
                if message_type == "query":
                    query = message.get("content", "")
                    context = message.get("context", {})
                    
                    async for response in agent_orchestrator.process_query(client_id, query, context):
                        await connection_manager.send_json(client_id, response)
                        
                elif message_type == "action":
                    action = message.get("action")
                    action_data = message.get("data", {})
                    
                    if action == "analyze_now":
                        kpi_info = action_data
                        query = f"请分析{kpi_info.get('kpi')}指标异常的原因，当前值{kpi_info.get('current_value')}，目标值{kpi_info.get('target_value')}"
                        
                        async for response in agent_orchestrator.process_query(client_id, query, {"alert_data": kpi_info}):
                            await connection_manager.send_json(client_id, response)
                            
                elif message_type == "ping":
                    await connection_manager.send_json(client_id, {"type": "pong"})
                    
            except json.JSONDecodeError:
                async for response in agent_orchestrator.process_query(client_id, data, {}):
                    await connection_manager.send_json(client_id, response)
                    
    except WebSocketDisconnect:
        logger.info(f"Client {client_id} disconnected")
        connection_manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
        connection_manager.disconnect(client_id)
        await websocket.close(code=1011, reason="Server error")