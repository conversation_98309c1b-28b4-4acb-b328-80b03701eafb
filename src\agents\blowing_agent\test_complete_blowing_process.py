#!/usr/bin/env python3
"""
完整吹炼过程测试脚本
同时测试阶段播报和实时监控功能，模拟真实的吹炼过程
"""

import asyncio
import sys
import os

# 确保能够导入项目模块
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from src.agents.blowing_agent.agent import BlowingAgent


async def test_complete_blowing_process():
    """测试完整的吹炼过程（阶段播报 + 实时监控）"""
    print("=" * 80)
    print("完整吹炼过程测试开始")
    print("模拟真实的炼钢吹炼过程，包含阶段播报和实时监控")
    print("优化功能：阶段播报查询指定时间数据，监控分析数据变化趋势")
    print("=" * 80)

    agent = BlowingAgent()

    try:
        # 初始化智能体
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 定义完整的吹炼过程
        blowing_process = {
            "start_time":
            "2025-08-13 08:00:00",
            "stages": [{
                "stage_number": 1,
                "stage_name": "准备阶段",
                "time": "2025-08-13 08:00:00",
                "description": "吹炼开始前的准备工作，检查设备和原料"
            }, {
                "stage_number": 2,
                "stage_name": "预热阶段",
                "time": "2025-08-13 08:05:00",
                "description": "炉内预热，开始低强度吹氧"
            }, {
                "stage_number": 3,
                "stage_name": "脱碳阶段",
                "time": "2025-08-13 08:10:00",
                "description": "主要脱碳反应阶段，高强度吹氧"
            }, {
                "stage_number": 4,
                "stage_name": "精炼阶段",
                "time": "2025-08-13 08:15:00",
                "description": "成分调整和精炼，控制温度和成分"
            }, {
                "stage_number": 5,
                "stage_name": "终点控制阶段",
                "time": "2025-08-13 08:20:00",
                "description": "接近终点，精确控制参数"
            }, {
                "stage_number": 6,
                "stage_name": "吹炼结束",
                "time": "2025-08-13 08:25:00",
                "description": "吹炼过程完成，准备出钢"
            }]
        }

        print(f"\n吹炼过程概览:")
        print(f"开始时间: {blowing_process['start_time']}")
        print(f"总阶段数: {len(blowing_process['stages'])}")
        print(f"预计总时长: 25分钟")

        print("\n阶段列表:")
        for stage in blowing_process['stages']:
            print(
                f"  {stage['stage_number']}. {stage['stage_name']} ({stage['time']})"
            )

        print("\n" + "=" * 80)
        print("开始模拟吹炼过程")
        print("=" * 80)

        # 第一阶段：准备阶段播报
        first_stage = blowing_process['stages'][0]
        print(
            f"\n🔥 【阶段 {first_stage['stage_number']}】{first_stage['stage_name']}"
        )
        print(f"时间: {first_stage['time']}")
        print("-" * 60)

        # 执行第一阶段播报
        stage_result = await agent.stage_broadcast_function(
            stage_info={
                "stage_number": first_stage["stage_number"],
                "stage_name": first_stage["stage_name"]
            },
            broadcast_time=first_stage["time"])

        if stage_result.get("success"):
            print("✓ 第一阶段播报成功")
            report = stage_result.get("report", {})
            broadcast_content = report.get('broadcast_content', '')
            if broadcast_content:
                print(f"\n播报内容预览: {broadcast_content[:200]}...")
        else:
            print(f"❌ 第一阶段播报失败: {stage_result.get('error')}")

        # 启动实时监控（从第二阶段开始）
        monitoring_start_time = blowing_process['stages'][1]['time']
        print(f"\n📊 启动实时监控")
        print(f"监控开始时间: {monitoring_start_time}")
        print("-" * 60)

        monitoring_result = await agent.real_time_monitoring_function(
            action="start", start_time=monitoring_start_time)

        if monitoring_result.get("success"):
            print("✓ 实时监控启动成功")
            print("监控将每30秒生成一次报告")
        else:
            print(f"❌ 实时监控启动失败: {monitoring_result.get('error')}")
            return

        # 执行后续阶段播报（第2-6阶段）
        for stage in blowing_process['stages'][1:]:
            print(f"\n🔥 【阶段 {stage['stage_number']}】{stage['stage_name']}")
            print(f"时间: {stage['time']}")
            print(f"描述: {stage['description']}")
            print("-" * 60)

            # 执行阶段播报
            stage_result = await agent.stage_broadcast_function(
                stage_info={
                    "stage_number": stage["stage_number"],
                    "stage_name": stage["stage_name"]
                },
                broadcast_time=stage["time"])

            if stage_result.get("success"):
                print(f"✓ {stage['stage_name']}播报成功")
                report = stage_result.get("report", {})

                # 显示关键信息
                if report.get('stage_type') == 'end_stage':
                    print("🏁 吹炼过程结束播报")
                else:
                    print(f"📋 阶段类型: {report.get('stage_type')}")
                    if report.get('blow_time'):
                        print(f"⏱️  吹炼时间: 第{report.get('blow_time')}分钟")
                    if report.get('material_reminder'):
                        print(f"🥄 加料提醒: {report.get('material_reminder')}")
                    if report.get('splash_info'):
                        print(f"⚠️  喷溅情况: {report.get('splash_info')}")

                # 显示播报内容预览
                broadcast_content = report.get('broadcast_content', '')
                if broadcast_content:
                    print(f"\n播报内容预览: {broadcast_content[:150]}...")

            else:
                print(
                    f"❌ {stage['stage_name']}播报失败: {stage_result.get('error')}"
                )

            # 在阶段之间等待，让监控运行
            if stage['stage_number'] < 6:  # 不是最后一个阶段
                wait_time = 45  # 等待45秒，让监控产生报告
                print(f"\n⏳ 等待 {wait_time} 秒，让实时监控运行...")
                await asyncio.sleep(wait_time)

        # 停止实时监控
        print(f"\n📊 停止实时监控")
        print("-" * 60)

        stop_result = await agent.real_time_monitoring_function(action="stop")
        if stop_result.get("success"):
            print("✓ 实时监控停止成功")
        else:
            print(f"❌ 实时监控停止失败: {stop_result.get('error')}")

        print("\n" + "=" * 80)
        print("完整吹炼过程测试完成")
        print("=" * 80)
        print("✅ 测试总结:")
        print("   - 完成了6个阶段的播报")
        print("   - 实时监控运行了约4分钟")
        print("   - 模拟了真实的炼钢吹炼过程")
        print("=" * 80)

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 确保清理资源
        try:
            await agent.real_time_monitoring_function(action="stop")
        except:
            pass
        await agent.cleanup()


async def test_quick_demo():
    """快速演示版本（缩短等待时间）"""
    print("\n" + "=" * 80)
    print("快速演示版本（缩短等待时间）")
    print("=" * 80)

    agent = BlowingAgent()

    try:
        await agent.initialize()
        print("✓ 智能体初始化成功")

        # 简化的阶段列表
        stages = [{
            "stage_number": 1,
            "stage_name": "准备阶段",
            "time": "2025-08-13 08:00:00"
        }, {
            "stage_number": 2,
            "stage_name": "脱碳阶段",
            "time": "2025-08-13 08:05:00"
        }, {
            "stage_number": 3,
            "stage_name": "吹炼结束",
            "time": "2025-08-13 08:10:00"
        }]

        print(f"\n快速演示包含 {len(stages)} 个阶段")

        # 第一阶段播报
        print(f"\n🔥 {stages[0]['stage_name']}")
        result1 = await agent.stage_broadcast_function(
            stage_info={
                "stage_number": stages[0]["stage_number"],
                "stage_name": stages[0]["stage_name"]
            },
            broadcast_time=stages[0]["time"])
        print("✓ 第一阶段播报完成" if result1.
              get("success") else f"❌ 失败: {result1.get('error')}")

        # 启动监控
        print(f"\n📊 启动监控")
        monitor_result = await agent.real_time_monitoring_function(
            "start", stages[1]["time"])
        print("✓ 监控启动" if monitor_result.
              get("success") else f"❌ 失败: {monitor_result.get('error')}")

        # 第二阶段播报
        await asyncio.sleep(2)
        print(f"\n🔥 {stages[1]['stage_name']}")
        result2 = await agent.stage_broadcast_function(
            stage_info={
                "stage_number": stages[1]["stage_number"],
                "stage_name": stages[1]["stage_name"]
            },
            broadcast_time=stages[1]["time"])
        print("✓ 第二阶段播报完成" if result2.
              get("success") else f"❌ 失败: {result2.get('error')}")

        # 等待监控运行
        print("\n⏳ 监控运行30秒...")
        await asyncio.sleep(30)

        # 结束阶段播报
        print(f"\n🔥 {stages[2]['stage_name']}")
        result3 = await agent.stage_broadcast_function(
            stage_info={
                "stage_number": stages[2]["stage_number"],
                "stage_name": stages[2]["stage_name"]
            },
            broadcast_time=stages[2]["time"])
        print("✓ 结束阶段播报完成" if result3.
              get("success") else f"❌ 失败: {result3.get('error')}")

        # 停止监控
        print(f"\n📊 停止监控")
        stop_result = await agent.real_time_monitoring_function("stop")
        print("✓ 监控停止" if stop_result.
              get("success") else f"❌ 失败: {stop_result.get('error')}")

        print("\n✅ 快速演示完成！")

    except Exception as e:
        print(f"❌ 快速演示失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            await agent.real_time_monitoring_function("stop")
        except:
            pass
        await agent.cleanup()


async def main():
    """主测试函数"""
    print("选择测试模式:")
    print("1. 完整吹炼过程测试（约5分钟）")
    print("2. 快速演示版本（约1分钟）")

    # 为了自动化测试，我们运行快速演示版本
    print("\n自动选择快速演示版本...")
    await test_quick_demo()

    print("\n如需完整测试，请手动运行 test_complete_blowing_process() 函数")


if __name__ == "__main__":
    asyncio.run(main())
