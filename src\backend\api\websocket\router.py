from fastapi import APIRouter, WebSocket
from .base import websocket_endpoint
from .blowing import blowing_websocket_endpoint
from .vision import vision_websocket_endpoint
from .analytics import analytics_websocket_endpoint
from .decision import decision_websocket_endpoint
from .speech import speech_websocket_endpoint

router = APIRouter(tags=["websocket"])

# 通用智能体端点
router.add_api_websocket_route("/ws/agent", websocket_endpoint)

# 各智能体专用端点
router.add_api_websocket_route("/ws/agent/blowing", blowing_websocket_endpoint)
router.add_api_websocket_route("/ws/agent/vision", vision_websocket_endpoint)
router.add_api_websocket_route("/ws/agent/analytics",
                               analytics_websocket_endpoint)
router.add_api_websocket_route("/ws/agent/decision",
                               decision_websocket_endpoint)

# 语音识别端点
router.add_api_websocket_route("/ws/speech", speech_websocket_endpoint)
