"""
测试 AgentOrchestrator 的 process_query 函数
"""

import asyncio
import sys
import os
from typing import Dict, Any
from pathlib import Path

import pdb

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.backend.services.agent_orchestrator import AgentOrchestrator
from src.backend.services.websocket_manager import ConnectionManager


async def test_query(orchestrator: AgentOrchestrator, query: str, test_name: str):
    """测试单个查询"""
    print(f"\n{'='*60}")
    print(f"测试: {test_name}")
    print(f"查询: {query}")
    print(f"{'='*60}")
    
    client_id = "test-client-001"
    
    try:
        # 收集所有响应
        responses = []
        async for response in orchestrator.process_query(client_id, query):
            response_type = response.get("type")
            # print(f"\n[{response_type}] ", end="")
            if response_type == "status":
                print(response.get("content"))
            elif response_type == "intent":
                print(f"意图: {response.get('intent')} (置信度: {response.get('confidence'):.2f})")
                print(f"描述: {response.get('description')}")
            elif response_type == "content":
                # pdb.set_trace()
                print(response.get("content", ""), end="", flush=True)
            elif response_type == "error":
                print(f"错误: {response.get('content')}")
            elif response_type == "done":
                print(f"完成 (会话ID: {response.get('session_id')})")
            else:
                print(f"其他响应: {response}")
                
            responses.append(response)
            
        print(f"\n\n总共收到 {len(responses)} 个响应")
        
    except Exception as e:
        print(f"\n错误: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()


async def main():
    """主测试函数"""
    print("初始化 AgentOrchestrator...")
    
    # 创建必要的依赖
    connection_manager = ConnectionManager()
    orchestrator = AgentOrchestrator(connection_manager)
    
    # 初始化
    await orchestrator.initialize()
    print("初始化完成！")
    
    # 测试不同类型的查询
    test_cases = [
        # (查询内容, 测试名称)
        ("你好", "通用对话测试"),
        ("分析本月吨钢毛利下降的原因", "经营决策测试"),
        ("当前炉况如何？", "生产控制测试"),
        ("最近的质量合格率如何？", "质量分析测试"),
        ("帮我分析一下HRB400E的市场价格趋势", "经营决策复杂查询"),
        ("如何预防喷溅？", "生产控制技术问题"),
    ]
    
    for query, test_name in test_cases:
        await test_query(orchestrator, query, test_name)
        
        # 询问是否继续
        print("\n按 Enter 继续下一个测试，输入 'q' 退出...")
        user_input = input()
        if user_input.lower() == 'q':
            break
    
    print("\n测试完成！")


async def test_handle_business_decision():
    """测试经营决策处理"""
    print("\n" + "="*60)
    print("测试经营决策处理")
    query = "分析近一个月生石灰的使用量"
    print("初始化 AgentOrchestrator...")
    
    # 创建必要的依赖
    connection_manager = ConnectionManager()
    orchestrator = AgentOrchestrator(connection_manager)
    
    # 初始化
    await orchestrator.initialize()
    print("初始化完成！")
    client_id = "test-client-002"
    async for response in orchestrator.process_query(client_id, query):
        print(f"Response: {response}")

async def test_streaming():
    """测试流式输出"""
    print("\n" + "="*60)
    print("测试直接调用 LLM 的流式输出")
    print("="*60)
    
    from src.agents.common.llms import get_llm
    
    llm = get_llm()
    query = "请简单介绍一下炼钢的基本流程"
    
    print(f"查询: {query}")
    print("\n响应: ", end="")
    
    try:
        async for chunk in llm.astream(query):
            if chunk.content:
                print(chunk.content, end="", flush=True)
    except Exception as e:
        print(f"\n错误: {type(e).__name__}: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n")


if __name__ == "__main__":
    print("开始测试 AgentOrchestrator...")
    
    # 选择测试模式
    print("\n选择测试模式:")
    print("1. 测试完整的 process_query 流程")
    print("2. 仅测试 LLM 流式输出")
    print("3. 运行所有测试")
    print("4. 测试经营决策处理")

    #choice = input("\n请输入选择 (1-4): ").strip()
    choice = "4"  # For testing purposes, we can hardcode the choice to 4

    if choice == "1":
        asyncio.run(main())
    elif choice == "2":
        asyncio.run(test_streaming())
    elif choice == "3":
        asyncio.run(test_streaming())
        asyncio.run(main())
    elif choice == "4":
        asyncio.run(test_handle_business_decision())
    else:
        print("无效的选择")