#!/bin/bash

echo "钢铁大模型智能体Demo启动脚本"
echo "=============================="

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$SCRIPT_DIR/../.."

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 检查Python环境
python --version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Python，请先安装Python 3.10+"
    exit 1
fi

# 安装依赖
echo "正在检查依赖..."
pip install poetry
poetry install

# 启动后端服务
echo "启动后端服务..."
cd src
python -m backend.main &
BACKEND_PID=$!
echo "后端服务已启动 (PID: $BACKEND_PID)"

# 等待服务启动
sleep 5

# 启动客户端
echo "启动Demo客户端..."
cd "$SCRIPT_DIR"
python client.py

# 清理
echo "正在关闭后端服务..."
kill $BACKEND_PID
echo "Demo已结束"