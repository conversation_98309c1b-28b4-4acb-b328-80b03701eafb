"""数据库测试配置和fixtures"""
import pytest
from unittest.mock import Mock, patch, MagicMock
import sqlalchemy
from sqlalchemy import create_engine, MetaData, Table, Column, String, DECIMAL, Date, Integer, Text
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session


@pytest.fixture
def mock_engine():
    """创建模拟的数据库引擎"""
    engine = Mock(spec=Engine)
    
    # 创建一个支持上下文管理器的mock连接
    mock_connection = Mock()
    mock_connection.execute.return_value.fetchone.return_value = (1,)
    
    # 创建一个mock的上下文管理器
    mock_context_manager = Mock()
    mock_context_manager.__enter__ = Mock(return_value=mock_connection)
    mock_context_manager.__exit__ = Mock(return_value=None)
    
    # 设置engine.connect返回这个上下文管理器
    engine.connect.return_value = mock_context_manager
    
    return engine


@pytest.fixture
def mock_inspector():
    """创建模拟的数据库检查器"""
    inspector = Mock()
    
    # 模拟表名列表
    inspector.get_table_names.return_value = [
        "steelmaking_indicator_info",
        "steelmaking_daily_data",
        "ironmaking_indicator_info", 
        "ironmaking_daily_data",
        "steelrolling_indicator_info",
        "steelrolling_daily_data"
    ]
    
    # 模拟各表的列信息
    def get_columns_side_effect(table_name):
        if table_name == "steelmaking_indicator_info":
            return [
                {"name": "idx_code", "type": "VARCHAR(50)"},
                {"name": "idx_name", "type": "VARCHAR(100)"},
                {"name": "idx_benchmark", "type": "VARCHAR(50)"},
                {"name": "level1", "type": "DECIMAL(10,2)"},
                {"name": "level2", "type": "DECIMAL(10,2)"},
                {"name": "abnormal_judgment_criteria", "type": "TEXT"},
                {"name": "influencing_factors", "type": "TEXT"},
                {"name": "is_key_indicator", "type": "TINYINT"}
            ]
        elif table_name == "steelmaking_daily_data":
            return [
                {"name": "time_flag", "type": "DATE"},
                {"name": "idx_code", "type": "VARCHAR(50)"},
                {"name": "idx_value", "type": "DECIMAL(10,2)"}
            ]
        elif table_name == "ironmaking_indicator_info":
            return [
                {"name": "idx_code", "type": "VARCHAR(50)"},
                {"name": "idx_name", "type": "VARCHAR(100)"},
                {"name": "unit", "type": "VARCHAR(20)"},
                {"name": "description", "type": "TEXT"}
            ]
        elif table_name == "ironmaking_daily_data":
            return [
                {"name": "time_flag", "type": "DATE"},
                {"name": "idx_code", "type": "VARCHAR(50)"},
                {"name": "idx_value", "type": "DECIMAL(10,2)"}
            ]
        elif table_name == "steelrolling_indicator_info":
            return [
                {"name": "idx_code", "type": "VARCHAR(50)"},
                {"name": "idx_name", "type": "VARCHAR(100)"},
                {"name": "unit", "type": "VARCHAR(20)"},
                {"name": "description", "type": "TEXT"}
            ]
        elif table_name == "steelrolling_daily_data":
            return [
                {"name": "time_flag", "type": "DATE"},
                {"name": "idx_code", "type": "VARCHAR(50)"},
                {"name": "idx_value", "type": "DECIMAL(10,2)"}
            ]
        return []
    
    inspector.get_columns.side_effect = get_columns_side_effect
    
    return inspector


@pytest.fixture
def mock_session():
    """创建模拟的数据库会话"""
    session = Mock(spec=Session)
    return session


@pytest.fixture
def test_db_config():
    """测试用数据库配置"""
    return {
        "DB_DIALECT": "mysql",
        "DB_DRIVER": "mysqlconnector",
        "DB_USERNAME": "test_user",
        "DB_PASSWORD": "test_password",
        "DB_HOST": "localhost",
        "DB_PORT": "3306",
        "DB_NAME": "test_db"
    }


@pytest.fixture
def mock_settings(test_db_config):
    """创建模拟的settings对象"""
    settings = Mock()
    for key, value in test_db_config.items():
        setattr(settings, key, value)
    
    settings.TARGET_TABLES = [
        "steelmaking_indicator_info",
        "steelmaking_daily_data",
        "ironmaking_indicator_info", 
        "ironmaking_daily_data",
        "steelrolling_indicator_info",
        "steelrolling_daily_data"
    ]
    
    return settings