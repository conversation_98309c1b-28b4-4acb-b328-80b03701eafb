from fastapi import APIRouter, Depends, Request
from typing import Dict, Any
from backend.services import ConnectionManager

router = APIRouter(prefix="/api", tags=["management"])


def get_connection_manager(request: Request) -> ConnectionManager:
    """获取连接管理器依赖"""
    return request.app.state.connection_manager


@router.get("/connections")
async def get_active_connections(
    connection_manager: ConnectionManager = Depends(get_connection_manager)
) -> Dict[str, Any]:
    """获取活跃的WebSocket连接"""
    return {
        "active_connections": connection_manager.get_active_connections(),
        "count": len(connection_manager.active_connections)
    }


@router.post("/broadcast")
async def broadcast_message(
    message: dict,
    connection_manager: ConnectionManager = Depends(get_connection_manager)
) -> Dict[str, Any]:
    """向所有客户端广播消息（用于测试）"""
    await connection_manager.broadcast_json(message)
    return {
        "status": "broadcasted",
        "recipients": len(connection_manager.active_connections)
    }