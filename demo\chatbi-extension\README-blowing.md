# 吹炼智能体浏览器插件

## 概述

这是一个专门为吹炼智能体设计的浏览器插件，提供直观的图形界面来控制吹炼过程的阶段播报和实时监控功能。

## 功能特性

### 🚀 **吹炼过程控制**
- **启动/停止吹炼过程**: 一键控制整个吹炼流程
- **实时监控**: 每60秒自动生成监控报告
- **状态显示**: 实时显示监控状态和当前阶段

### 📢 **阶段播报管理**
- **四个标准阶段**: 准备阶段、脱碳阶段、精炼阶段、吹炼结束
- **时间参数支持**: 可指定播报时间或使用当前时间
- **一键播报**: 点击按钮即可执行对应阶段播报

### 📊 **智能分析**
- **数据变化分析**: 分析参数变化趋势
- **专业建议**: 基于大模型的操作建议
- **实时反馈**: 即时显示分析结果

## 安装步骤

### 1. 准备文件
确保以下文件存在于 `extension` 目录中：
```
extension/
├── manifest-blowing.json      # 插件配置文件
├── content-blowing.js         # 主要功能脚本
├── styles-blowing.css         # 样式文件
├── popup-blowing.html         # 弹出页面
├── popup-blowing.js           # 弹出页面脚本
├── background.js              # 后台脚本（复用现有）
└── icons/                     # 图标文件夹（复用现有）
```

### 2. 安装插件

#### 方法1：开发者模式安装
1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含 `manifest-blowing.json` 的 `extension` 文件夹
6. 插件安装完成

#### 方法2：重命名现有文件
如果您想替换现有插件：
1. 将 `manifest.json` 重命名为 `manifest-original.json`
2. 将 `manifest-blowing.json` 重命名为 `manifest.json`
3. 将 `content.js` 重命名为 `content-original.js`
4. 将 `content-blowing.js` 重命名为 `content.js`
5. 将 `styles.css` 重命名为 `styles-original.css`
6. 将 `styles-blowing.css` 重命名为 `styles.css`
7. 重新加载插件

### 3. 启动后端服务
```bash
# 激活虚拟环境
conda activate AISteelMaking

# 启动服务器
python -m uvicorn src.backend.main:app --host 0.0.0.0 --port 9001 --reload
```

## 使用方法

### 1. 打开控制面板
- **方法1**: 点击浏览器右下角的🔥图标
- **方法2**: 点击浏览器工具栏中的插件图标，然后点击"打开控制面板"

### 2. 启动吹炼过程
1. 在控制面板中点击"🚀 启动吹炼过程"
2. 系统将自动启动实时监控
3. 状态指示器显示"🟢 监控中"

### 3. 执行阶段播报
1. 选择要播报的阶段（第1-4阶段）
2. 可选：设置播报时间（留空使用当前时间）
3. 点击对应的阶段按钮
4. 查看播报结果和分析内容

### 4. 停止吹炼过程
1. 点击"🛑 停止吹炼过程"
2. 系统将停止实时监控
3. 状态指示器显示"🔴 已停止"

## 界面说明

### 控制面板布局
```
┌─────────────────────────────────┐
│ 🔥 吹炼智能体        🟢 监控中  │ ← 标题栏和状态
├─────────────────────────────────┤
│                                 │
│        消息显示区域              │ ← 播报内容和系统消息
│                                 │
├─────────────────────────────────┤
│ 🚀 启动吹炼过程  🛑 停止吹炼过程 │ ← 过程控制按钮
├─────────────────────────────────┤
│ 阶段播报控制                     │
│ [第1阶段] [第2阶段]              │ ← 阶段播报按钮
│ [第3阶段] [结束阶段]             │
│ 播报时间: [时间选择器]           │ ← 时间参数设置
├─────────────────────────────────┤
│ 使用提示...                     │ ← 帮助信息
└─────────────────────────────────┘
```

### 消息类型说明
- **🎉 系统消息**: 连接状态、操作确认
- **📢 播报消息**: 阶段播报完成通知
- **📊 分析内容**: 大模型生成的专业分析
- **⏱️ 时间信息**: 当前吹炼时间等
- **📋 提醒信息**: 加料提醒等操作建议
- **❌ 错误消息**: 操作失败或连接错误

## 故障排除

### 常见问题

#### 1. 插件无法加载
**症状**: 插件安装后不显示或报错
**解决方案**:
- 检查 `manifest-blowing.json` 文件格式
- 确保所有必需文件都存在
- 查看浏览器控制台错误信息

#### 2. 无法连接服务器
**症状**: 显示"❌ 服务器未连接"
**解决方案**:
- 确保后端服务器正在运行
- 检查端口 9001 是否被占用
- 验证 WebSocket 连接地址

#### 3. 阶段播报失败
**症状**: 点击阶段按钮后显示错误
**解决方案**:
- 确保已启动吹炼过程
- 检查时间格式是否正确
- 查看服务器日志获取详细错误

#### 4. 实时监控不工作
**症状**: 启动后没有监控报告
**解决方案**:
- 确保数据库连接正常
- 检查智能体初始化状态
- 等待60秒查看是否有监控输出

### 调试方法

#### 查看浏览器控制台
1. 按 F12 打开开发者工具
2. 切换到 Console 标签
3. 查看 JavaScript 错误信息

#### 查看网络请求
1. 在开发者工具中切换到 Network 标签
2. 筛选 WS (WebSocket) 连接
3. 检查连接状态和消息内容

#### 查看插件日志
1. 访问 `chrome://extensions/`
2. 找到吹炼智能体插件
3. 点击"检查视图"查看后台页面日志

## 自定义配置

### 修改服务器地址
如果服务器不在本地或使用不同端口，修改以下文件：

**content-blowing.js**:
```javascript
// 第 89 行
ws = new WebSocket('ws://your-server:your-port/ws/agent/blowing');
```

**popup-blowing.js**:
```javascript
// 第 15 行
const ws = new WebSocket('ws://your-server:your-port/ws/agent/blowing');
```

### 修改监控间隔显示
虽然实际监控间隔由服务器控制（60秒），但可以修改界面显示：

**content-blowing.js**:
```javascript
// 第 140 行附近
addMessage('system', `🔄 监控间隔: ${data.monitoring_interval}`);
```

### 添加自定义阶段
可以在阶段按钮区域添加更多阶段：

**content-blowing.js** (第 45-50 行):
```html
<button class="chatbi-stage-btn" data-stage="5" data-name="自定义阶段">第5阶段</button>
```

## 技术架构

### 前端组件
- **content-blowing.js**: 主要功能逻辑
- **styles-blowing.css**: 界面样式
- **popup-blowing.html/js**: 弹出页面

### 通信协议
- **WebSocket**: 与后端实时通信
- **JSON消息**: 标准化的消息格式
- **事件驱动**: 基于消息类型的处理

### 状态管理
- **monitoringActive**: 监控状态
- **currentStage**: 当前阶段信息
- **messages**: 消息历史记录

## 更新日志

### v1.0.0 (2025-01-XX)
- ✅ 初始版本发布
- ✅ 支持阶段播报控制
- ✅ 支持实时监控管理
- ✅ 提供直观的图形界面
- ✅ 完整的错误处理机制

## 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：
1. 查看本文档的故障排除部分
2. 检查浏览器控制台和服务器日志
3. 提供详细的错误信息和操作步骤
