<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <style>
      body {
        width: 300px;
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        margin: 0;
      }

      .header {
        text-align: center;
        margin-bottom: 20px;
      }

      .header h1 {
        margin: 0;
        font-size: 18px;
        color: #ff6b35;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
      }

      .header p {
        margin: 8px 0 0 0;
        font-size: 12px;
        color: #6c757d;
      }

      .action-button {
        width: 100%;
        padding: 12px;
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        margin-bottom: 15px;
        transition: all 0.2s;
      }

      .action-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
      }

      .features {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
      }

      .features h3 {
        margin: 0 0 10px 0;
        font-size: 14px;
        color: #495057;
      }

      .feature-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .feature-list li {
        font-size: 12px;
        color: #6c757d;
        margin-bottom: 6px;
        padding-left: 16px;
        position: relative;
      }

      .feature-list li:before {
        content: "🔥";
        position: absolute;
        left: 0;
      }

      .status {
        text-align: center;
        font-size: 12px;
        color: #6c757d;
        padding: 10px;
        background: #e9ecef;
        border-radius: 6px;
      }

      .status.connected {
        background: #d4edda;
        color: #155724;
      }

      .status.disconnected {
        background: #f8d7da;
        color: #721c24;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>🔥 吹炼智能体</h1>
      <p>智能炼钢过程控制助手</p>
    </div>

    <button id="toggle-chat" class="action-button">打开控制面板</button>

    <div class="features">
      <h3>主要功能</h3>
      <ul class="feature-list">
        <li>实时监控管理</li>
        <li>阶段自动判断</li>
        <li>参数状态分析</li>
        <li>风险提示建议</li>
      </ul>
    </div>

    <div class="features">
      <h3>使用说明</h3>
      <ul class="feature-list">
        <li>点击"启动吹炼过程"开始监控</li>
        <li>系统自动判断吹炼阶段</li>
        <li>监控每60秒自动运行</li>
        <li>实时分析参数和风险</li>
      </ul>
    </div>

    <div id="connection-status" class="status">检查连接状态...</div>

    <script src="popup-blowing.js"></script>
  </body>
</html>
