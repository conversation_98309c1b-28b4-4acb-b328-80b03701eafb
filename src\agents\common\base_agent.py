from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type
from datetime import datetime
from pydantic import BaseModel, Field
from langchain.agents import AgentExecutor
from langchain.memory import ConversationBufferWindowMemory
from langchain.callbacks.base import BaseCallbackHandler
from langchain.schema import AgentAction, AgentFinish
from config.logging import logger
import asyncio
from uuid import uuid4


class AgentState(BaseModel):
    """智能体状态"""
    agent_id: str = Field(default_factory=lambda: str(uuid4()))
    agent_type: str
    status: str = "idle"  # idle, thinking, executing, error
    current_task: Optional[Dict[str, Any]] = None
    memory_summary: Optional[str] = None
    last_action_time: Optional[datetime] = None
    metrics: Dict[str, float] = Field(default_factory=dict)


class AgentContext(BaseModel):
    """智能体上下文"""
    heat_no: Optional[str] = None
    furnace_id: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    user_id: Optional[str] = None
    session_id: str = Field(default_factory=lambda: str(uuid4()))
    metadata: Dict[str, Any] = Field(default_factory=dict)


class AgentCallbackHandler(BaseCallbackHandler):
    """智能体回调处理器"""

    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.start_time: Optional[datetime] = None

    def on_agent_action(self, action: AgentAction, **kwargs: Any) -> Any:
        """智能体执行动作时的回调"""
        logger.info(f"Agent {self.agent_id} executing action: {action.tool}")

    def on_agent_finish(self, finish: AgentFinish, **kwargs: Any) -> Any:
        """智能体完成时的回调"""
        logger.info(
            f"Agent {self.agent_id} finished with output: {finish.return_values}"
        )

    def on_tool_start(self, serialized: Dict[str, Any], input_str: str,
                      **kwargs: Any) -> Any:
        """工具开始执行时的回调"""
        self.start_time = datetime.utcnow()
        logger.debug(f"Tool {serialized.get('name')} started")

    def on_tool_end(self, output: str, **kwargs: Any) -> Any:
        """工具执行结束时的回调"""
        if self.start_time:
            duration = (datetime.utcnow() - self.start_time).total_seconds()
            logger.debug(f"Tool execution took {duration:.2f}s")


class BaseAgent(ABC):
    """智能体基类"""

    def __init__(self,
                 agent_type: str,
                 memory_window: int = 10,
                 verbose: bool = False):
        self.agent_type = agent_type
        self.state = AgentState(agent_type=agent_type)
        self.memory = ConversationBufferWindowMemory(k=memory_window,
                                                     return_messages=True,
                                                     memory_key="chat_history")
        self.verbose = verbose
        self.callback_handler = AgentCallbackHandler(self.state.agent_id)
        self.tools: List[Any] = []
        self.agent_executor = None

    @abstractmethod
    async def initialize(self):
        """初始化智能体"""
        pass

    async def cleanup(self) -> None:
        """清理资源（可选实现）"""
        pass

    @abstractmethod
    async def perceive(self, data: Dict[str, Any],
                       context: AgentContext) -> Dict[str, Any]:
        """感知环境 - 处理输入数据"""
        pass

    @abstractmethod
    async def plan(self, perception: Dict[str, Any],
                   context: AgentContext) -> Dict[str, Any]:
        """制定计划 - 基于感知结果制定行动计划"""
        pass

    @abstractmethod
    async def execute(self, plan: Dict[str, Any],
                      context: AgentContext) -> Dict[str, Any]:
        """执行计划 - 执行具体动作"""
        pass

    async def learn(self, feedback: Dict[str, Any],
                    context: AgentContext) -> None:
        """学习反馈 - 根据执行结果进行学习（可选）"""
        # 默认实现：记录到内存
        self.memory.save_context({"feedback": str(feedback)},
                                 {"response": "Feedback recorded"})

    async def run(self,
                  input_data: Dict[str, Any],
                  context: Optional[AgentContext] = None) -> Dict[str, Any]:
        """运行智能体的完整流程"""
        if context is None:
            context = AgentContext()

        self.state.status = "thinking"
        self.state.current_task = input_data

        try:
            # 感知
            perception = await self.perceive(input_data, context)
            logger.info(f"Agent {self.agent_type} perception completed")

            # 规划
            self.state.status = "planning"
            plan = await self.plan(perception, context)
            logger.info(f"Agent {self.agent_type} planning completed")

            # 执行
            self.state.status = "executing"
            result = await self.execute(plan, context)
            logger.info(f"Agent {self.agent_type} execution completed")

            # 更新状态
            self.state.status = "idle"
            self.state.last_action_time = datetime.utcnow()
            self.state.current_task = None

            # 保存到内存
            self.memory.save_context({"input": str(input_data)},
                                     {"output": str(result)})

            return {
                "success": True,
                "perception": perception,
                "plan": plan,
                "result": result,
                "agent_id": self.state.agent_id,
                "context": context.dict()
            }

        except Exception as e:
            logger.error(f"Agent {self.agent_type} error: {str(e)}")
            self.state.status = "error"
            return {
                "success": False,
                "error": str(e),
                "agent_id": self.state.agent_id,
                "context": context.dict()
            }

    def get_state(self) -> AgentState:
        """获取智能体状态"""
        return self.state

    def get_memory_summary(self) -> str:
        """获取记忆摘要"""
        messages = self.memory.chat_memory.messages
        if not messages:
            return "No memory available"

        # 简单实现：返回最近的几条消息
        recent_messages = messages[-5:]
        summary = "\n".join(
            [f"{msg.type}: {msg.content}" for msg in recent_messages])
        return summary

    async def reset(self):
        """重置智能体状态"""
        self.state = AgentState(agent_type=self.agent_type)
        self.memory.clear()
        logger.info(f"Agent {self.agent_type} has been reset")

    def add_tool(self, tool):
        """添加工具"""
        self.tools.append(tool)

    def get_tools(self):
        """获取所有工具"""
        return self.tools

    async def async_invoke(self, *args, **kwargs):
        """异步调用智能体执行器"""
        if self.agent_executor:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self.agent_executor.invoke,
                                              *args, **kwargs)
